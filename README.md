# EZMind AI - Learn 10x Faster with AI

🚀 **Production Ready** - AI-powered study companion that transforms any content into interactive learning materials.

## Features

### 📚 Content Processing
- **File Upload**: PDF, DOCX, TXT, and image files
- **YouTube Integration**: Extract transcripts from YouTube videos
- **Website Content**: Process any website URL
- **Audio Transcription**: Convert speech to text

### 🤖 AI-Powered Learning Tools
- **Smart Notes**: AI-generated comprehensive study notes
- **Interactive Quizzes**: Personalized quiz generation
- **Flashcards**: Spaced repetition learning cards
- **Summaries**: Concise content summaries
- **Math Expert**: Advanced mathematical problem solving

### 💎 Premium Features
- **Unlimited AI Questions**: No limits on AI interactions
- **Unlimited File Uploads**: Process as many files as needed
- **Live Recording**: Real-time audio transcription
- **Multiple File Processing**: Batch file processing
- **Image Analysis**: AI-powered image content extraction

## Pricing

### Free Plan
- 5 AI questions per month
- 3 file uploads
- 1 audio transcription (5 minutes)
- 5MB storage limit

### Premium Plan
- **Monthly**: $9.99/month
- **Yearly**: $7.99/month (billed annually, 20% savings)
- Unlimited everything
- All premium features included

## Technology Stack

- **Frontend**: React + TypeScript + Vite
- **UI**: Tailwind CSS + shadcn/ui
- **Backend**: Firebase Functions
- **Database**: Firestore
- **Authentication**: Firebase Auth
- **Payments**: Stripe
- **AI**: Google Gemini API
- **Hosting**: Firebase Hosting

## Production Configuration

### Environment Variables
```bash
# Firebase
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id

# Stripe (Live Keys)
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Price IDs
VITE_STRIPE_MONTHLY_PRICE_ID=price_...
VITE_STRIPE_YEARLY_PRICE_ID=price_...

# AI Services
VITE_GEMINI_API_KEY=your_gemini_api_key
```

### Deployment

1. **Install Dependencies**
   ```bash
   npm install
   cd functions && npm install
   ```

2. **Build for Production**
   ```bash
   npm run build
   ```

3. **Deploy Firebase Functions**
   ```bash
   firebase deploy --only functions
   ```

4. **Deploy Frontend**
   ```bash
   firebase deploy --only hosting
   ```

### Webhook Configuration

- **Webhook URL**: `https://ezmindai.com/api/webhook`
- **Events**: `checkout.session.completed`, `invoice.payment_succeeded`, `customer.subscription.updated`, `customer.subscription.deleted`

## Security Features

- ✅ Firebase Authentication
- ✅ Firestore Security Rules
- ✅ Stripe Webhook Signature Verification
- ✅ Environment Variable Protection
- ✅ CORS Configuration
- ✅ Input Validation

## Support

For support, contact: <EMAIL>

---

**EZMind AI** - Transforming education through artificial intelligence.
