"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Brain,
  Menu,
  X,
  FileText,
  CreditCard,
  MessageSquare,
  GraduationCap,
  Mic,
  Calendar,
  Video,
  Bot,
  Upload,
  Youtube,
  Type,
  ArrowRight,
  CheckCircle,
  Play,
  Sparkles,
  Zap,
  Shield,
} from "lucide-react"

export default function LandingPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const features = [
    {
      icon: FileText,
      title: "Notes AI",
      description: "Generate comprehensive study notes from any content automatically",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: CreditCard,
      title: "Flashcards AI",
      description: "Create interactive flashcards that adapt to your learning pace",
      color: "from-purple-500 to-pink-500",
    },
    {
      icon: MessageSquare,
      title: "Quizzes AI",
      description: "Generate practice quizzes tailored to your study materials",
      color: "from-green-500 to-emerald-500",
    },
    {
      icon: GraduationCap,
      title: "Essay Grader",
      description: "Get instant feedback and improvement suggestions on your writing",
      color: "from-orange-500 to-red-500",
    },
    {
      icon: Mic,
      title: "Audio Recap",
      description: "Transform your notes into engaging podcast-style audio content",
      color: "from-indigo-500 to-purple-500",
    },
    {
      icon: Calendar,
      title: "Study Calendar",
      description: "AI-powered scheduling that optimizes your learning timeline",
      color: "from-teal-500 to-blue-500",
    },
    {
      icon: Video,
      title: "Explainer Videos",
      description: "Generate educational videos that break down complex concepts",
      color: "from-pink-500 to-rose-500",
    },
    {
      icon: Bot,
      title: "AI Tutor",
      description: "Chat with your personal AI tutor for instant help and guidance",
      color: "from-violet-500 to-purple-500",
    },
  ]

  const uploadMethods = [
    {
      icon: Upload,
      title: "Upload Documents",
      description: "PDF, DOCX, PPTX files",
      formats: "Supports all major document formats",
      gradient: "from-blue-600 to-purple-600",
    },
    {
      icon: Mic,
      title: "Record Lectures",
      description: "Live recording capability",
      formats: "Real-time audio processing",
      gradient: "from-purple-600 to-pink-600",
    },
    {
      icon: Youtube,
      title: "Video Content",
      description: "MP4, YouTube links",
      formats: "Extract knowledge from videos",
      gradient: "from-pink-600 to-red-600",
    },
    {
      icon: Type,
      title: "Text Input",
      description: "Plain text content",
      formats: "Paste or type directly",
      gradient: "from-green-600 to-blue-600",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header
        className={`fixed top-0 w-full z-50 transition-all duration-500 ${
          isScrolled
            ? "bg-white/80 backdrop-blur-xl shadow-lg shadow-black/5 border-b border-gray-200/50"
            : "bg-transparent"
        }`}
      >
        <div className="container mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2.5 h-2.5 text-white" />
                </div>
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-blue-900 bg-clip-text text-transparent">
                EZMind AI
              </span>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-12">
              <a
                href="#features"
                className="text-gray-700 hover:text-purple-600 transition-all duration-300 font-medium"
              >
                Features
              </a>
              <a href="#upload" className="text-gray-700 hover:text-purple-600 transition-all duration-300 font-medium">
                Get Started
              </a>
              <a
                href="#pricing"
                className="text-gray-700 hover:text-purple-600 transition-all duration-300 font-medium"
              >
                Pricing
              </a>
              <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300">
                Get Started
              </Button>
            </nav>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden p-3 rounded-2xl bg-gray-100 hover:bg-gray-200 transition-colors duration-300"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-3xl mt-4 p-6 shadow-xl">
              <nav className="flex flex-col space-y-6">
                <a href="#features" className="text-gray-700 hover:text-purple-600 transition-colors font-medium">
                  Features
                </a>
                <a href="#upload" className="text-gray-700 hover:text-purple-600 transition-colors font-medium">
                  Get Started
                </a>
                <a href="#pricing" className="text-gray-700 hover:text-purple-600 transition-colors font-medium">
                  Pricing
                </a>
                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-2xl font-semibold">
                  Get Started
                </Button>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative pt-32 pb-24 overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-blue-50 to-cyan-50"></div>
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse"></div>
          <div className="absolute top-0 right-1/4 w-96 h-96 bg-blue-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse delay-1000"></div>
          <div className="absolute bottom-0 left-1/3 w-96 h-96 bg-cyan-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse delay-2000"></div>
        </div>

        <div className="relative container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-5xl mx-auto">
            {/* Badge */}
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-xl border border-purple-200/50 rounded-full px-6 py-3 mb-8 shadow-lg shadow-purple-500/10">
              <Zap className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-semibold text-purple-600">AI-Powered Learning Revolution</span>
              <Sparkles className="w-4 h-4 text-purple-600" />
            </div>

            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              Learn{" "}
              <span className="relative">
                <span className="bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-500 bg-clip-text text-transparent">
                  10x Faster
                </span>
                <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-500 rounded-full"></div>
              </span>
              <br />
              with EZMind AI
            </h1>

            <p className="text-xl sm:text-2xl text-gray-600 mb-12 leading-relaxed max-w-3xl mx-auto">
              Transform any study material into personalized learning experiences with our cutting-edge AI platform.
              From intelligent notes to adaptive quizzes, we make learning effortless and incredibly effective.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-10 py-4 text-lg font-semibold rounded-2xl shadow-xl shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300 hover:-translate-y-1"
              >
                Start Learning Now
                <ArrowRight className="ml-3 w-5 h-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 font-semibold px-10 py-4 text-lg rounded-2xl hover:border-purple-300 hover:text-purple-600 transition-all duration-300"
              >
                <Play className="mr-3 w-5 h-5" />
                Watch Demo
              </Button>
            </div>

            {/* Interactive Demo Features */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <Card className="border-2 border-purple-200 bg-white/80 backdrop-blur-sm shadow-xl">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-900">Custom Flashcards</h3>
                  </div>
                  <p className="text-gray-600 mb-4">Generate exactly the number of flashcards you need</p>
                  <div className="flex items-center space-x-3">
                    <label className="text-sm font-medium text-gray-700">Quantity:</label>
                    <select className="border border-gray-300 rounded-lg px-3 py-1 text-sm">
                      <option>10 flashcards</option>
                      <option>20 flashcards</option>
                      <option>50 flashcards</option>
                      <option>Custom amount</option>
                    </select>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 border-blue-200 bg-white/80 backdrop-blur-sm shadow-xl">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center">
                      <MessageSquare className="w-5 h-5 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-900">Custom Quizzes</h3>
                  </div>
                  <p className="text-gray-600 mb-4">Create quizzes with your preferred question count</p>
                  <div className="flex items-center space-x-3">
                    <label className="text-sm font-medium text-gray-700">Questions:</label>
                    <select className="border border-gray-300 rounded-lg px-3 py-1 text-sm">
                      <option>5 questions</option>
                      <option>10 questions</option>
                      <option>15 questions</option>
                      <option>Custom amount</option>
                    </select>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-white">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-purple-100 rounded-full px-6 py-3 mb-6">
              <Sparkles className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-semibold text-purple-600">Powerful Features</span>
            </div>
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">AI Tools for Every Learning Style</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our comprehensive suite of AI-powered tools adapts to your unique learning needs, making studying more
              efficient and engaging than ever before.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 border-0 shadow-lg bg-white/80 backdrop-blur-sm overflow-hidden"
              >
                <CardContent className="p-8 text-center relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="relative z-10">
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-500 shadow-lg`}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Upload Options Section */}
      <section id="upload" className="py-24 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-blue-100 rounded-full px-6 py-3 mb-6">
              <Upload className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-semibold text-blue-600">Get Started</span>
            </div>
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">Multiple Ways to Begin</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Choose the method that works best for you. Our AI can process any type of content and transform it into
              personalized learning materials instantly.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {uploadMethods.map((method, index) => (
              <Card
                key={index}
                className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 cursor-pointer border-0 shadow-lg bg-white overflow-hidden"
              >
                <CardContent className="p-8 text-center relative">
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${method.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                  ></div>
                  <div className="relative z-10">
                    <div
                      className={`w-20 h-20 bg-gradient-to-r ${method.gradient} rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-500 shadow-xl`}
                    >
                      <method.icon className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{method.title}</h3>
                    <p className="text-gray-600 mb-4">{method.description}</p>
                    <Badge variant="secondary" className="bg-gray-100 text-gray-700 px-4 py-2 rounded-full font-medium">
                      {method.formats}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Interactive Features Demo Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-green-100 rounded-full px-6 py-3 mb-6">
              <Sparkles className="w-4 h-4 text-green-600" />
              <span className="text-sm font-semibold text-green-600">Interactive Features</span>
            </div>
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">Customize Your Learning Experience</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Configure AI-generated study materials to match your exact learning needs and preferences
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {/* Flashcard Configuration */}
            <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50 shadow-xl hover:shadow-2xl transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-3xl flex items-center justify-center shadow-lg">
                    <CreditCard className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">Smart Flashcards</h3>
                    <p className="text-gray-600">AI-generated with custom quantities</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Number of Flashcards</label>
                    <select className="w-full border-2 border-purple-200 rounded-xl px-4 py-3 text-gray-700 focus:border-purple-500 focus:outline-none transition-colors">
                      <option value="10">10 flashcards</option>
                      <option value="20">20 flashcards</option>
                      <option value="30">30 flashcards</option>
                      <option value="50">50 flashcards</option>
                      <option value="custom">Custom amount</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
                    <select className="w-full border-2 border-purple-200 rounded-xl px-4 py-3 text-gray-700 focus:border-purple-500 focus:outline-none transition-colors">
                      <option value="beginner">Beginner</option>
                      <option value="intermediate">Intermediate</option>
                      <option value="advanced">Advanced</option>
                      <option value="mixed">Mixed Levels</option>
                    </select>
                  </div>

                  <Button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white py-3 rounded-xl font-semibold shadow-lg transition-all duration-300">
                    Generate Flashcards
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quiz Configuration */}
            <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50 shadow-xl hover:shadow-2xl transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-3xl flex items-center justify-center shadow-lg">
                    <MessageSquare className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">Smart Quizzes</h3>
                    <p className="text-gray-600">Adaptive questions with scoring</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Number of Questions</label>
                    <select className="w-full border-2 border-blue-200 rounded-xl px-4 py-3 text-gray-700 focus:border-blue-500 focus:outline-none transition-colors">
                      <option value="5">5 questions</option>
                      <option value="10">10 questions</option>
                      <option value="15">15 questions</option>
                      <option value="25">25 questions</option>
                      <option value="custom">Custom amount</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Question Type</label>
                    <select className="w-full border-2 border-blue-200 rounded-xl px-4 py-3 text-gray-700 focus:border-blue-500 focus:outline-none transition-colors">
                      <option value="multiple-choice">Multiple Choice</option>
                      <option value="true-false">True/False</option>
                      <option value="short-answer">Short Answer</option>
                      <option value="mixed">Mixed Types</option>
                    </select>
                  </div>

                  <Button className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white py-3 rounded-xl font-semibold shadow-lg transition-all duration-300">
                    Create Quiz
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600"></div>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-white/10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-white/10 rounded-full filter blur-3xl"></div>

        <div className="relative container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <div className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-xl rounded-full px-6 py-3 mb-8">
              <Shield className="w-4 h-4 text-white" />
              <span className="text-sm font-semibold text-white">Trusted & Secure</span>
            </div>

            <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">Ready to Transform Your Learning?</h2>
            <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed">
              Experience AI-powered learning with customizable flashcards, quizzes, and study materials. Start creating
              personalized learning content today.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
              <Button
                size="lg"
                className="bg-white text-purple-600 hover:bg-gray-50 font-bold px-10 py-4 text-lg rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1"
              >
                Start Free Trial
                <ArrowRight className="ml-3 w-5 h-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-white/30 text-white hover:bg-white/10 font-bold px-10 py-4 text-lg rounded-2xl backdrop-blur-sm transition-all duration-300"
              >
                Schedule Demo
              </Button>
            </div>

            <div className="flex flex-wrap items-center justify-center gap-8 text-white/80">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">No credit card required</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">7-day free trial</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">Cancel anytime</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-20">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
            {/* Company Info */}
            <div className="md:col-span-1">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 rounded-2xl flex items-center justify-center">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold">EZMind AI</span>
              </div>
              <p className="text-gray-400 leading-relaxed mb-6">
                Transforming education with cutting-edge AI-powered learning tools that adapt to every student's unique
                needs and learning style.
              </p>
            </div>

            {/* Product */}
            <div>
              <h3 className="font-bold text-lg mb-6">Product</h3>
              <ul className="space-y-4 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    API
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Integrations
                  </a>
                </li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="font-bold text-lg mb-6">Company</h3>
              <ul className="space-y-4 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    About
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Careers
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Press
                  </a>
                </li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="font-bold text-lg mb-6">Support</h3>
              <ul className="space-y-4 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Contact
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Privacy
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors duration-300">
                    Terms
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2024 EZMind AI. All rights reserved. Built with ❤️ for learners worldwide.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
