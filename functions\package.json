{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0", "stripe": "^14.0.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.23", "typescript": "^5.3.0"}, "private": true}