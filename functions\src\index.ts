import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import Stripe from 'stripe';
import * as express from 'express';
import { Request, Response } from 'express';

// Initialize Firebase Admin
admin.initializeApp();

// Initialize Stripe with live secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || functions.config().stripe?.secret_key || '***********************************************************************************************************', {
  apiVersion: '2023-10-16',
});

// Create Express app for webhook
const webhookApp = express();
webhookApp.use(express.raw({ type: 'application/json' }));

// Firestore reference
const db = admin.firestore();

// Create Checkout Session
export const createCheckoutSession = functions.https.onCall(async (data: any, context: functions.https.CallableContext) => {
  try {
    // Verify user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { priceId, planType, userEmail, successUrl, cancelUrl } = data;
    
    console.log('🔄 Creating checkout session:', { priceId, planType, userEmail });

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      customer_email: userEmail,
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        userId: context.auth.uid,
        planType,
        userEmail,
      },
      subscription_data: {
        metadata: {
          userId: context.auth.uid,
          planType,
          userEmail,
        },
      },
    });

    console.log('✅ Checkout session created:', session.id);

    return {
      success: true,
      url: session.url,
      sessionId: session.id,
    };
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw new functions.https.HttpsError('internal', 'Unable to create checkout session');
  }
});

// Create Payment Intent
export const createPaymentIntent = functions.https.onCall(async (data: any, context: functions.https.CallableContext) => {
  try {
    // Verify user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { priceId, planType } = data;
    
    // Get pricing plan from Firestore
    const planDoc = await db.collection('pricing_plans').doc(planType).get();
    if (!planDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Pricing plan not found');
    }

    const planData = planDoc.data();
    const amount = planData?.price * 100; // Convert to cents

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: 'usd',
      metadata: {
        userId: context.auth.uid,
        planType,
        priceId,
      },
    });

    return {
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    throw new functions.https.HttpsError('internal', 'Unable to create payment intent');
  }
});

// Create Subscription
export const createSubscription = functions.https.onCall(async (data: any, context: functions.https.CallableContext) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { priceId, paymentMethodId } = data;
    const userId = context.auth.uid;

    // Get or create Stripe customer
    let customerId: string;
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (userDoc.exists && userDoc.data()?.stripeCustomerId) {
      customerId = userDoc.data()!.stripeCustomerId;
    } else {
      const customer = await stripe.customers.create({
        email: context.auth.token.email,
        metadata: { userId },
      });
      customerId = customer.id;
      
      // Save customer ID to Firestore
      await db.collection('users').doc(userId).set({
        stripeCustomerId: customerId,
        email: context.auth.token.email,
      }, { merge: true });
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    });

    // Set as default payment method
    await stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    // Create subscription with metadata
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_settings: {
        payment_method_types: ['card'],
        save_default_payment_method: 'on_subscription',
      },
      metadata: {
        userId: userId,
        userEmail: context.auth.token.email || '',
        plan: 'premium'
      },
      expand: ['latest_invoice.payment_intent'],
    });

    console.log('✅ Subscription created successfully:', subscription.id);

    // Save subscription to database immediately
    const subscriptionData = {
      plan: 'premium',
      status: subscription.status,
      stripeSubscriptionId: subscription.id, // Real Stripe subscription ID
      stripeCustomerId: customerId,
      currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    // Update user document with subscription
    await db.collection('users').doc(userId).update({
      subscription: subscriptionData,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Create subscription document
    await db.collection('subscriptions').doc(subscription.id).set({
      userId: userId,
      userEmail: context.auth.token.email,
      ...subscriptionData,
    });

    console.log('💾 Subscription saved to database:', subscription.id);

    return {
      subscriptionId: subscription.id, // Real Stripe subscription ID
      customerId: customerId,
      status: subscription.status,
      clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
    };
  } catch (error) {
    console.error('Error creating subscription:', error);
    throw new functions.https.HttpsError('internal', 'Unable to create subscription');
  }
});

// Get User Subscription Status
export const getUserSubscription = functions.https.onCall(async (data: any, context: functions.https.CallableContext) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const userId = context.auth.uid;
    const userDoc = await db.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      return { hasActiveSubscription: false };
    }

    // const userData = userDoc.data(); // Not needed for this function
    const subscriptionDoc = await db.collection('subscriptions')
      .where('userId', '==', userId)
      .where('status', '==', 'active')
      .limit(1)
      .get();

    if (subscriptionDoc.empty) {
      return { hasActiveSubscription: false };
    }

    const subscription = subscriptionDoc.docs[0].data();
    return {
      hasActiveSubscription: true,
      subscription,
    };
  } catch (error) {
    console.error('Error getting user subscription:', error);
    throw new functions.https.HttpsError('internal', 'Unable to get subscription status');
  }
});

// Update User Subscription in Database
export const updateUserSubscription = functions.https.onCall(async (data: any, context: functions.https.CallableContext) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { subscription, sessionId, planType } = data;
    const userId = context.auth.uid;
    const userEmail = context.auth.token.email;

    // Create/update user document
    await db.collection('users').doc(userId).set({
      email: userEmail,
      subscription: subscription,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
      stripeCustomerId: subscription.stripeCustomerId,
    }, { merge: true });

    // Create subscription document
    await db.collection('subscriptions').doc(sessionId).set({
      userId: userId,
      userEmail: userEmail,
      plan: subscription.plan,
      status: subscription.status,
      planType: planType,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      stripeCustomerId: subscription.stripeCustomerId,
      currentPeriodEnd: subscription.currentPeriodEnd,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log('✅ User subscription updated in Firestore:', userId);
    return { success: true, userId, subscription };
  } catch (error) {
    console.error('Error updating user subscription:', error);
    throw new functions.https.HttpsError('internal', 'Unable to update subscription');
  }
});

// Stripe Webhook Handler
export const stripeWebhook = functions.https.onRequest(async (req: Request, res: Response) => {
  // Set CORS headers for webhook
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'POST');
  res.set('Access-Control-Allow-Headers', 'Content-Type, stripe-signature');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    console.error('❌ Webhook: Invalid method:', req.method);
    res.status(405).send('Method Not Allowed');
    return;
  }

  // Get the signature from headers
  const sig = req.headers['stripe-signature'] as string;
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET || functions.config().stripe?.webhook_secret;

  // Log incoming webhook details for debugging
  console.log('🔍 Webhook received:', {
    method: req.method,
    hasSignature: !!sig,
    hasSecret: !!endpointSecret,
    bodyType: typeof req.body,
    bodyLength: req.body ? req.body.length : 0,
    headers: Object.keys(req.headers)
  });

  // Validate required components
  if (!sig) {
    console.error('❌ Webhook: Missing stripe-signature header');
    res.status(400).send('Missing stripe-signature header');
    return;
  }

  if (!endpointSecret) {
    console.error('❌ Webhook: Missing webhook secret configuration');
    res.status(500).send('Webhook secret not configured');
    return;
  }

  if (!req.body) {
    console.error('❌ Webhook: Missing request body');
    res.status(400).send('Missing request body');
    return;
  }

  let event;

  try {
    // Construct the event from the webhook payload
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    console.log('✅ Webhook signature verified successfully');
  } catch (err: any) {
    console.error('❌ Webhook signature verification failed:', {
      error: err.message,
      type: err.type,
      signature: sig ? 'present' : 'missing',
      secretConfigured: !!endpointSecret
    });
    res.status(400).send(`Webhook Error: ${err.message}`);
    return;
  }

  try {
    console.log(`🎯 Processing webhook event: ${event.type}`);

    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object;
        console.log('💳 Processing checkout session completed:', session.id);
        await handleCheckoutSessionCompleted(session);
        break;

      case 'invoice.payment_succeeded':
        const invoice = event.data.object;
        console.log('💰 Processing invoice payment succeeded:', invoice.id);
        await handleInvoicePaymentSucceeded(invoice);
        break;

      case 'customer.subscription.updated':
        const updatedSubscription = event.data.object;
        console.log('🔄 Processing subscription updated:', updatedSubscription.id);
        await handleSubscriptionChange(updatedSubscription);
        break;

      case 'customer.subscription.deleted':
        const deletedSubscription = event.data.object;
        console.log('❌ Processing subscription deleted:', deletedSubscription.id);
        await handleSubscriptionChange(deletedSubscription);
        break;

      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        console.log('💳 Processing payment intent succeeded:', paymentIntent.id);
        await handlePaymentIntentSucceeded(paymentIntent);
        break;

      default:
        console.log(`⚠️ Unhandled event type: ${event.type}`);
    }

    console.log('✅ Webhook processed successfully');
    res.json({ received: true, processed: true });
  } catch (error: any) {
    console.error('❌ Error processing webhook:', {
      error: error.message,
      stack: error.stack,
      eventType: event?.type,
      eventId: event?.id
    });
    res.status(500).send('Webhook processing failed');
  }
});

// Handle successful checkout session
async function handleCheckoutSessionCompleted(session: any) {
  try {
    const customerEmail = session.customer_details?.email;
    const customerId = session.customer;
    const subscriptionId = session.subscription;
    const mode = session.mode; // 'subscription' or 'payment'

    console.log('🔍 Checkout session details:', {
      sessionId: session.id,
      customerEmail,
      customerId,
      subscriptionId,
      mode,
      paymentStatus: session.payment_status
    });

    if (!customerEmail) {
      console.error('❌ No customer email in checkout session');
      return;
    }

    // Find user by email
    const userQuery = await db.collection('users').where('email', '==', customerEmail).limit(1).get();

    if (userQuery.empty) {
      console.error('❌ User not found with email:', customerEmail);
      return;
    }

    const userDoc = userQuery.docs[0];
    const userId = userDoc.id;

    // Prepare update data
    const updateData: any = {
      stripeCustomerId: customerId,
      lastPayment: admin.firestore.FieldValue.serverTimestamp(),
    };

    // If this is a subscription checkout, get subscription details
    if (subscriptionId && mode === 'subscription') {
      try {
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);
        // Assuming planType (monthly/yearly) can be derived or is stored elsewhere, e.g., metadata
        // For now, let's try to get it from the price's nickname or product metadata if available
        // This part might need adjustment based on how planType is determined from Stripe objects
        let planType = 'monthly'; // Default
        if (subscription.items.data.length > 0) {
          const price = subscription.items.data[0].price;
          if (price.nickname?.toLowerCase().includes('year')) {
            planType = 'yearly';
          } else if (price.nickname?.toLowerCase().includes('month')) {
            planType = 'monthly';
          }
          // Alternatively, check product metadata if you store it there
        }

        updateData.subscription = {
          plan: 'premium', // This 'plan' seems to be about access level (premium/free)
          status: subscription.status,
          stripeSubscriptionId: subscriptionId, // Real Stripe subscription ID
          stripeCustomerId: customerId,
          currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
          planType: planType, // Added planType here
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };
        console.log('📋 Real Stripe subscription details added:', subscriptionId, 'Plan Type:', planType);
      } catch (subError) {
        console.error('❌ Error retrieving subscription:', subError);
      }
    }

    // Update user document
    await db.collection('users').doc(userId).update(updateData);

    // Also save payment record
    await db.collection('payments').add({
      userId,
      userEmail: customerEmail,
      sessionId: session.id,
      customerId,
      subscriptionId,
      amount: session.amount_total,
      currency: session.currency,
      status: 'completed',
      mode,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log('✅ Checkout session completed for user:', userId);
  } catch (error) {
    console.error('❌ Error handling checkout session:', error);
    throw error;
  }
}

// Handle successful invoice payment
async function handleInvoicePaymentSucceeded(invoice: any) {
  try {
    const customerId = invoice.customer;
    const subscriptionId = invoice.subscription;
    const invoiceId = invoice.id;

    console.log('🔍 Invoice payment details:', {
      invoiceId,
      customerId,
      subscriptionId,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      status: invoice.status
    });

    if (!customerId) {
      console.error('❌ No customer ID in invoice');
      return;
    }

    // Find user by Stripe customer ID
    const userQuery = await db.collection('users').where('stripeCustomerId', '==', customerId).limit(1).get();

    if (userQuery.empty) {
      console.error('❌ User not found with customer ID:', customerId);
      return;
    }

    const userDoc = userQuery.docs[0];
    const userId = userDoc.id;

    // Get subscription details from Stripe if subscription exists
    let subscriptionData = null;
    if (subscriptionId) {
      try {
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);
        let planType = 'monthly'; // Default
        if (subscription.items.data.length > 0) {
          const price = subscription.items.data[0].price;
          if (price.nickname?.toLowerCase().includes('year')) {
            planType = 'yearly';
          } else if (price.nickname?.toLowerCase().includes('month')) {
            planType = 'monthly';
          }
        }
        subscriptionData = {
          plan: 'premium',
          status: subscription.status,
          stripeSubscriptionId: subscriptionId,
          stripeCustomerId: customerId,
          currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
          planType: planType, // Added planType here
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };
      } catch (subError) {
        console.error('❌ Error retrieving subscription for invoice:', subError);
      }
    }

    // Update user document
    const updateData: any = {
      lastPayment: admin.firestore.FieldValue.serverTimestamp(),
    };

    if (subscriptionData) {
      updateData.subscription = subscriptionData;
    }

    await db.collection('users').doc(userId).update(updateData);

    // Update/create subscription document if we have subscription data
    if (subscriptionData && subscriptionId) {
      await db.collection('subscriptions').doc(subscriptionId).set({
        userId: userId,
        ...subscriptionData,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      }, { merge: true });
    }

    // Save payment record
    await db.collection('payments').add({
      userId,
      invoiceId,
      customerId,
      subscriptionId,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      status: 'completed',
      type: 'invoice_payment',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log('✅ Invoice payment succeeded for user:', userId);
  } catch (error) {
    console.error('❌ Error handling invoice payment:', error);
    throw error;
  }
}

// Handle payment intent succeeded (for one-time payments)
async function handlePaymentIntentSucceeded(paymentIntent: any) {
  try {
    const customerId = paymentIntent.customer;
    const paymentIntentId = paymentIntent.id;
    const metadata = paymentIntent.metadata;

    console.log('🔍 Payment intent details:', {
      paymentIntentId,
      customerId,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      metadata
    });

    // If we have user ID in metadata, use that
    if (metadata?.userId) {
      const userId = metadata.userId;
      const planType = metadata.planType || 'monthly';

      // Update user with payment info
      await db.collection('users').doc(userId).update({
        lastPayment: admin.firestore.FieldValue.serverTimestamp(),
        stripeCustomerId: customerId,
      });

      // Save payment record
      await db.collection('payments').add({
        userId,
        paymentIntentId,
        customerId,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        planType,
        status: 'completed',
        type: 'one_time_payment',
        metadata,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      console.log('✅ Payment intent succeeded for user:', userId);
    } else if (customerId) {
      // Find user by customer ID
      const userQuery = await db.collection('users').where('stripeCustomerId', '==', customerId).limit(1).get();

      if (!userQuery.empty) {
        const userDoc = userQuery.docs[0];
        const userId = userDoc.id;

        await db.collection('users').doc(userId).update({
          lastPayment: admin.firestore.FieldValue.serverTimestamp(),
        });

        console.log('✅ Payment intent succeeded for customer:', customerId);
      }
    }
  } catch (error) {
    console.error('❌ Error handling payment intent:', error);
    throw error;
  }
}

// Handle subscription changes
async function handleSubscriptionChange(subscription: any) {
  try {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log('🔍 Subscription change details:', {
      subscriptionId,
      customerId,
      status: subscription.status,
      cancelAtPeriodEnd: subscription.cancel_at_period_end
    });

    if (!customerId) {
      console.error('❌ No customer ID in subscription');
      return;
    }

    // Find user by Stripe customer ID
    const userQuery = await db.collection('users').where('stripeCustomerId', '==', customerId).limit(1).get();

    if (userQuery.empty) {
      console.error('❌ User not found with customer ID:', customerId);
      return;
    }

    const userDoc = userQuery.docs[0];
    const userId = userDoc.id;

    let planType = 'monthly'; // Default
    if (subscription.items.data.length > 0) {
      const price = subscription.items.data[0].price;
      if (price.nickname?.toLowerCase().includes('year')) {
        planType = 'yearly';
      } else if (price.nickname?.toLowerCase().includes('month')) {
        planType = 'monthly';
      }
    }
    const subscriptionData = {
      plan: subscription.status === 'active' ? 'premium' : 'free',
      status: subscription.status,
      stripeSubscriptionId: subscription.id,
      stripeCustomerId: customerId,
      currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      planType: planType, // Added planType here
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await db.collection('users').doc(userId).update({
      subscription: subscriptionData,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Update subscription document
    await db.collection('subscriptions').doc(subscriptionId).set({
      userId: userId,
      ...subscriptionData,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    }, { merge: true });

    console.log('✅ Subscription updated for user:', userId, 'Status:', subscription.status);
  } catch (error) {
    console.error('❌ Error handling subscription change:', error);
    throw error;
  }
}
