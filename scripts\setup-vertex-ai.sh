#!/bin/bash

# Vertex AI Setup Script for Study Spark

echo "🚀 Setting up Vertex AI for Study Spark..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud CLI not found. Installing..."
    
    # Install Google Cloud CLI based on OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        echo "📦 Installing Google Cloud CLI for Linux..."
        curl https://sdk.cloud.google.com | bash
        exec -l $SHELL
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        echo "📦 Installing Google Cloud CLI for macOS..."
        if command -v brew &> /dev/null; then
            brew install google-cloud-sdk
        else
            curl https://sdk.cloud.google.com | bash
            exec -l $SHELL
        fi
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        # Windows
        echo "📦 Please install Google Cloud CLI manually from:"
        echo "https://cloud.google.com/sdk/docs/install"
        echo "Then run this script again."
        exit 1
    fi
fi

echo "✅ Google Cloud CLI found"

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "🔐 Authenticating with Google Cloud..."
    gcloud auth login
fi

echo "✅ Authentication verified"

# Set the project
echo "🏗️ Setting up project: studywise-hsli2"
gcloud config set project studywise-hsli2

# Enable required APIs
echo "🔧 Enabling Vertex AI API..."
gcloud services enable aiplatform.googleapis.com

# Set up Application Default Credentials
echo "🔑 Setting up Application Default Credentials..."
gcloud auth application-default login

echo "✅ Vertex AI setup complete!"
echo ""
echo "🎉 Your Study Spark application is now configured to use Vertex AI"
echo ""
echo "Next steps:"
echo "1. Start your development server: npm run dev"
echo "2. Test the AI features in the application"
echo "3. Check the browser console for any authentication issues"
echo ""
echo "If you encounter issues, check VERTEX_AI_SETUP.md for troubleshooting tips."
