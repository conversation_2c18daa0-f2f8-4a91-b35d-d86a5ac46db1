
import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/react";
import { initializeAnalytics } from "@/utils/analytics";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import PricingPage from "./pages/PricingPage";
import SettingsPage from "./pages/SettingsPage";
import PaymentSuccess from "./pages/PaymentSuccess";
import PaymentCancelled from "./pages/PaymentCancelled";
import NotFound from "./pages/NotFound";
import PaywallModal from "@/components/paywall/PaywallModal";
import NewUserPaywall from "@/components/paywall/NewUserPaywall";
import VertexAITest from "@/components/debug/VertexAITest";
import { OAuthTest } from "@/components/debug/OAuthTest";
import { SubscriptionTest } from "@/components/debug/SubscriptionTest";
import useSubscription from "@/hooks/useSubscription";


const queryClient = new QueryClient();

// Component that uses auth context - must be inside AuthProvider
const AppContent = () => {
  const { isPaywallOpen, paywallData, closePaywall } = useSubscription();
  const { showNewUserPaywall, dismissNewUserPaywall, user, getUserDisplayName } = useAuth();

  return (
    <>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/pricing" element={<PricingPage />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="/payment-success" element={<PaymentSuccess />} />
          <Route path="/payment-cancelled" element={<PaymentCancelled />} />
          <Route path="/debug/vertex-ai" element={<VertexAITest />} />
          <Route path="/debug/oauth" element={<OAuthTest />} />
          <Route path="/debug/subscription" element={<SubscriptionTest />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>

      {/* New User Welcome Paywall */}
      <NewUserPaywall
        isOpen={showNewUserPaywall}
        onClose={dismissNewUserPaywall}
        onContinueFree={dismissNewUserPaywall}
        userEmail={user?.email}
        userName={getUserDisplayName()}
      />

      {/* Global Paywall Modal */}
      {paywallData && (
        <PaywallModal
          isOpen={isPaywallOpen}
          onClose={closePaywall}
          feature={paywallData.feature}
          description={paywallData.description}
          requiredPlan={paywallData.requiredPlan}
        />
      )}
    </>
  );
};

const App = () => {
  // Initialize analytics
  React.useEffect(() => {
    initializeAnalytics();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </TooltipProvider>
      <Analytics />
      <SpeedInsights />
    </QueryClientProvider>
  );
};

export default App;
