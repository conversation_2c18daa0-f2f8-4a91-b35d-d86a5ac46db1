
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Zap, Copy, Download, Loader2, ChevronLeft, ChevronRight, RotateCcw } from "lucide-react";
import { generateFlashcards } from "@/services/geminiService";
import { toast } from "sonner";

interface Flashcard {
  front: string;
  back: string;
}

const FlashcardGenerator = () => {
  const [topic, setTopic] = useState("");
  const [count, setCount] = useState(10);
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [currentCard, setCurrentCard] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleGenerateFlashcards = async () => {
    if (!topic.trim()) {
      toast.error("Please enter a topic");
      return;
    }

    setIsLoading(true);
    try {
      const response = await generateFlashcards(topic, count);
      const parsedCards = parseFlashcards(response);
      setFlashcards(parsedCards);
      setCurrentCard(0);
      setIsFlipped(false);
      toast.success("Flashcards generated successfully!");
    } catch (error) {
      toast.error("Failed to generate flashcards. Please try again.");
      console.error("Error generating flashcards:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const parseFlashcards = (response: string): Flashcard[] => {
    const cards: Flashcard[] = [];
    const cardBlocks = response.split(/Card \d+:/i).filter(block => block.trim());
    
    cardBlocks.forEach(block => {
      const frontMatch = block.match(/Front:\s*(.+?)(?=Back:)/s);
      const backMatch = block.match(/Back:\s*(.+)/s);
      
      if (frontMatch && backMatch) {
        cards.push({
          front: frontMatch[1].trim(),
          back: backMatch[1].trim()
        });
      }
    });
    
    return cards;
  };

  const nextCard = () => {
    if (currentCard < flashcards.length - 1) {
      setCurrentCard(currentCard + 1);
      setIsFlipped(false);
    }
  };

  const prevCard = () => {
    if (currentCard > 0) {
      setCurrentCard(currentCard - 1);
      setIsFlipped(false);
    }
  };

  const flipCard = () => {
    setIsFlipped(!isFlipped);
  };

  const resetCards = () => {
    setCurrentCard(0);
    setIsFlipped(false);
  };

  const exportFlashcards = () => {
    const content = flashcards.map((card, index) => 
      `Card ${index + 1}:\nFront: ${card.front}\nBack: ${card.back}\n\n`
    ).join('');
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${topic.replace(/\s+/g, '_')}_flashcards.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("Flashcards exported!");
  };

  const copyFlashcards = () => {
    const content = flashcards.map((card, index) => 
      `Card ${index + 1}:\nFront: ${card.front}\nBack: ${card.back}\n\n`
    ).join('');
    
    navigator.clipboard.writeText(content);
    toast.success("Flashcards copied to clipboard!");
  };

  return (
    <div className="space-y-6">
      <style>
        {`
          .flashcard {
            perspective: 1000px;
          }
          
          .flashcard-inner {
            position: relative;
            width: 100%;
            height: 300px;
            text-align: center;
            transition: transform 0.6s;
            transform-style: preserve-3d;
          }
          
          .flashcard-inner.flipped {
            transform: rotateY(180deg);
          }
          
          .flashcard-front, .flashcard-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
          }
          
          .flashcard-back {
            transform: rotateY(180deg);
          }
        `}
      </style>

      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-t-lg">
          <div className="flex items-center space-x-3">
            <Zap className="w-6 h-6" />
            <div>
              <CardTitle className="text-xl">AI Flashcard Generator</CardTitle>
              <CardDescription className="text-orange-100">
                Create interactive flashcards for effective memorization
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid md:grid-cols-2 gap-4 mb-6">
            <div className="space-y-2">
              <Label htmlFor="topic">Topic</Label>
              <Input
                id="topic"
                placeholder="e.g., Spanish Vocabulary, Biology Terms..."
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                className="border-gray-300 focus:border-orange-500"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="count">Number of Cards</Label>
              <Input
                id="count"
                type="number"
                min={5}
                max={50}
                value={count}
                onChange={(e) => setCount(Number(e.target.value))}
                className="border-gray-300 focus:border-orange-500"
              />
            </div>
          </div>
          
          <Button 
            onClick={handleGenerateFlashcards}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating Flashcards...
              </>
            ) : (
              <>
                <Zap className="w-4 h-4 mr-2" />
                Generate Flashcards
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {flashcards.length > 0 && (
        <>
          <Card className="border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Study Mode</CardTitle>
                <CardDescription>
                  Card {currentCard + 1} of {flashcards.length}
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetCards}
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Reset
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyFlashcards}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportFlashcards}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flashcard">
                <div className={`flashcard-inner ${isFlipped ? 'flipped' : ''}`}>
                  <div className="flashcard-front bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200">
                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">Front</h3>
                      <p className="text-gray-700">{flashcards[currentCard]?.front}</p>
                    </div>
                  </div>
                  <div className="flashcard-back bg-gradient-to-br from-green-50 to-emerald-100 border-2 border-green-200">
                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">Back</h3>
                      <p className="text-gray-700">{flashcards[currentCard]?.back}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-between items-center mt-6">
                <Button
                  variant="outline"
                  onClick={prevCard}
                  disabled={currentCard === 0}
                >
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  Previous
                </Button>
                
                <Button
                  onClick={flipCard}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  {isFlipped ? 'Show Front' : 'Show Back'}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={nextCard}
                  disabled={currentCard === flashcards.length - 1}
                >
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>All Flashcards</CardTitle>
              <CardDescription>Review all generated flashcards</CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={flashcards.map((card, index) => 
                  `Card ${index + 1}:\nFront: ${card.front}\nBack: ${card.back}\n\n`
                ).join('')}
                readOnly
                className="min-h-[300px] border-gray-300 bg-gray-50"
              />
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default FlashcardGenerator;
