
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Brain, CheckCircle, XCircle, RefreshCw, Loader2 } from "lucide-react";
import { generateQuiz } from "@/services/geminiService";
import { toast } from "sonner";

interface QuizQuestion {
  question: string;
  options: string[];
  correctAnswer: string;
  explanation: string;
}

const QuizGenerator = () => {
  const [topic, setTopic] = useState("");
  const [difficulty, setDifficulty] = useState("");
  const [questionCount, setQuestionCount] = useState("5");
  const [quiz, setQuiz] = useState<QuizQuestion[]>([]);
  const [userAnswers, setUserAnswers] = useState<{[key: number]: string}>({});
  const [showResults, setShowResults] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const parseQuizContent = (content: string): QuizQuestion[] => {
    const questions: QuizQuestion[] = [];
    const questionBlocks = content.split(/Q\d+:/).slice(1);
    
    questionBlocks.forEach((block) => {
      const lines = block.trim().split('\n').filter(line => line.trim());
      if (lines.length < 6) return;
      
      const question = lines[0].trim();
      const options = lines.slice(1, 5).map(line => line.replace(/^[A-D]\)\s*/, '').trim());
      const correctAnswerLine = lines.find(line => line.includes('Correct Answer:'));
      const explanationLine = lines.find(line => line.includes('Explanation:'));
      
      if (correctAnswerLine && explanationLine) {
        const correctAnswer = correctAnswerLine.split(':')[1].trim();
        const explanation = explanationLine.split(':')[1].trim();
        
        questions.push({
          question,
          options,
          correctAnswer,
          explanation
        });
      }
    });
    
    return questions;
  };

  const handleGenerateQuiz = async () => {
    if (!topic.trim()) {
      toast.error("Please enter a topic");
      return;
    }
    if (!difficulty) {
      toast.error("Please select a difficulty level");
      return;
    }

    setIsLoading(true);
    setShowResults(false);
    setUserAnswers({});
    
    try {
      const quizContent = await generateQuiz(topic, difficulty, parseInt(questionCount));
      const parsedQuiz = parseQuizContent(quizContent);
      
      if (parsedQuiz.length === 0) {
        throw new Error("Failed to parse quiz questions");
      }
      
      setQuiz(parsedQuiz);
      toast.success("Quiz generated successfully!");
    } catch (error) {
      toast.error("Failed to generate quiz. Please try again.");
      console.error("Error generating quiz:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswerChange = (questionIndex: number, answer: string) => {
    setUserAnswers(prev => ({
      ...prev,
      [questionIndex]: answer
    }));
  };

  const submitQuiz = () => {
    if (Object.keys(userAnswers).length < quiz.length) {
      toast.error("Please answer all questions before submitting");
      return;
    }
    setShowResults(true);
    const score = quiz.reduce((acc, question, index) => {
      return acc + (userAnswers[index] === question.correctAnswer ? 1 : 0);
    }, 0);
    toast.success(`Quiz completed! Score: ${score}/${quiz.length}`);
  };

  const resetQuiz = () => {
    setUserAnswers({});
    setShowResults(false);
  };

  const getScoreColor = () => {
    const score = quiz.reduce((acc, question, index) => {
      return acc + (userAnswers[index] === question.correctAnswer ? 1 : 0);
    }, 0);
    const percentage = (score / quiz.length) * 100;
    
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-t-lg">
          <div className="flex items-center space-x-3">
            <Brain className="w-6 h-6" />
            <div>
              <CardTitle className="text-xl">Practice Quiz Generator</CardTitle>
              <CardDescription className="text-green-100">
                Test your knowledge with AI-generated quizzes
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid md:grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
              <Label htmlFor="topic">Topic</Label>
              <Input
                id="topic"
                placeholder="e.g., Biology, History, Physics..."
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                className="border-gray-300 focus:border-green-500"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="difficulty">Difficulty</Label>
              <Select value={difficulty} onValueChange={setDifficulty}>
                <SelectTrigger className="border-gray-300 focus:border-green-500">
                  <SelectValue placeholder="Select difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="easy">Easy</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="hard">Hard</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="count">Questions</Label>
              <Select value={questionCount} onValueChange={setQuestionCount}>
                <SelectTrigger className="border-gray-300 focus:border-green-500">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3">3 Questions</SelectItem>
                  <SelectItem value="5">5 Questions</SelectItem>
                  <SelectItem value="10">10 Questions</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <Button 
            onClick={handleGenerateQuiz}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating Quiz...
              </>
            ) : (
              <>
                <Brain className="w-4 h-4 mr-2" />
                Generate Quiz
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {quiz.length > 0 && (
        <div className="space-y-4">
          {showResults && (
            <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-green-50">
              <CardContent className="p-6">
                <div className="text-center">
                  <h3 className={`text-2xl font-bold ${getScoreColor()}`}>
                    Quiz Results: {quiz.reduce((acc, question, index) => {
                      return acc + (userAnswers[index] === question.correctAnswer ? 1 : 0);
                    }, 0)}/{quiz.length}
                  </h3>
                  <Button 
                    onClick={resetQuiz}
                    variant="outline"
                    className="mt-4"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Retake Quiz
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {quiz.map((question, index) => (
            <Card key={index} className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Question {index + 1}</span>
                  {showResults && (
                    <div className="flex items-center">
                      {userAnswers[index] === question.correctAnswer ? (
                        <CheckCircle className="w-6 h-6 text-green-600" />
                      ) : (
                        <XCircle className="w-6 h-6 text-red-600" />
                      )}
                    </div>
                  )}
                </CardTitle>
                <CardDescription className="text-base text-gray-700">
                  {question.question}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup
                  value={userAnswers[index] || ""}
                  onValueChange={(value) => handleAnswerChange(index, value)}
                  disabled={showResults}
                >
                  {question.options.map((option, optionIndex) => {
                    const optionLetter = String.fromCharCode(65 + optionIndex);
                    const isCorrect = optionLetter === question.correctAnswer;
                    const isSelected = userAnswers[index] === optionLetter;
                    
                    return (
                      <div 
                        key={optionIndex} 
                        className={`flex items-center space-x-2 p-3 rounded-lg transition-colors ${
                          showResults 
                            ? isCorrect 
                              ? 'bg-green-100 border border-green-300' 
                              : isSelected && !isCorrect
                                ? 'bg-red-100 border border-red-300'
                                : 'bg-gray-50'
                            : 'hover:bg-gray-50'
                        }`}
                      >
                        <RadioGroupItem value={optionLetter} id={`q${index}-${optionLetter}`} />
                        <Label 
                          htmlFor={`q${index}-${optionLetter}`}
                          className="flex-1 cursor-pointer"
                        >
                          {optionLetter}) {option}
                        </Label>
                      </div>
                    );
                  })}
                </RadioGroup>
                
                {showResults && (
                  <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm font-medium text-blue-800">Explanation:</p>
                    <p className="text-sm text-blue-700 mt-1">{question.explanation}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}

          {!showResults && (
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <Button 
                  onClick={submitQuiz}
                  className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
                >
                  Submit Quiz
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default QuizGenerator;
