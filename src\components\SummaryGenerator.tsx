
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Book, Copy, Download, Loader2 } from "lucide-react";
import { generateSummary } from "@/services/geminiService";
import { toast } from "sonner";

const SummaryGenerator = () => {
  const [inputContent, setInputContent] = useState("");
  const [summary, setSummary] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleGenerateSummary = async () => {
    if (!inputContent.trim()) {
      toast.error("Please enter content to summarize");
      return;
    }

    setIsLoading(true);
    try {
      const generatedSummary = await generateSummary(inputContent);
      setSummary(generatedSummary);
      toast.success("Summary generated successfully!");
    } catch (error) {
      toast.error("Failed to generate summary. Please try again.");
      console.error("Error generating summary:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(summary);
    toast.success("Summary copied to clipboard!");
  };

  const downloadSummary = () => {
    const blob = new Blob([summary], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ai_summary.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("Summary downloaded!");
  };

  return (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-t-lg">
          <div className="flex items-center space-x-3">
            <Book className="w-6 h-6" />
            <div>
              <CardTitle className="text-xl">AI Summary Generator</CardTitle>
              <CardDescription className="text-purple-100">
                Transform long content into concise, digestible summaries
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="content">Content to Summarize</Label>
              <Textarea
                id="content"
                placeholder="Paste your text, article, or study material here..."
                value={inputContent}
                onChange={(e) => setInputContent(e.target.value)}
                className="min-h-[200px] border-gray-300 focus:border-purple-500"
              />
              <p className="text-sm text-gray-500">
                {inputContent.length} characters
              </p>
            </div>
            
            <Button 
              onClick={handleGenerateSummary}
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating Summary...
                </>
              ) : (
                <>
                  <Book className="w-4 h-4 mr-2" />
                  Generate Summary
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {summary && (
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Generated Summary</CardTitle>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={copyToClipboard}
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={downloadSummary}
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none">
              <Textarea
                value={summary}
                readOnly
                className="min-h-[300px] border-gray-300 bg-gray-50"
              />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SummaryGenerator;
