import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Pause, 
  Download, 
  Trash2, 
  Clock, 
  Calendar,
  Volume2,
  FileAudio,
  MoreVertical
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface AudioRecording {
  id: string;
  title: string;
  blob: Blob;
  url: string;
  duration: number;
  createdAt: string;
  transcript?: string;
  size: number;
}

interface AudioLibraryProps {
  userEmail: string;
}

const AudioLibrary: React.FC<AudioLibraryProps> = ({ userEmail }) => {
  const [recordings, setRecordings] = useState<AudioRecording[]>([]);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);

  // Load recordings from localStorage on mount
  useEffect(() => {
    loadRecordings();
  }, [userEmail]);

  const loadRecordings = () => {
    try {
      const stored = localStorage.getItem(`audio-recordings-${userEmail}`);
      if (stored) {
        const recordingData = JSON.parse(stored);
        // Convert stored data back to recordings - URLs are now base64 data
        const loadedRecordings = recordingData.map((recording: any) => ({
          ...recording,
          blob: null, // We'll recreate blob when needed
          url: recording.url // This is now base64 data
        }));
        setRecordings(loadedRecordings);
      }
    } catch (error) {
      console.error('Failed to load recordings:', error);
    }
  };

  const saveRecordings = (updatedRecordings: AudioRecording[]) => {
    try {
      // Store recordings data (URLs will be preserved)
      localStorage.setItem(`audio-recordings-${userEmail}`, JSON.stringify(updatedRecordings));
      setRecordings(updatedRecordings);
    } catch (error) {
      console.error('Failed to save recordings:', error);
      toast.error('Failed to save recordings');
    }
  };

  const addRecording = async (blob: Blob, duration: number, transcript?: string) => {
    const id = `recording-${Date.now()}`;
    const title = `Recording ${new Date().toLocaleString()}`;

    try {
      // Convert blob to base64 for persistent storage
      const base64Data = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          resolve(result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });

      const newRecording: AudioRecording = {
        id,
        title,
        blob,
        url: base64Data, // Store base64 data instead of blob URL
        duration,
        createdAt: new Date().toISOString(),
        transcript,
        size: blob.size
      };

      const updatedRecordings = [newRecording, ...recordings];

      // Store recordings with base64 data
      const recordingsToStore = updatedRecordings.map(rec => ({
        ...rec,
        blob: undefined, // Don't store blob object
        url: rec.url.startsWith('data:') ? rec.url : base64Data // Ensure we have base64 data
      }));

      localStorage.setItem(`audio-recordings-${userEmail}`, JSON.stringify(recordingsToStore));
      setRecordings(updatedRecordings);

      toast.success('Recording saved to library!');
    } catch (error) {
      console.error('Failed to save recording:', error);
      toast.error('Failed to save recording');
    }
  };

  const playRecording = async (recording: AudioRecording) => {
    try {
      // Stop current audio if playing
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }

      // Use the base64 data directly (recording.url is now base64 data)
      const audio = new Audio(recording.url);
      setCurrentAudio(audio);
      setCurrentlyPlaying(recording.id);

      audio.onended = () => {
        setCurrentlyPlaying(null);
        setCurrentAudio(null);
      };

      audio.onerror = (e) => {
        console.error('Audio playback error:', e);
        toast.error('Failed to play recording - audio format may not be supported');
        setCurrentlyPlaying(null);
        setCurrentAudio(null);
      };

      audio.onloadstart = () => {
        console.log('Audio loading started...');
      };

      audio.oncanplay = () => {
        console.log('Audio can start playing');
      };

      await audio.play();
      toast.success('Playing recording...');
    } catch (error) {
      console.error('Failed to play recording:', error);
      toast.error(`Failed to play recording: ${error.message}`);
      setCurrentlyPlaying(null);
    }
  };

  const stopRecording = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentlyPlaying(null);
      setCurrentAudio(null);
    }
  };

  const downloadRecording = (recording: AudioRecording) => {
    try {
      const link = document.createElement('a');
      link.href = recording.url; // This is base64 data
      link.download = `${recording.title.replace(/[^a-z0-9]/gi, '_')}.webm`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Recording downloaded!');
    } catch (error) {
      console.error('Failed to download recording:', error);
      toast.error('Failed to download recording');
    }
  };

  const deleteRecording = (recordingId: string) => {
    if (confirm('Are you sure you want to delete this recording?')) {
      // Stop if currently playing
      if (currentlyPlaying === recordingId) {
        stopRecording();
      }

      const updatedRecordings = recordings.filter(r => r.id !== recordingId);

      // Save updated recordings to localStorage
      const recordingsToStore = updatedRecordings.map(rec => ({
        ...rec,
        blob: undefined // Don't store blob object
      }));

      localStorage.setItem(`audio-recordings-${userEmail}`, JSON.stringify(recordingsToStore));
      setRecordings(updatedRecordings);

      toast.success('Recording deleted');
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    const kb = bytes / 1024;
    const mb = kb / 1024;
    if (mb >= 1) {
      return `${mb.toFixed(1)} MB`;
    }
    return `${kb.toFixed(1)} KB`;
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Expose addRecording function to parent components
  React.useImperativeHandle(React.forwardRef(() => null), () => ({
    addRecording
  }));

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileAudio className="w-5 h-5 text-green-600" />
          Audio Library
          <Badge variant="secondary">{recordings.length} recordings</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {recordings.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FileAudio className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No recordings yet</p>
            <p className="text-sm">Your audio recordings will appear here</p>
          </div>
        ) : (
          <div className="space-y-4">
            {recordings.map((recording) => (
              <Card key={recording.id} className="border border-gray-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 mb-1">
                        {recording.title}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {formatDuration(recording.duration)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {formatDate(recording.createdAt)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Volume2 className="w-4 h-4" />
                          {formatFileSize(recording.size)}
                        </div>
                      </div>
                      {recording.transcript && (
                        <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                          {recording.transcript.substring(0, 100)}...
                        </p>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => 
                          currentlyPlaying === recording.id 
                            ? stopRecording() 
                            : playRecording(recording)
                        }
                        className="flex items-center gap-1"
                      >
                        {currentlyPlaying === recording.id ? (
                          <Pause className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                        {currentlyPlaying === recording.id ? 'Stop' : 'Play'}
                      </Button>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => downloadRecording(recording)}>
                            <Download className="w-4 h-4 mr-2" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => deleteRecording(recording.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Create a ref type for the component
export type AudioLibraryRef = {
  addRecording: (blob: Blob, duration: number, transcript?: string) => void;
};

export default AudioLibrary;
