import { useState, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mic, Square, Play, Pause, Loader2, Download } from 'lucide-react';
import { toast } from 'sonner';
import { processAudioFile } from '@/services/audioService';
import { trackAudioRecordingStart, trackAudioRecordingComplete, trackAudioTranscriptionComplete } from '@/utils/analytics';

interface AudioRecorderProps {
  onRecordingComplete: (audioBlob: Blob, transcript?: string) => void;
  onRecordingSaved?: (audioBlob: Blob, duration: number, transcript?: string) => void;
  userEmail?: string;
  className?: string;
}

const AudioRecorder = ({ onRecordingComplete, onRecordingSaved, userEmail, className }: AudioRecorderProps) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const startRecording = async () => {
    try {
      console.log('Requesting microphone access...');

      // Request microphone with specific constraints
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      });

      console.log('Microphone access granted');

      // Check MediaRecorder support
      const mimeType = MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/mp4';
      console.log('Using MIME type:', mimeType);

      const mediaRecorder = new MediaRecorder(stream, { mimeType });
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        console.log('Audio data chunk received:', event.data.size, 'bytes');
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        console.log('Recording stopped, creating blob from', chunksRef.current.length, 'chunks');
        const blob = new Blob(chunksRef.current, { type: mimeType });
        console.log('Audio blob created:', blob.size, 'bytes');

        setAudioBlob(blob);
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);

        // Stop all tracks to release the microphone
        stream.getTracks().forEach(track => track.stop());

        // Track recording completion
        trackAudioRecordingComplete(recordingTime);

        toast.success('Recording completed! Click "Use Recording" to transcribe.');
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        toast.error('Recording error occurred');
      };

      // Start recording with data collection every second
      mediaRecorder.start(1000);
      setIsRecording(true);
      setRecordingTime(0);

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      // Track recording start
      trackAudioRecordingStart();

      console.log('Recording started successfully');
      toast.success('🎤 Recording started - speak clearly!');

    } catch (error) {
      console.error('Error accessing microphone:', error);

      if (error.name === 'NotAllowedError') {
        toast.error('❌ Microphone access denied. Please allow microphone access in your browser settings and try again.');
      } else if (error.name === 'NotFoundError') {
        toast.error('❌ No microphone found. Please connect a microphone and try again.');
      } else if (error.name === 'NotSupportedError') {
        toast.error('❌ Audio recording not supported in this browser.');
      } else {
        toast.error(`❌ Failed to start recording: ${error.message}`);
      }
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  const playRecording = () => {
    if (audioUrl && audioRef.current) {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const pauseRecording = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const saveRecording = async () => {
    if (!audioBlob || !userEmail) {
      toast.error('No recording to save or user not logged in');
      return;
    }

    try {
      // Convert blob to base64 for persistent storage
      const base64Data = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          resolve(result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(audioBlob);
      });

      // Save to localStorage with user isolation
      const recordings = JSON.parse(localStorage.getItem(`audio-recordings-${userEmail}`) || '[]');
      const newRecording = {
        id: `recording-${Date.now()}`,
        title: `Recording ${new Date().toLocaleString()}`,
        url: base64Data, // Store base64 data instead of blob URL
        duration: recordingTime,
        createdAt: new Date().toISOString(),
        size: audioBlob.size
      };

      recordings.unshift(newRecording);
      localStorage.setItem(`audio-recordings-${userEmail}`, JSON.stringify(recordings));

      // Call callback if provided
      if (onRecordingSaved) {
        onRecordingSaved(audioBlob, recordingTime);
      }

      toast.success('🎵 Recording saved to your library!');
    } catch (error) {
      console.error('Failed to save recording:', error);
      toast.error('Failed to save recording');
    }
  };

  const processRecording = async () => {
    if (!audioBlob) {
      toast.error('No recording to process');
      return;
    }

    console.log('🎵 Processing audio directly with Gemini AI:', audioBlob.size, 'bytes');
    setIsProcessing(true);

    try {
      // Send directly to Gemini AI (like official Gemini website)
      const result = await sendToGeminiAI(audioBlob);

      console.log('✅ Gemini AI analysis completed');

      // Track transcription completion
      const wordCount = result.split(' ').length;
      trackAudioTranscriptionComplete(wordCount);

      toast.success('✅ Recording analyzed by AI!');

      // Call the callback with the audio blob and AI result
      onRecordingComplete(audioBlob, result);

      // Reset state
      setAudioBlob(null);
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
      setAudioUrl(null);
      setRecordingTime(0);

    } catch (error) {
      console.error('❌ Error processing recording with Gemini:', error);
      toast.error(`❌ Failed to process recording: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Send audio directly to Gemini AI (like official website)
  const sendToGeminiAI = async (audioBlob: Blob): Promise<string> => {
    const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
    if (!GEMINI_API_KEY) {
      throw new Error('Gemini API key not configured. Please add VITE_GEMINI_API_KEY to your environment variables.');
    }
    const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent";

    try {
      // Convert audio to base64
      const base64Data = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1]; // Remove data URL prefix
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(audioBlob);
      });

      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: "Please analyze this audio recording. Provide a transcription and then create comprehensive study notes, key insights, and important points from the content."
            }, {
              inline_data: {
                mime_type: "audio/webm",
                data: base64Data
              }
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 4096,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_NONE"
            }
          ]
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      if (data.candidates && data.candidates.length > 0) {
        return data.candidates[0].content.parts[0].text;
      }

      throw new Error('No response from Gemini AI');

    } catch (error) {
      console.error('❌ Gemini AI processing failed:', error);
      throw error;
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Card className={`border shadow-sm bg-gradient-to-br from-green-500 to-green-600 text-white hover:shadow-md transition-all duration-200 ${className || ''}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-white/20 rounded-lg">
            <Mic className="w-5 h-5" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-sm">Record Lecture</h3>
            <p className="text-green-100 text-xs">Record audio directly</p>
          </div>
        </div>

        {(isRecording || audioBlob) && (
          <div className="mt-3 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-xs font-mono bg-white/20 px-2 py-1 rounded">
                {formatTime(recordingTime)}
              </span>

              {isRecording && (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                  <span className="text-xs">Recording...</span>
                </div>
              )}
            </div>

            <div className="flex space-x-2">
              {isRecording ? (
                <Button
                  onClick={stopRecording}
                  size="sm"
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  <Square className="w-4 h-4 mr-1" />
                  Stop
                </Button>
              ) : audioBlob ? (
                <>
                  <Button
                    onClick={isPlaying ? pauseRecording : playRecording}
                    size="sm"
                    className="bg-white/20 hover:bg-white/30"
                  >
                    {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  </Button>
                  <Button
                    onClick={saveRecording}
                    size="sm"
                    className="bg-blue-500/80 hover:bg-blue-600/80"
                    title="Save recording to library"
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                  <Button
                    onClick={processRecording}
                    disabled={isProcessing}
                    size="sm"
                    className="bg-white/20 hover:bg-white/30"
                  >
                    {isProcessing ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-1" />
                    ) : null}
                    {isProcessing ? 'Processing...' : 'Use Recording'}
                  </Button>
                </>
              ) : (
                <Button
                  onClick={startRecording}
                  size="sm"
                  className="bg-white/20 hover:bg-white/30"
                >
                  <Mic className="w-4 h-4 mr-1" />
                  Start Recording
                </Button>
              )}
            </div>

            {audioUrl && (
              <audio
                ref={audioRef}
                src={audioUrl}
                onEnded={() => setIsPlaying(false)}
                className="hidden"
              />
            )}
          </div>
        )}

        {!isRecording && !audioBlob && (
          <div className="mt-3">
            <Button
              onClick={startRecording}
              className="w-full bg-white/20 hover:bg-white/30 text-white border-0 h-8 text-xs"
              size="sm"
            >
              <Mic className="w-3 h-3 mr-1" />
              Start Recording
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { AudioRecorder };
export default AudioRecorder;
