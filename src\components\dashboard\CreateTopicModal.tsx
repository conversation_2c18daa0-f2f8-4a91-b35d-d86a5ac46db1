
import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Plus, FileText, Mic, Upload } from 'lucide-react';
import AudioRecorder from './AudioRecorder';
import FileUpload from './FileUpload';

interface CreateTopicModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTopic: (title: string, content: string) => void;
}

const CreateTopicModal = ({ isOpen, onClose, onCreateTopic }: CreateTopicModalProps) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) return;

    setLoading(true);
    try {
      onCreateTopic(title, content);
      setTitle('');
      setContent('');
      onClose();
    } catch (error) {
      console.error('Error creating topic:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRecordingComplete = (audioBlob: Blob, transcript?: string) => {
    if (transcript) {
      setContent(transcript);
      if (!title.trim()) {
        setTitle(`Lecture Recording - ${new Date().toLocaleDateString()}`);
      }
    }
  };

  const handleFileProcessed = (fileName: string, fileContent: string) => {
    setContent(fileContent);
    if (!title.trim()) {
      setTitle(fileName);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="w-5 h-5" />
            Create New Study Topic
          </DialogTitle>
          <DialogDescription>
            Add your study material by typing, recording audio, or uploading files to generate AI-powered study materials.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="text" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="text" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Text Input
            </TabsTrigger>
            <TabsTrigger value="record" className="flex items-center gap-2">
              <Mic className="w-4 h-4" />
              Record Audio
            </TabsTrigger>
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Upload File
            </TabsTrigger>
          </TabsList>

          <TabsContent value="text" className="space-y-4">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">Topic Title</Label>
                <Input
                  id="title"
                  placeholder="e.g., Chapter 5: Photosynthesis"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">Study Material</Label>
                <Textarea
                  id="content"
                  placeholder="Paste your textbook chapter, lecture notes, article, or any study material here..."
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="min-h-[200px] resize-none"
                  required
                />
                <p className="text-sm text-gray-500">
                  {content.length} characters
                </p>
              </div>
              <div className="flex justify-end gap-3">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading || !title.trim() || !content.trim()}
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : (
                    <Plus className="w-4 h-4 mr-2" />
                  )}
                  Create Topic
                </Button>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="record" className="space-y-4">
            <div className="text-center py-4">
              <AudioRecorder onRecordingComplete={handleRecordingComplete} />
              <p className="text-sm text-gray-500 mt-4">
                Record your lecture or study session. The audio will be transcribed and ready for AI processing.
              </p>
            </div>

            {content && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="recorded-title">Topic Title</Label>
                  <Input
                    id="recorded-title"
                    placeholder="e.g., Lecture Recording - Biology"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Transcribed Content</Label>
                  <Textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    className="min-h-[150px] resize-none"
                    placeholder="Transcribed content will appear here..."
                  />
                </div>
                <div className="flex justify-end gap-3">
                  <Button type="button" variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={loading || !title.trim() || !content.trim()}
                    className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                  >
                    {loading ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <Plus className="w-4 h-4 mr-2" />
                    )}
                    Create Topic
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="upload" className="space-y-4">
            <div className="text-center py-4">
              <FileUpload onFileProcessed={handleFileProcessed} />
              <p className="text-sm text-gray-500 mt-4">
                Upload audio, video, PDF, or document files. Content will be extracted and processed by AI.
              </p>
            </div>

            {content && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="uploaded-title">Topic Title</Label>
                  <Input
                    id="uploaded-title"
                    placeholder="e.g., Uploaded Document - Physics"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Extracted Content</Label>
                  <Textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    className="min-h-[150px] resize-none"
                    placeholder="Extracted content will appear here..."
                  />
                </div>
                <div className="flex justify-end gap-3">
                  <Button type="button" variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={loading || !title.trim() || !content.trim()}
                    className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                  >
                    {loading ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <Plus className="w-4 h-4 mr-2" />
                    )}
                    Create Topic
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export { CreateTopicModal };
export default CreateTopicModal;
