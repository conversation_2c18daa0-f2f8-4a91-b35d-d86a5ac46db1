
import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, BookOpen, Search, Mic, Upload, Video, FileText, Trophy, TrendingUp } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DashboardHeader from './DashboardHeader';
import TopicCard from './TopicCard';
import CreateTopicModal from './CreateTopicModal';
import AudioRecorder from './AudioRecorder';
import FileUpload from './FileUpload';
import StudyFeatures from './StudyFeatures';
import { GamificationDashboard } from '@/components/gamification/GamificationDashboard';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { addNotification } from '@/components/notifications/NotificationCenter';
import { checkTopicCreation, showUpgradePrompt, getUsageWarnings } from '@/services/paywallService';
import { updateUserStats, getTopicProgress, updateTopicProgress } from '@/services/gamificationService';

interface StudyTopic {
  id: string;
  title: string;
  original_content: string;
  created_at: string;
  contentCount: {
    notes: number;
    summary: number;
    quiz: number;
    flashcards: number;
  };
}

interface DashboardProps {
  onSelectTopic: (topicId: string) => void;
}

const Dashboard = ({ onSelectTopic }: DashboardProps) => {
  const [topics, setTopics] = useState<StudyTopic[]>([]);
  const [filteredTopics, setFilteredTopics] = useState<StudyTopic[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchTopics();
      showDiscordInviteNotification();
    }
  }, [user]);

  useEffect(() => {
    const filtered = topics.filter(topic =>
      topic.title.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredTopics(filtered);
  }, [topics, searchQuery]);

  // Show Discord community invitation notification
  const showDiscordInviteNotification = () => {
    const userEmail = user?.email || localStorage.getItem('user-email') || 'anonymous';
    const hasSeenDiscordInvite = localStorage.getItem(`discord-invite-seen-${userEmail}`);

    // Only show if user hasn't seen it before
    if (!hasSeenDiscordInvite) {
      setTimeout(() => {
        addNotification({
          type: 'info',
          title: 'Join Our Discord Community!',
          message: 'Share feedback and stay updated! We\'re constantly improving the app based on user suggestions.',
          action: {
            label: 'Join Discord',
            onClick: () => {
              window.open('https://discord.gg/placeholder-invite', '_blank');
              // Mark as seen when user clicks
              localStorage.setItem(`discord-invite-seen-${userEmail}`, 'true');
            }
          }
        });
      }, 2000); // Show after 2 seconds to let the dashboard load

      // Mark as seen after showing (but user can still click the action)
      localStorage.setItem(`discord-invite-seen-${userEmail}`, 'true');
    }
  };

  const fetchTopics = async () => {
    try {
      console.log('📚 Loading topics from localStorage...');
      const userEmail = user?.email || localStorage.getItem('user-email') || 'anonymous';

      // Get all localStorage keys that match the topic pattern
      const topicKeys = Object.keys(localStorage).filter(key =>
        key.startsWith(`topic-${userEmail}-`)
      );

      console.log('📚 Found topic keys:', topicKeys);

      const loadedTopics = topicKeys.map(key => {
        try {
          const topicData = JSON.parse(localStorage.getItem(key) || '{}');

          // Check if there's generated content for this topic
          const contentKey = `topic-content-${userEmail}-${topicData.id}`;
          const contentData = localStorage.getItem(contentKey);
          let contentCount = { notes: 0, summary: 0, quiz: 0, flashcards: 0 };

          if (contentData) {
            const parsedContent = JSON.parse(contentData);
            contentCount = {
              notes: parsedContent.filter((c: any) => c.content_type === 'notes').length,
              summary: parsedContent.filter((c: any) => c.content_type === 'summary').length,
              quiz: parsedContent.filter((c: any) => c.content_type === 'quiz').length,
              flashcards: parsedContent.filter((c: any) => c.content_type === 'flashcards').length,
            };
          }

          return {
            id: topicData.id,
            title: topicData.title,
            original_content: topicData.content,
            created_at: topicData.createdAt,
            fileType: topicData.fileType,
            fileName: topicData.fileName,
            fileUrl: topicData.fileUrl,
            metadata: topicData.metadata,
            contentCount
          };
        } catch (error) {
          console.error('Error parsing topic:', key, error);
          return null;
        }
      }).filter(Boolean);

      // Sort by creation date (newest first)
      loadedTopics.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      console.log('📚 Loaded topics:', loadedTopics);
      setTopics(loadedTopics);
    } catch (error) {
      console.error('Error fetching topics:', error);
      toast.error('Failed to load topics');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTopic = async (title: string, content: string) => {
    // Check paywall limits
    const paywallCheck = checkTopicCreation(user?.email);
    if (!paywallCheck.allowed) {
      showUpgradePrompt(paywallCheck);
      return;
    }

    try {
      console.log('🚀 Creating topic:', title, 'Content length:', content.length);

      // Create topic with localStorage for immediate functionality
      const topicId = Date.now().toString();
      const userEmail = user?.email || localStorage.getItem('user-email') || 'anonymous';

      const topic = {
        id: topicId,
        title,
        content,
        createdAt: new Date().toISOString()
      };

      // Save to localStorage with user-specific key
      localStorage.setItem(`topic-${userEmail}-${topicId}`, JSON.stringify(topic));

      // Update gamification
      updateUserStats({
        experiencePoints: 10 // Award XP for creating topic
      }, userEmail);

      // Initialize topic progress
      updateTopicProgress(topicId, {
        topicTitle: title
      }, userEmail);

      console.log('✅ Topic saved to localStorage:', topicId);
      toast.success('Topic created successfully! +10 XP');

      // Redirect to topic view immediately
      onSelectTopic(topicId);

    } catch (error) {
      console.error('Error creating topic:', error);
      toast.error('Failed to create topic');
    }
  };

  const handleFeatureSelect = (feature: string, options?: any) => {
    // Handle different study features
    switch (feature) {
      case 'notes':
      case 'flashcards':
      case 'quiz':
        // These will be handled in the topic view
        toast.info(`${feature} feature will be available in topic view`);
        break;
      case 'record':
        // Open audio recorder
        setShowCreateModal(true);
        break;
      case 'calendar':
        toast.info('Study calendar feature coming soon!');
        break;
      default:
        toast.info(`${feature} feature coming soon!`);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <DashboardHeader />
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-48 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-100/50 relative overflow-hidden">
      {/* Modern Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-indigo-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
      </div>

      <DashboardHeader />

      <div className="p-4 max-w-7xl mx-auto relative z-10">
        {/* Compact Header Section */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-lg font-bold text-white">AI</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  EZMind AI
                </h2>
                <p className="text-sm text-gray-600">AI-Powered Learning Platform</p>
              </div>
            </div>
          </div>

          {/* Compact Navigation Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-white/90 backdrop-blur-sm border shadow-sm rounded-lg p-1 h-10">
              <TabsTrigger
                value="overview"
                className="rounded-md text-sm font-medium text-gray-600 data-[state=active]:bg-indigo-500 data-[state=active]:text-white transition-all duration-200"
              >
                Overview
              </TabsTrigger>
              <TabsTrigger
                value="progress"
                className="rounded-md text-sm font-medium text-gray-600 data-[state=active]:bg-indigo-500 data-[state=active]:text-white transition-all duration-200"
              >
                <Trophy className="w-4 h-4 mr-1" />
                Progress
              </TabsTrigger>
              <TabsTrigger
                value="features"
                className="rounded-md text-sm font-medium text-gray-600 data-[state=active]:bg-indigo-500 data-[state=active]:text-white transition-all duration-200"
              >
                AI Features
              </TabsTrigger>
              <TabsTrigger
                value="topics"
                className="rounded-md text-sm font-medium text-gray-600 data-[state=active]:bg-indigo-500 data-[state=active]:text-white transition-all duration-200"
              >
                My Topics
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-4">
              {/* Compact Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <Card className="group border shadow-sm bg-gradient-to-br from-blue-500 to-blue-600 text-white hover:shadow-md hover:scale-[1.02] transition-all duration-200 cursor-pointer"
                      onClick={() => setShowCreateModal(true)}>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-white/20 rounded-lg">
                        <FileText className="w-5 h-5" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-sm">Upload Text</h3>
                        <p className="text-blue-100 text-xs">Paste or type content</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <AudioRecorder onRecordingComplete={(audioBlob, transcript) => {
                  // NO REDIRECT - Just show success message
                  console.log('🎵 Audio recording processed:', transcript.length, 'characters');
                  toast.success('Audio recording analyzed by AI!');
                  // Don't call handleCreateTopic to avoid redirect
                }} />

                <FileUpload
                  onFileProcessed={(fileName, content) => {
                    // NO REDIRECT - Just log the processing
                    console.log('📁 File processed successfully:', fileName, content.length);
                    toast.success(`File "${fileName}" processed successfully!`);
                    // Don't call handleCreateTopic to avoid redirect
                  }}
                  onTopicCreated={(topicId) => {
                    // Refresh topics list to show the new file
                    console.log('📚 Topic created from file:', topicId);
                    toast.success('File saved to study materials!');
                    fetchTopics(); // This will now load from localStorage

                    // Show Discord invite after first successful file processing
                    const userEmail = user?.email || localStorage.getItem('user-email') || 'anonymous';
                    const hasProcessedFile = localStorage.getItem(`first-file-processed-${userEmail}`);
                    if (!hasProcessedFile) {
                      localStorage.setItem(`first-file-processed-${userEmail}`, 'true');
                      setTimeout(() => {
                        addNotification({
                          type: 'info',
                          title: 'Join Our Discord Community!',
                          message: 'Great job on your first file! Join our Discord to share feedback and get tips from other users.',
                          action: {
                            label: 'Join Discord',
                            onClick: () => {
                              window.open('https://discord.gg/placeholder-invite', '_blank');
                            }
                          }
                        });
                      }, 3000); // Show after 3 seconds
                    }
                  }}
                />
              </div>
            </TabsContent>

            <TabsContent value="progress" className="mt-6">
              <GamificationDashboard />
            </TabsContent>

            <TabsContent value="features" className="mt-6">
              <StudyFeatures onFeatureSelect={handleFeatureSelect} />
            </TabsContent>

            <TabsContent value="topics" className="mt-6">
              {/* Search and Filter */}
              <div className="flex items-center justify-between mb-6">
                <div className="relative max-w-md flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search your study topics..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-white/80 backdrop-blur-sm border-0 shadow-md"
                  />
                </div>
                <div className="ml-4 text-sm text-gray-600 bg-white/60 px-3 py-2 rounded-lg">
                  {topics.length} topic{topics.length !== 1 ? 's' : ''} created
                </div>
              </div>

              {/* Topics Grid */}
              {filteredTopics.length === 0 ? (
                <Card className="text-center py-20 bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-black/5 rounded-3xl">
                  <CardHeader>
                    <div className="w-24 h-24 mx-auto bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-3xl flex items-center justify-center mb-8 shadow-2xl shadow-indigo-500/25 animate-pulse">
                      <BookOpen className="w-12 h-12 text-white" />
                    </div>
                    <CardTitle className="text-3xl font-bold text-gray-800 mb-4">
                      {topics.length === 0 ? 'Ready to start learning?' : 'No topics found'}
                    </CardTitle>
                    <CardDescription className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                      {topics.length === 0
                        ? 'Record a lecture, upload audio files, or paste your study material to get started with AI-powered learning tools.'
                        : 'Try adjusting your search terms or create a new topic.'
                      }
                    </CardDescription>
                  </CardHeader>
                  {topics.length === 0 && (
                    <CardContent>
                      <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                        <Button
                          onClick={() => setShowCreateModal(true)}
                          className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 hover:from-indigo-600 hover:via-purple-600 hover:to-pink-600 px-8 py-4 text-lg font-semibold rounded-2xl shadow-2xl shadow-indigo-500/25 hover:shadow-3xl hover:scale-105 transition-all duration-300"
                        >
                          <Plus className="w-5 h-5 mr-3" />
                          Create Your First Topic
                        </Button>
                      </div>
                    </CardContent>
                  )}
                </Card>
              ) : (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredTopics.map((topic) => (
                    <TopicCard
                      key={topic.id}
                      id={topic.id}
                      title={topic.title}
                      contentCount={topic.contentCount}
                      createdAt={topic.created_at}
                      onClick={() => onSelectTopic(topic.id)}
                    />
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        <CreateTopicModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onCreateTopic={handleCreateTopic}
        />
      </div>
    </div>
  );
};

export default Dashboard;
