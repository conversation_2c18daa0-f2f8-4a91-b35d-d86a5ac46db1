
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Brain, Sparkles, LogOut, User, Shield, Crown, CreditCard, Settings } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import useSubscription from '@/hooks/useSubscription';
import NotificationCenter from '@/components/notifications/NotificationCenter';

interface DashboardHeaderProps {
  userEmail?: string | null;
  onSignOut?: () => void;
}

const DashboardHeader = ({ userEmail, onSignOut }: DashboardHeaderProps) => {
  const { signOut, getUserDisplayName } = useAuth();
  const navigate = useNavigate();
  const { subscription } = useSubscription(userEmail || undefined);

  const handleSignOut = async () => {
    try {
      await signOut();
      if (onSignOut) {
        onSignOut();
      }
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };



  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'premium': return <Crown className="w-3 h-3" />;
      case 'yearly': return <Sparkles className="w-3 h-3" />;
      case 'monthly': return <User className="w-3 h-3" />;
      default: return <User className="w-3 h-3" />;
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'premium': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'yearly': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'monthly': return 'bg-blue-100 text-blue-800 border-blue-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  return (
    <header className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-6 py-4 sticky top-0 z-50 shadow-lg shadow-black/5">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <Sparkles className="w-2.5 h-2.5 text-white" />
            </div>
          </div>
          <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-blue-900 bg-clip-text text-transparent">
            EZMind AI
          </span>
        </div>

        <div className="flex items-center gap-4">
          {/* Subscription Badge */}
          <Badge variant="outline" className={getPlanColor(subscription.plan)}>
            {getPlanIcon(subscription.plan)}
            <span className="ml-1 capitalize">{subscription.plan}</span>
          </Badge>

          {/* Upgrade Button for Free Users */}
          {subscription.plan === 'free' && (
            <Button
              onClick={() => navigate('/pricing')}
              size="sm"
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 text-white"
            >
              <Crown className="w-4 h-4 mr-1" />
              Upgrade
            </Button>
          )}

          {/* Notification Center */}
          <NotificationCenter />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                <Avatar className="h-10 w-10">
                  <AvatarFallback>
                    <User className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end">
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-1 leading-none">
                  <p className="font-medium">{getUserDisplayName()}</p>
                  <p className="text-xs text-gray-500">{userEmail}</p>
                  <Badge variant="outline" size="sm" className={`w-fit mt-1 ${getPlanColor(subscription.plan)}`}>
                    {getPlanIcon(subscription.plan)}
                    <span className="ml-1 capitalize">{subscription.plan} Plan</span>
                  </Badge>
                </div>
              </div>
              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={() => navigate('/settings')}>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigate('/pricing')}>
                <CreditCard className="mr-2 h-4 w-4" />
                Billing & Plans
              </DropdownMenuItem>
              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export { DashboardHeader };
export default DashboardHeader;
