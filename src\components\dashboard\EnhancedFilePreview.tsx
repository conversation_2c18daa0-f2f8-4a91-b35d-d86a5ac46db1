import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { X, FileText, Image, Download, ZoomIn, ZoomOut } from 'lucide-react';

interface EnhancedFilePreviewProps {
  fileName: string;
  content: string;
  htmlContent?: string;
  fileType: 'pdf' | 'word' | 'text' | 'image';
  fileUrl?: string;
  onClose: () => void;
  onCreateTopic: () => void;
}

export const EnhancedFilePreview: React.FC<EnhancedFilePreviewProps> = ({
  fileName,
  content,
  htmlContent,
  fileType,
  fileUrl,
  onClose,
  onCreateTopic
}) => {
  const [viewMode, setViewMode] = useState<'formatted' | 'text'>('formatted');
  const [zoom, setZoom] = useState(100);

  const getFileIcon = () => {
    switch (fileType) {
      case 'pdf': return '📄';
      case 'word': return '📝';
      case 'text': return '📋';
      case 'image': return '🖼️';
      default: return '📄';
    }
  };

  const getFileTypeColor = () => {
    switch (fileType) {
      case 'pdf': return 'bg-red-100 text-red-700 border-red-200';
      case 'word': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'text': return 'bg-gray-100 text-gray-700 border-gray-200';
      case 'image': return 'bg-purple-100 text-purple-700 border-purple-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <Card className={`mt-4 border-2 ${getFileTypeColor()}`}>
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{getFileIcon()}</span>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {fileName}
              </h3>
              <p className="text-sm text-gray-600 capitalize">
                {fileType} Document Preview
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* View Mode Toggle */}
            {htmlContent && (
              <div className="flex bg-gray-100 rounded-lg p-1">
                <Button
                  onClick={() => setViewMode('formatted')}
                  variant={viewMode === 'formatted' ? 'default' : 'ghost'}
                  size="sm"
                  className="text-xs"
                >
                  <FileText className="w-3 h-3 mr-1" />
                  Formatted
                </Button>
                <Button
                  onClick={() => setViewMode('text')}
                  variant={viewMode === 'text' ? 'default' : 'ghost'}
                  size="sm"
                  className="text-xs"
                >
                  Text Only
                </Button>
              </div>
            )}
            
            {/* Zoom Controls */}
            <div className="flex items-center gap-1">
              <Button
                onClick={() => setZoom(Math.max(50, zoom - 25))}
                variant="ghost"
                size="sm"
                className="p-1"
              >
                <ZoomOut className="w-4 h-4" />
              </Button>
              <span className="text-xs text-gray-600 min-w-[3rem] text-center">
                {zoom}%
              </span>
              <Button
                onClick={() => setZoom(Math.min(200, zoom + 25))}
                variant="ghost"
                size="sm"
                className="p-1"
              >
                <ZoomIn className="w-4 h-4" />
              </Button>
            </div>

            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Content Preview */}
        <div 
          className="bg-white rounded-lg border max-h-96 overflow-auto"
          style={{ fontSize: `${zoom}%` }}
        >
          {viewMode === 'formatted' && htmlContent ? (
            // Formatted HTML view with images
            <div 
              className="p-4 prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: htmlContent }}
              style={{
                fontSize: 'inherit'
              }}
            />
          ) : (
            // Plain text view
            <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono p-4 leading-relaxed">
              {content}
            </pre>
          )}
        </div>

        {/* File Info */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-600">File Type:</span>
              <p className="text-gray-900 capitalize">{fileType}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Content Length:</span>
              <p className="text-gray-900">{content.length.toLocaleString()} characters</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Word Count:</span>
              <p className="text-gray-900">
                {content.split(/\s+/).filter(word => word.length > 0).length.toLocaleString()} words
              </p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Has Images:</span>
              <p className="text-gray-900">
                {htmlContent && htmlContent.includes('<img') ? 'Yes' : 'No'}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 mt-6">
          <Button
            onClick={onCreateTopic}
            className="bg-blue-600 hover:bg-blue-700 text-white flex-1"
          >
            ✅ Create Topic & Generate AI Materials
          </Button>
          <Button
            onClick={onClose}
            variant="outline"
            className="px-6"
          >
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedFilePreview;
