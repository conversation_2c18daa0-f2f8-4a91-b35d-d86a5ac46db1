import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Image as ImageIcon,
  Video,
  Music,
  FileSpreadsheet,
  Presentation,
  Download,
  Edit3,
  Save,
  X,
  Play,
  Pause,
  Volume2,
  Maximize2,
  Sparkles,
  Brain,
  BookOpen,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { processDocument } from '@/services/documentService';

interface FilePreviewProps {
  file: File;
  onClose: () => void;
  onSave?: (content: string) => void;
  onProcessWithAI?: (result: any) => void;
  className?: string;
}

const FilePreview = ({ file, onClose, onSave, onProcessWithAI, className }: FilePreviewProps) => {
  const [previewContent, setPreviewContent] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingAI, setIsProcessingAI] = useState(false);
  const [aiResult, setAiResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Media refs and state
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    loadFilePreview();
  }, [file]);

  const getFileCategory = (file: File): 'document' | 'image' | 'video' | 'audio' | 'office' | 'other' => {
    const extension = file.name.toLowerCase().split('.').pop() || '';
    const mimeType = file.type.toLowerCase();

    if (['txt', 'md', 'csv', 'json', 'xml'].includes(extension) || mimeType.startsWith('text/')) {
      return 'document';
    }
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(extension) || mimeType.startsWith('image/')) {
      return 'image';
    }
    if (['mp4', 'mov', 'avi', 'mkv', 'webm'].includes(extension) || mimeType.startsWith('video/')) {
      return 'video';
    }
    if (['mp3', 'wav', 'm4a', 'aac', 'ogg', 'flac'].includes(extension) || mimeType.startsWith('audio/')) {
      return 'audio';
    }
    if (['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'pdf'].includes(extension)) {
      return 'office';
    }

    return 'other';
  };

  const isFileSupported = (file: File): boolean => {
    const supportedExtensions = [
      'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg',
      'mp3', 'wav', 'm4a', 'aac', 'ogg', 'flac',
      'mp4', 'mov', 'avi', 'mkv', 'webm',
      'pdf', 'txt', 'md', 'csv', 'json', 'xml',
      'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt'
    ];

    const extension = file.name.toLowerCase().split('.').pop() || '';
    const isSupported = supportedExtensions.includes(extension);
    const isSizeValid = file.size <= 50 * 1024 * 1024; // 50MB

    return isSupported && isSizeValid;
  };

  const loadFilePreview = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const fileCategory = getFileCategory(file);

      switch (fileCategory) {
        case 'document':
          await loadTextPreview();
          break;
        case 'image':
          await loadImagePreview();
          break;
        case 'video':
          await loadVideoPreview();
          break;
        case 'audio':
          await loadAudioPreview();
          break;
        case 'office':
          await loadOfficePreview();
          break;
        default:
          setError('File type not supported for preview');
      }
    } catch (err) {
      console.error('Error loading file preview:', err);
      setError(`Failed to load preview: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTextPreview = async () => {
    const text = await file.text();
    setPreviewContent(text);
    setEditedContent(text);
  };

  const loadImagePreview = async () => {
    const url = URL.createObjectURL(file);
    setPreviewContent(url);
  };

  const loadVideoPreview = async () => {
    const url = URL.createObjectURL(file);
    setPreviewContent(url);
  };

  const loadAudioPreview = async () => {
    const url = URL.createObjectURL(file);
    setPreviewContent(url);
  };

  const loadOfficePreview = async () => {
    setPreviewContent('Microsoft Office document ready for AI processing. The AI can read and analyze the full document content including text, images, tables, and formatting.');
  };

  const handleProcessWithAI = async () => {
    if (!isFileSupported(file)) {
      toast.error('File type not supported or exceeds size limit');
      return;
    }

    setIsProcessingAI(true);
    try {
      console.log('🤖 Processing file with AI:', file.name);
      toast.info('🚀 Processing with AI...');

      const result = await processDocument(file);
      setAiResult(result);

      if (onProcessWithAI) {
        onProcessWithAI(result);
      }

      toast.success('✅ AI processing completed!');
    } catch (error) {
      console.error('❌ Error processing with AI:', error);
      toast.error(`Failed to process with AI: ${error.message}`);
    } finally {
      setIsProcessingAI(false);
    }
  };

  const handleSaveEdit = () => {
    if (onSave) {
      onSave(editedContent);
      setPreviewContent(editedContent);
      setIsEditing(false);
      toast.success('Content saved successfully');
    }
  };

  const handleMediaPlay = () => {
    const media = videoRef.current || audioRef.current;
    if (media) {
      if (isPlaying) {
        media.pause();
      } else {
        media.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    const media = videoRef.current || audioRef.current;
    if (media) {
      setCurrentTime(media.currentTime);
      setDuration(media.duration || 0);
    }
  };

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getFileIcon = () => {
    const category = getFileCategory(file);
    switch (category) {
      case 'document': return <FileText className="w-6 h-6" />;
      case 'image': return <ImageIcon className="w-6 h-6" />;
      case 'video': return <Video className="w-6 h-6" />;
      case 'audio': return <Music className="w-6 h-6" />;
      case 'office':
        if (file.name.includes('xlsx') || file.name.includes('xls')) {
          return <FileSpreadsheet className="w-6 h-6" />;
        }
        if (file.name.includes('pptx') || file.name.includes('ppt')) {
          return <Presentation className="w-6 h-6" />;
        }
        return <FileText className="w-6 h-6" />;
      default: return <FileText className="w-6 h-6" />;
    }
  };

  const getFileTypeLabel = () => {
    const category = getFileCategory(file);
    switch (category) {
      case 'document': return 'Document';
      case 'image': return 'Image';
      case 'video': return 'Video';
      case 'audio': return 'Audio';
      case 'office': return 'Office Document';
      default: return 'File';
    }
  };



  const renderPreviewContent = () => {
    const fileCategory = getFileCategory(file);

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading preview...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center h-64 text-gray-500">
          <FileText className="w-12 h-12 mb-2" />
          <p>{error}</p>
          <p className="text-sm mt-2">File will be processed directly by AI</p>
        </div>
      );
    }

    switch (fileCategory) {
      case 'document':
        return isEditing ? (
          <div className="space-y-4">
            <Textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="min-h-[400px] font-mono text-sm"
              placeholder="Edit your content here..."
            />
            <div className="flex space-x-2">
              <Button onClick={handleSaveEdit} size="sm">
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </Button>
              <Button onClick={() => setIsEditing(false)} variant="outline" size="sm">
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <pre className="whitespace-pre-wrap font-mono text-sm bg-gray-50 p-4 rounded-lg max-h-96 overflow-auto">
              {previewContent}
            </pre>
            <Button onClick={() => setIsEditing(true)} size="sm" variant="outline">
              <Edit3 className="w-4 h-4 mr-2" />
              Edit Content
            </Button>
          </div>
        );

      case 'image':
        return (
          <div className="flex justify-center">
            <img
              src={previewContent}
              alt={file.name}
              className="max-w-full max-h-96 object-contain rounded-lg shadow-lg"
              onLoad={() => URL.revokeObjectURL(previewContent)}
            />
          </div>
        );

      case 'video':
        return (
          <div className="space-y-4">
            <video
              ref={videoRef}
              src={previewContent}
              className="w-full max-h-96 rounded-lg shadow-lg"
              onTimeUpdate={handleTimeUpdate}
              onLoadedMetadata={handleTimeUpdate}
              controls
            />
            <div className="flex items-center space-x-4">
              <Button onClick={handleMediaPlay} size="sm">
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>
              <span className="text-sm text-gray-600">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
              <Button size="sm" variant="outline">
                <Maximize2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        );

      case 'audio':
        return (
          <div className="space-y-4">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
              <div className="flex items-center space-x-4">
                <Music className="w-8 h-8" />
                <div>
                  <h3 className="font-semibold">{file.name}</h3>
                  <p className="text-sm opacity-90">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
            </div>
            <audio
              ref={audioRef}
              src={previewContent}
              className="w-full"
              onTimeUpdate={handleTimeUpdate}
              onLoadedMetadata={handleTimeUpdate}
              controls
            />
            <div className="flex items-center space-x-4">
              <Button onClick={handleMediaPlay} size="sm">
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>
              <Volume2 className="w-4 h-4" />
              <span className="text-sm text-gray-600">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>
          </div>
        );

      case 'office':
        return (
          <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg">
            {getFileIcon()}
            <h3 className="mt-4 font-semibold">{file.name}</h3>
            <p className="text-sm text-gray-600 mt-2 text-center max-w-md">
              {previewContent}
            </p>
            <div className="mt-4 text-xs text-gray-500">
              Size: {(file.size / 1024 / 1024).toFixed(2)} MB
            </div>
          </div>
        );

      default:
        return (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <FileText className="w-12 h-12 mb-2" />
            <p>Preview not available</p>
            <p className="text-sm mt-2">File will be processed directly by AI</p>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getFileIcon()}
              <div>
                <CardTitle className="text-lg">{file.name}</CardTitle>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge variant="secondary">{getFileTypeLabel()}</Badge>
                  <span className="text-sm text-gray-500">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </span>
                  {isFileSupported(file) && (
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      AI Compatible
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4" />
              </Button>
              <Button onClick={onClose} variant="ghost" size="sm">
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>


        <CardContent className="p-6">
          {/* File Preview */}
          <div className="mb-6">
            {renderPreviewContent()}
          </div>

          {/* AI Processing Results */}
          {aiResult && (
            <div className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
              <h3 className="font-semibold mb-3 flex items-center text-green-800">
                <Brain className="w-4 h-4 mr-2" />
                AI Analysis Results
              </h3>
              <div className="max-h-60 overflow-y-auto text-sm text-gray-700">
                <div className="prose prose-sm max-w-none">
                  <pre className="whitespace-pre-wrap font-sans">{aiResult.content}</pre>
                </div>
              </div>
              <div className="mt-3 text-xs text-gray-500">
                Content extracted and processed successfully
              </div>
            </div>
          )}

          {/* AI Processing Status */}
          {isProcessingAI && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h3 className="font-semibold mb-3 flex items-center text-blue-800">
                <Sparkles className="w-4 h-4 mr-2 animate-pulse" />
                Processing with AI
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                Analyzing your file with advanced AI...
              </p>
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">Processing file with AI model</span>
              </div>
            </div>
          )}

          {/* AI Processing Controls */}
          {isFileSupported(file) && (
            <div className="space-y-4">
              <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                <h3 className="font-semibold text-lg mb-2 text-gray-800 flex items-center">
                  <Brain className="w-5 h-5 mr-2" />
                  AI Processing
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Process this file with advanced AI for content extraction and analysis.
                  The AI can understand images, audio, video, and documents.
                </p>

                <Button
                  onClick={handleProcessWithAI}
                  disabled={isProcessingAI}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  size="sm"
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Process with AI
                </Button>
              </div>

              <div className="text-center">
                <p className="text-xs text-gray-500">
                  AI can process files up to 50MB including images, audio, video, PDFs, and Office documents
                </p>
              </div>
            </div>
          )}

          {/* Unsupported File Warning */}
          {!isFileSupported(file) && (
            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <h3 className="font-semibold mb-2 text-yellow-800">File Not Supported</h3>
              <p className="text-sm text-yellow-700">
                This file type is not supported or exceeds the 50MB size limit.
                Supported formats include images, audio, video, PDF, and Office documents.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FilePreview;
