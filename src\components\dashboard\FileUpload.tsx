import { useState, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, Loader2, Video, Plus, Globe, Sparkles } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { processDocument } from '@/services/documentService';
import { processWebsiteUrl, isValidUrl, looksLikeUrl } from '@/services/websiteService';
import { isValidYouTubeUrl, createStudyMaterialFromVideo } from '@/services/youtubeService';
import { addNotification } from '@/components/notifications/NotificationCenter';
import { checkFileUpload, showUpgradePrompt, enforcePaywall } from '@/services/paywallService';
import { updateUserStats, updateTopicProgress } from '@/services/gamificationService';

interface FileUploadProps {
  onFileProcessed: (fileName: string, content: string) => void;
  onTopicCreated?: (topicId: string) => void;
  className?: string;
}

const FileUpload = ({ onFileProcessed, onTopicCreated, className }: FileUploadProps) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [showYoutubeInput, setShowYoutubeInput] = useState(false);
  const [showWebsiteInput, setShowWebsiteInput] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<string>('');

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      await processFiles(files);
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      await processFiles(fileArray);
      e.target.value = '';
    }
  };

  // Simple file processing - no preview, immediate processing
  const processFiles = async (files: File[]) => {
    if (files.length === 0) return;

    // Check paywall for each file
    for (const file of files) {
      const paywallCheck = checkFileUpload(file.size);
      if (!paywallCheck.allowed) {
        showUpgradePrompt(paywallCheck);
        return;
      }
    }

    setIsProcessing(true);
    setProcessingStatus(`Processing ${files.length} file${files.length > 1 ? 's' : ''}...`);

    try {
      let processed = 0;
      for (const file of files) {
        await processFileWithAI(file);
        processed++;
        setProcessingStatus(`Processed ${processed}/${files.length} files...`);

        // Increment usage after successful processing
        await enforcePaywall('fileUploads', 1);
      }

      toast.success(`Successfully processed ${processed} file${processed > 1 ? 's' : ''}!`);

      // Add notification
      addNotification({
        type: 'success',
        title: 'Files Processed Successfully',
        message: `${processed} file${processed > 1 ? 's' : ''} processed and ready for AI analysis`
      });
    } catch (error) {
      console.error('❌ Error processing files:', error);
      toast.error('Failed to process some files');

      // Add error notification
      addNotification({
        type: 'error',
        title: 'File Processing Failed',
        message: 'Some files could not be processed. Please try again.'
      });
    } finally {
      setIsProcessing(false);
      setProcessingStatus('');
    }
  };

  const processFileWithAI = async (file: File) => {
    console.log('🤖 Processing file:', file.name, file.type, file.size);
    setIsProcessing(true);

    try {
      // For PDFs, send directly to Gemini 2.5 Flash instead of extracting text
      if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
        console.log('📄 Processing PDF directly with Gemini 2.5 Flash...');

        // Convert file to base64 for Gemini API
        const base64Data = await fileToBase64(file);

        // Send PDF directly to Gemini for processing
        const content = await processPDFWithGemini(base64Data);

        console.log('✅ PDF processed directly with Gemini:', {
          fileName: file.name,
          contentLength: content.length
        });

        // Save to study materials with original file
        await autoSaveToStudy(file, content);
      } else {
        // For other file types, use existing text extraction
        const result = await processDocument(file);
        const content = result.content;

        if (!content || content.trim().length === 0) {
          throw new Error('No content could be extracted from the file');
        }

        console.log('✅ File processed successfully:', {
          fileName: file.name,
          contentLength: content.length,
          wordCount: result.wordCount
        });

        // Automatically save to study materials
        await autoSaveToStudy(file, content);
      }

    } catch (error) {
      console.error('❌ Error processing file:', error);
      toast.error(`Failed to process file: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Helper function to convert file to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data:application/pdf;base64, prefix
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  };

  // Process PDF directly with Gemini 2.5 Flash - Optimized
  const processPDFWithGemini = async (base64Data: string): Promise<string> => {
    try {
      console.log('🚀 Sending PDF to Gemini 2.5 Flash for direct processing...');
      setProcessingStatus('Analyzing PDF with AI...');

      // Create AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, 60000); // 60 second timeout

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-001:generateContent?key=AIzaSyBd5ImRFOeTFhAQUgBVjhsTkFHsHmelbmI`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
        body: JSON.stringify({
          contents: [{
            parts: [
              {
                text: `Extract and format the text content from this PDF document. Preserve structure, headings, and key information. Be concise but comprehensive.`
              },
              {
                inline_data: {
                  mime_type: "application/pdf",
                  data: base64Data
                }
              }
            ]
          }],
          generationConfig: {
            temperature: 0.1,
            topK: 16, // Reduced for faster processing
            topP: 0.8, // Reduced for faster processing
            maxOutputTokens: 4096, // Reduced for faster processing
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_NONE"
            }
          ]
        })
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Gemini API error:', errorText);
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
      }

      setProcessingStatus('Processing AI response...');
      const data = await response.json();

      if (data.candidates && data.candidates.length > 0) {
        const content = data.candidates[0].content.parts[0].text.trim();

        if (content.length > 0) {
          console.log('✅ PDF processed successfully with Gemini 2.5 Flash');
          setProcessingStatus('Finalizing...');
          return content;
        }
      }

      throw new Error('No content extracted from PDF by Gemini');

    } catch (error) {
      if (error.name === 'AbortError') {
        console.error('❌ PDF processing timed out');
        throw new Error('PDF processing timed out. Please try with a smaller file.');
      }
      console.error('❌ Error processing PDF with Gemini:', error);
      throw new Error(`Failed to process PDF with Gemini: ${error.message}`);
    }
  };

  // Automatically save file to study materials
  const autoSaveToStudy = async (file: File, content: string) => {
    try {
      console.log('💾 Auto-saving file to study materials:', file.name);

      // Create topic with file content
      const topicId = Date.now().toString();
      const userEmail = localStorage.getItem('user-email') || 'anonymous';

      // Create blob URL for files that need visual preview
      let fileUrl = undefined;
      if (file.type.includes('pdf') || file.type.includes('image') || file.type.includes('video') || file.type.includes('audio') ||
          file.type.includes('presentation') || file.name.endsWith('.pptx') || file.name.endsWith('.ppt')) {
        fileUrl = URL.createObjectURL(file);
        console.log('📎 Created blob URL for file preview:', fileUrl);
      }

      const topic = {
        id: topicId,
        title: file.name,
        content: content,
        createdAt: new Date().toISOString(),
        fileType: file.type,
        fileName: file.name,
        fileUrl: fileUrl,
        metadata: {
          size: file.size,
          lastModified: file.lastModified
        }
      };

      // Save to localStorage with user-specific key
      localStorage.setItem(`topic-${userEmail}-${topicId}`, JSON.stringify(topic));

      // Update gamification
      updateUserStats({
        experiencePoints: 5 // Award XP for file upload
      }, userEmail);

      // Initialize topic progress
      updateTopicProgress(topicId, {
        topicTitle: file.name
      }, userEmail);

      console.log('✅ File auto-saved as study topic:', topicId);
      toast.success(`"${file.name}" saved to study materials! +5 XP`);

      // Notify parent components
      onFileProcessed(file.name, content);

      if (onTopicCreated) {
        onTopicCreated(topicId);
      }

    } catch (error) {
      console.error('❌ Error auto-saving file to study:', error);
      toast.error(`Failed to save file: ${error.message}`);
    }
  };

  const handleYouTubeUrl = async () => {
    if (!youtubeUrl.trim()) return;

    if (!isValidYouTubeUrl(youtubeUrl)) {
      toast.error('Please enter a valid YouTube URL');
      return;
    }

    setIsProcessing(true);
    try {
      console.log('🎬 Processing YouTube URL:', youtubeUrl);
      toast.info('🚀 Processing YouTube video...');

      // Use the proper function that extracts video title and content
      const result = await createStudyMaterialFromVideo(youtubeUrl);

      console.log('✅ YouTube processing completed');

      // Auto-save YouTube content to study materials
      const topicId = Date.now().toString();
      const userEmail = localStorage.getItem('user-email') || 'anonymous';

      const topic = {
        id: topicId,
        title: result.title, // Use the actual video title
        content: result.content,
        createdAt: new Date().toISOString(),
        fileType: 'video/youtube',
        fileName: result.title,
        fileUrl: youtubeUrl,
        metadata: {
          videoId: youtubeUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1],
          originalUrl: youtubeUrl,
          title: result.title
        }
      };

      localStorage.setItem(`topic-${userEmail}-${topicId}`, JSON.stringify(topic));
      onFileProcessed(result.title, result.content);
      if (onTopicCreated) onTopicCreated(topicId);

      setYoutubeUrl('');
      setShowYoutubeInput(false);

    } catch (error) {
      console.error('❌ Error processing YouTube video:', error);
      toast.error(`Failed to process YouTube video: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleWebsiteUrl = async () => {
    if (!websiteUrl.trim()) return;

    if (!isValidUrl(websiteUrl) && !looksLikeUrl(websiteUrl)) {
      toast.error('Please enter a valid website URL');
      return;
    }

    setIsProcessing(true);
    try {
      console.log('🌐 Processing website URL:', websiteUrl);
      toast.info('🚀 Processing website content...');

      const result = await processWebsiteUrl(websiteUrl);

      console.log('✅ Website processing completed');

      // Auto-save website content to study materials
      const websiteFile = {
        name: `Website - ${result.title}`,
        type: 'text/website',
        size: 0,
        lastModified: Date.now()
      } as File;

      // Store website URL as fileUrl for preview
      const topicId = Date.now().toString();
      const userEmail = localStorage.getItem('user-email') || 'anonymous';

      const topic = {
        id: topicId,
        title: result.title, // Use the extracted title instead of generic name
        content: result.content,
        createdAt: new Date().toISOString(),
        fileType: 'text/website',
        fileName: result.title,
        fileUrl: websiteUrl,
        metadata: {
          title: result.title,
          originalUrl: websiteUrl,
          description: result.description,
          domain: result.metadata?.domain,
          wordCount: result.metadata?.wordCount
        }
      };

      console.log('💾 Saving website topic with content length:', result.content.length);

      localStorage.setItem(`topic-${userEmail}-${topicId}`, JSON.stringify(topic));
      onFileProcessed(websiteFile.name, result.content);
      if (onTopicCreated) onTopicCreated(topicId);

      setWebsiteUrl('');
      setShowWebsiteInput(false);

    } catch (error) {
      console.error('❌ Error processing website:', error);
      toast.error(`Failed to process website: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className={className}>
      <Card className="border shadow-sm bg-gradient-to-br from-blue-500 to-purple-600 text-white hover:shadow-md transition-all duration-200">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3 mb-3">
            <div className="p-2 bg-white/20 rounded-lg">
              <Upload className="w-5 h-5" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-sm">Upload & Process</h3>
              <p className="text-blue-100 text-xs">Files, videos, websites</p>
            </div>
          </div>

          {/* Compact File Upload Area */}
          <div
            className={`border-2 border-dashed border-white/30 rounded-lg p-4 text-center transition-colors ${
              isDragOver ? 'border-white/60 bg-white/10' : 'hover:border-white/50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileSelect}
              className="hidden"
              accept=".pdf,.docx,.doc,.txt,.mp3,.wav,.m4a,.mp4,.mov,.avi,.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg,.aac,.ogg,.flac,.mkv,.webm,.xlsx,.xls,.pptx,.ppt,.csv,.json,.xml,.md"
              multiple
            />

            {isProcessing ? (
              <div className="flex flex-col items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-white/30 border-t-white mb-2"></div>
                <p className="font-medium text-sm">Processing...</p>
                {processingStatus && (
                  <p className="text-xs text-white/90 mt-1">{processingStatus}</p>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <Upload className="w-6 h-6 mb-2" />
                <p className="font-medium text-sm">Drop files or click to browse</p>
                <p className="text-xs text-blue-100 mt-1">
                  PDF, DOCX, TXT, audio, video • Multiple files
                </p>
              </div>
            )}
          </div>

          {/* Compact YouTube Input */}
          <div className="mt-3">
            {showYoutubeInput ? (
              <div className="flex space-x-2">
                <Input
                  placeholder="YouTube URL..."
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                  className="flex-1 bg-white/20 border-white/30 text-white placeholder:text-white/70 text-sm h-8"
                  onKeyDown={(e) => e.key === 'Enter' && handleYouTubeUrl()}
                />
                <Button
                  onClick={handleYouTubeUrl}
                  disabled={isProcessing || !youtubeUrl.trim()}
                  className="bg-white/20 hover:bg-white/30 h-8 px-3"
                  size="sm"
                >
                  {isProcessing ? <Loader2 className="w-3 h-3 animate-spin" /> : <Plus className="w-3 h-3" />}
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => setShowYoutubeInput(true)}
                className="w-full bg-white/20 hover:bg-white/30 text-white border-0 h-8 text-sm"
                disabled={isProcessing}
                size="sm"
              >
                <Video className="w-3 h-3 mr-2" />
                YouTube Video
              </Button>
            )}
          </div>

          {/* Compact Website URL Input */}
          <div className="mt-2">
            {showWebsiteInput ? (
              <div className="flex space-x-2">
                <Input
                  placeholder="Website URL..."
                  value={websiteUrl}
                  onChange={(e) => setWebsiteUrl(e.target.value)}
                  className="flex-1 bg-white/20 border-white/30 text-white placeholder:text-white/70 text-sm h-8"
                  onKeyDown={(e) => e.key === 'Enter' && handleWebsiteUrl()}
                />
                <Button
                  onClick={handleWebsiteUrl}
                  disabled={isProcessing || !websiteUrl.trim()}
                  className="bg-white/20 hover:bg-white/30 h-8 px-3"
                  size="sm"
                >
                  {isProcessing ? <Loader2 className="w-3 h-3 animate-spin" /> : <Plus className="w-3 h-3" />}
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => setShowWebsiteInput(true)}
                className="w-full bg-white/20 hover:bg-white/30 text-white border-0 h-8 text-sm"
                disabled={isProcessing}
                size="sm"
              >
                <Globe className="w-3 h-3 mr-2" />
                Website Content
              </Button>
            )}
          </div>

          {/* Processing Status */}
          {isProcessing && (
            <div className="mt-4 p-3 bg-white/10 rounded-lg">
              <div className="flex items-center space-x-2">
                <Sparkles className="w-4 h-4 animate-pulse" />
                <span className="text-sm">{processingStatus || 'Processing content for AI analysis...'}</span>
              </div>
            </div>
          )}


        </CardContent>
      </Card>
    </div>
  );
};

export { FileUpload };
export default FileUpload;
