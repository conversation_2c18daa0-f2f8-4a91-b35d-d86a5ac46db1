import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  Plus,
  FileText,
  Video,
  Mic,
  Upload,
  FolderPlus,
  MoreHorizontal,
  Calendar,
  Users,
  Clock,
  Youtube,
  File,
  Image,
  Music,
  FileImage,
  FileAudio,
  FileVideo
} from 'lucide-react';
import { DashboardHeader } from './DashboardHeader';
import { CreateTopicModal } from './CreateTopicModal';
import { FileUpload } from './FileUpload';
import { AudioRecorder } from './AudioRecorder';
import AudioLibrary from '@/components/audio/AudioLibrary';
import YouTubeProcessor from './YouTubeProcessor';
import SmartTextInput from './SmartTextInput';

import StreamlinedUploadCard from './StreamlinedUploadCard';
import { useUsageGate } from '@/hooks/useSubscription';
import { checkTopicCreation, checkFileUpload, checkAudioRecording, enforcePaywall } from '@/services/paywallService';
import { toast } from 'sonner';
import UsageLimitsDisplay from './UsageLimitsDisplay';
import { getUserSubscription } from '@/services/subscriptionService';

interface Topic {
  id: string;
  title: string;
  type: 'document' | 'video' | 'audio' | 'session' | 'folder' | 'pdf' | 'word' | 'text' | 'image' | 'youtube';
  date: string;
  size?: string;
  collaborators?: string[];
  isFolder?: boolean;
  itemCount?: number;
  color?: string;
  userEmail?: string; // Add user ownership
  sourceType?: string; // Track source (youtube, audio, file)
  fileExtension?: string; // Track file extension for display
}

interface LibraryDashboardProps {
  onSelectTopic: (topicId: string) => void;
  userEmail?: string | null;
  onSignOut?: () => void;
}

// Helper function to determine topic type from file data
const determineTopicType = (fileType?: string, fileName?: string): Topic['type'] => {
  if (!fileType && !fileName) return 'text';

  if (fileType === 'youtube' || fileName?.includes('youtube')) return 'youtube';
  if (fileType === 'audio' || fileName?.match(/\.(mp3|wav|m4a|aac|ogg|flac)$/i)) return 'audio';
  if (fileType === 'pdf' || fileName?.endsWith('.pdf')) return 'pdf';
  if (fileType === 'word' || fileName?.match(/\.(doc|docx)$/i)) return 'word';
  if (fileType === 'video' || fileName?.match(/\.(mp4|mov|avi|mkv|webm)$/i)) return 'video';
  if (fileType === 'image' || fileName?.match(/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i)) return 'image';

  return 'document';
};

export const LibraryDashboard: React.FC<LibraryDashboardProps> = ({
  onSelectTopic,
  userEmail,
  onSignOut
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAudioLibrary, setShowAudioLibrary] = useState(false);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const subscription = getUserSubscription();

  // Function to reload topics (can be called when returning from Study page)
  const reloadTopics = () => {
    console.log('🔄 Reloading topics...');
    setIsLoading(true);

    try {
      const topicKeys = Object.keys(localStorage).filter(key =>
        key.startsWith(`topic-${userEmail || 'anonymous'}-`)
      );

      const loadedTopics = topicKeys.map(key => {
        try {
          const topicData = JSON.parse(localStorage.getItem(key) || '{}');

          const topic: Topic = {
            id: topicData.id,
            title: topicData.title,
            type: determineTopicType(topicData.fileType, topicData.fileName),
            date: new Date(topicData.createdAt).toLocaleDateString(),
            userEmail: userEmail || 'anonymous',
            sourceType: topicData.fileType,
            fileExtension: topicData.fileName?.split('.').pop()
          };

          return topic;
        } catch (error) {
          console.error('Error parsing individual topic:', key, error);
          return null;
        }
      }).filter(Boolean);

      loadedTopics.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setTopics(loadedTopics);
      console.log('🔄 Topics reloaded successfully:', loadedTopics.length);
    } catch (error) {
      console.error('Error reloading topics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Usage gates for different features
  const fileUploadGate = useUsageGate(
    'fileUploads',
    'File Upload',
    'Upload and process documents, PDFs, and other files with AI-powered analysis.',
    'basic',
    userEmail || undefined
  );

  const audioRecordingGate = useUsageGate(
    'audioTranscriptions',
    'Audio Recording & Transcription',
    'Record audio and get AI-powered transcriptions for your study sessions.',
    'basic',
    userEmail || undefined
  );

  // Load topics from localStorage on mount - USER SPECIFIC
  useEffect(() => {
    const loadTopics = () => {
      try {
        console.log('📚 Loading topics from individual localStorage keys...');

        // Get all localStorage keys that match the individual topic pattern
        const topicKeys = Object.keys(localStorage).filter(key =>
          key.startsWith(`topic-${userEmail || 'anonymous'}-`)
        );

        console.log('📚 Found individual topic keys:', topicKeys);

        const loadedTopics = topicKeys.map(key => {
          try {
            const topicData = JSON.parse(localStorage.getItem(key) || '{}');

            // Convert individual topic data to LibraryDashboard format
            const topic: Topic = {
              id: topicData.id,
              title: topicData.title,
              type: determineTopicType(topicData.fileType, topicData.fileName),
              date: new Date(topicData.createdAt).toLocaleDateString(),
              userEmail: userEmail || 'anonymous',
              sourceType: topicData.fileType,
              fileExtension: topicData.fileName?.split('.').pop()
            };

            return topic;
          } catch (error) {
            console.error('Error parsing individual topic:', key, error);
            return null;
          }
        }).filter(Boolean);

        // Sort by creation date (newest first)
        loadedTopics.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

        console.log('📚 Loaded topics from individual keys:', loadedTopics);
        setTopics(loadedTopics);
      } catch (error) {
        console.error('Error loading topics:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (userEmail) {
      loadTopics();
    } else {
      setIsLoading(false);
    }
  }, [userEmail]);

  // Add window focus listener to reload topics when returning to dashboard
  useEffect(() => {
    const handleFocus = () => {
      if (userEmail) {
        console.log('🔄 Window focused, reloading topics to sync any changes...');
        reloadTopics();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [userEmail]);

  // Note: Individual topics are saved directly to localStorage with topic-${userEmail}-${topicId} keys
  // No need to save the topics array since we read from individual keys

  const filteredTopics = topics.filter(topic =>
    topic.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateTopic = async (title: string, content: string, sourceType?: string, fileExtension?: string) => {
    console.log('🎯 Creating topic:', { title, sourceType, fileExtension, contentLength: content.length });

    // Check if user can create a new topic (paywall enforcement)
    const topicCheck = checkTopicCreation(userEmail || undefined);
    if (!topicCheck.allowed) {
      toast.error(topicCheck.upgradeMessage || 'Topic creation limit reached');
      // Trigger paywall modal
      const event = new CustomEvent('showPaywall', {
        detail: {
          feature: 'Topic Creation',
          description: topicCheck.upgradeMessage,
          requiredPlan: 'basic'
        }
      });
      window.dispatchEvent(event);
      return;
    }

    // Determine topic type based on source and file extension
    let topicType = 'document';
    if (sourceType === 'youtube') {
      topicType = 'video';
    } else if (sourceType === 'audio' || title.includes('Recording')) {
      topicType = 'audio';
    } else if (fileExtension) {
      // Determine type based on file extension
      const ext = fileExtension.toLowerCase();
      if (['pdf'].includes(ext)) topicType = 'pdf';
      else if (['doc', 'docx'].includes(ext)) topicType = 'word';
      else if (['txt', 'md'].includes(ext)) topicType = 'text';
      else if (['mp3', 'wav', 'm4a', 'aac'].includes(ext)) topicType = 'audio';
      else if (['mp4', 'mov', 'avi', 'mkv'].includes(ext)) topicType = 'video';
      else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext)) topicType = 'image';
      else topicType = 'document';
    }

    console.log('🎯 Determined topic type:', topicType);

    const newTopic: Topic = {
      id: Date.now().toString(),
      title,
      type: topicType,
      date: new Date().toLocaleDateString(),
      collaborators: [userEmail || 'user'],
      userEmail: userEmail || 'anonymous', // Add user ownership
      sourceType, // Track the source type
      fileExtension // Track file extension for display
    };

    // Enforce paywall and increment usage
    const paywallResult = await enforcePaywall('fileUploads', 1, userEmail || undefined);
    if (!paywallResult.allowed) {
      toast.error(paywallResult.upgradeMessage || 'Topic creation limit reached');
      return;
    }

    // Save topic content to localStorage with user-specific key
    const topicContent = {
      id: newTopic.id,
      title,
      content,
      createdAt: new Date().toISOString(),
      userEmail: userEmail || 'anonymous'
    };

    localStorage.setItem(`topic-${userEmail || 'anonymous'}-${newTopic.id}`, JSON.stringify(topicContent));
    console.log('💾 Topic saved to localStorage:', newTopic.id);

    // Update local state to show the new topic immediately
    setTopics(prev => {
      const updatedTopics = [newTopic, ...prev];
      console.log('📋 Topics updated, new count:', updatedTopics.length);
      return updatedTopics;
    });
    setShowCreateModal(false);
    console.log('✅ Topic creation completed successfully!');

    // Show success message
    toast.success(`Topic "${title}" created successfully!`);
  };

  const handleFileUpload = async (fileName: string, content: string, fileSize?: number) => {
    console.log('📁 File upload handler called:', fileName, 'Content length:', content.length);

    // Check file size limit for free users
    if (fileSize) {
      const fileSizeCheck = checkFileUpload(fileSize, userEmail || undefined);
      if (!fileSizeCheck.allowed) {
        toast.error(fileSizeCheck.upgradeMessage || 'File size limit exceeded');
        // Trigger paywall modal
        const event = new CustomEvent('showPaywall', {
          detail: {
            feature: 'File Upload',
            description: fileSizeCheck.upgradeMessage,
            requiredPlan: 'basic'
          }
        });
        window.dispatchEvent(event);
        return;
      }
    }

    // Extract file extension from fileName
    const fileExtension = fileName.split('.').pop() || '';
    console.log('📁 File extension detected:', fileExtension);

    // Create topic with paywall enforcement
    await handleCreateTopic(fileName, content, 'file', fileExtension);
    console.log('📁 Topic creation called for file:', fileName);
  };

  const handleAudioRecording = async (audioBlob: Blob, transcript: string) => {
    // Check audio recording limits
    const audioCheck = checkAudioRecording(userEmail || undefined);
    if (!audioCheck.allowed) {
      toast.error(audioCheck.upgradeMessage || 'Audio recording limit reached');
      // Trigger paywall modal
      const event = new CustomEvent('showPaywall', {
        detail: {
          feature: 'Audio Recording',
          description: audioCheck.upgradeMessage,
          requiredPlan: 'basic'
        }
      });
      window.dispatchEvent(event);
      return;
    }

    if (transcript) {
      const fileName = `Recording - ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`;
      await handleCreateTopic(fileName, transcript, 'audio');

      // Increment audio usage
      await enforcePaywall('audioTranscriptions', 1, userEmail || undefined);
    }
  };

  const handleYouTubeUpload = (title: string, content: string, videoUrl?: string, metadata?: any) => {
    // Create topic with YouTube metadata
    const topicData = {
      title,
      content,
      sourceType: 'youtube',
      fileType: 'video/youtube',
      fileName: title,
      fileUrl: videoUrl,
      metadata
    };

    handleCreateTopic(title, content, 'youtube');
  };



  const getIcon = (type: string, isFolder?: boolean, sourceType?: string) => {
    if (isFolder) return FolderPlus;

    // Handle specific source types first
    if (sourceType === 'youtube') return Youtube;
    if (sourceType === 'audio') return Mic;

    switch (type) {
      case 'pdf': return FileText;
      case 'word': return FileText;
      case 'text': return File;
      case 'image': return Image;
      case 'audio': return Music;
      case 'video': return Video;
      case 'youtube': return Youtube;
      case 'document': return FileText;
      case 'session': return FileText;
      default: return FileText;
    }
  };

  const getTypeColor = (type: string, color?: string, sourceType?: string) => {
    if (color) {
      switch (color) {
        case 'blue': return 'bg-blue-100 text-blue-700';
        case 'green': return 'bg-green-100 text-green-700';
        case 'purple': return 'bg-purple-100 text-purple-700';
        default: return 'bg-gray-100 text-gray-700';
      }
    }

    // Handle specific source types first
    if (sourceType === 'youtube') return 'bg-red-100 text-red-700';
    if (sourceType === 'audio') return 'bg-green-100 text-green-700';

    switch (type) {
      case 'pdf': return 'bg-red-100 text-red-700';
      case 'word': return 'bg-blue-100 text-blue-700';
      case 'text': return 'bg-gray-100 text-gray-700';
      case 'image': return 'bg-purple-100 text-purple-700';
      case 'audio': return 'bg-green-100 text-green-700';
      case 'video': return 'bg-red-100 text-red-700';
      case 'youtube': return 'bg-red-100 text-red-700';
      case 'document': return 'bg-blue-100 text-blue-700';
      case 'session': return 'bg-purple-100 text-purple-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const groupedTopics = filteredTopics.reduce((acc, topic) => {
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();

    let group = 'Older';
    if (topic.date === 'Today' || topic.date.includes('Sep 16')) {
      group = 'Today';
    } else if (topic.date === 'Yesterday' || topic.date.includes('Sep 15')) {
      group = 'Yesterday';
    }

    if (!acc[group]) acc[group] = [];
    acc[group].push(topic);
    return acc;
  }, {} as Record<string, Topic[]>);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <DashboardHeader userEmail={userEmail} onSignOut={onSignOut} />

      <div className="container mx-auto px-6 lg:px-8 py-12">
        {/* Compact Header */}
        <div className="mb-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Study Library</h1>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between mb-8 gap-4">
          <div className="flex items-center gap-3 flex-1 max-w-lg">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search materials..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 bg-white border-gray-200 rounded-lg text-sm"
              />
            </div>
            <Button variant="outline" size="sm" className="rounded-lg">
              <Filter className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex items-center gap-3">
            <div className="hidden sm:block text-sm text-gray-600">
              Sort: <span className="font-medium">Last Accessed</span>
            </div>
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
            >
              <Plus className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">New Folder</span>
              <span className="sm:hidden">Folder</span>
            </Button>
          </div>
        </div>

        {/* Usage Limits Display for Free Users */}
        {subscription.plan === 'free' && (
          <div className="mb-6">
            <UsageLimitsDisplay
              userEmail={userEmail || undefined}
              onUpgrade={() => {
                // Trigger upgrade flow
                const event = new CustomEvent('showPaywall', {
                  detail: {
                    feature: 'Unlimited Access',
                    description: 'Upgrade to remove all limits and unlock premium features.',
                    requiredPlan: 'basic'
                  }
                });
                window.dispatchEvent(event);
              }}
              compact={true}
            />
          </div>
        )}

        {/* Upload Components - Responsive Layout */}
        <div className="mb-6 sm:mb-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* File Upload */}
            <div className="bg-white rounded-xl border border-gray-200 p-5 sm:p-6 min-h-[200px] sm:h-72 lg:h-80 flex flex-col">
              <FileUpload
                onFileProcessed={(fileName, content, fileSize) => {
                  handleFileUpload(fileName, content, fileSize);
                }}
                onMultipleFilesProcessed={(files) => {
                  files.forEach(file => handleFileUpload(file.name, file.content, file.size));
                }}
                allowMultiple={true}
                className="border-0 shadow-none compact flex-1"
              />
            </div>

            {/* Audio Recorder */}
            <div className="bg-white rounded-xl border border-gray-200 p-5 sm:p-6 min-h-[200px] sm:h-72 lg:h-80 flex flex-col">
              <AudioRecorder
                onRecordingComplete={handleAudioRecording}
                onRecordingSaved={(blob, duration, transcript) => {
                  window.location.reload();
                }}
                userEmail={userEmail}
                className="border-0 shadow-none compact flex-1"
              />
            </div>

            {/* YouTube Processor */}
            <div className="bg-white rounded-xl border border-gray-200 p-5 sm:p-6 min-h-[200px] sm:h-72 lg:h-80 flex flex-col">
              <YouTubeProcessor
                onVideoProcessed={(title, content, videoUrl, metadata) => {
                  handleYouTubeUpload(title, content, videoUrl, metadata);
                }}
                className="border-0 shadow-none compact flex-1"
              />
            </div>

            {/* Text Input */}
            <div className="bg-white rounded-xl border border-gray-200 p-5 sm:p-6 min-h-[200px] sm:h-72 lg:h-80 flex flex-col">
              <SmartTextInput
                onTextProcessed={(title, content, contentType) => {
                  handleCreateTopic(title, content, 'text', contentType);
                }}
                className="border-0 shadow-none compact flex-1"
              />
            </div>
          </div>
        </div>

        {/* Audio Library - Collapsible */}
        <div className="mb-12">
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <button
              onClick={() => setShowAudioLibrary(!showAudioLibrary)}
              className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <Music className="w-5 h-5 text-green-600" />
                <h3 className="font-semibold text-gray-900">Audio Library</h3>
                <Badge variant="secondary" className="text-xs">
                  {/* You can add count here if needed */}
                </Badge>
              </div>
              <div className={`transform transition-transform ${showAudioLibrary ? 'rotate-180' : ''}`}>
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </button>
            {showAudioLibrary && (
              <div className="border-t border-gray-200">
                <AudioLibrary userEmail={userEmail} />
              </div>
            )}
          </div>
        </div>



        {/* Library Content */}
        <div className="space-y-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
              <span className="ml-3 text-gray-600">Loading your library...</span>
            </div>
          ) : Object.keys(groupedTopics).length === 0 ? (
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <FileText className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No study materials yet</h3>
              <p className="text-gray-600 mb-6">
                Start by uploading a document, recording audio, or creating your first topic
              </p>
              <Button
                onClick={() => setShowCreateModal(true)}
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Topic
              </Button>
            </div>
          ) : (
            Object.entries(groupedTopics).map(([group, groupTopics]) => (
              <div key={group}>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">{group}</h3>
                <div className="space-y-2">
                  {groupTopics.map((topic) => {
                    const Icon = getIcon(topic.type, topic.isFolder, topic.sourceType);
                    return (
                      <Card
                        key={topic.id}
                        className="hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => onSelectTopic(topic.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${getTypeColor(topic.type, topic.color, topic.sourceType)}`}>
                                <Icon className="w-5 h-5" />
                              </div>
                              <div>
                                <h4 className="font-medium text-gray-900">{topic.title}</h4>
                                {topic.isFolder && topic.itemCount && (
                                  <p className="text-sm text-gray-500">({topic.itemCount})</p>
                                )}
                                {/* Show file type badge for uploaded files */}
                                {topic.fileExtension && (
                                  <Badge variant="secondary" className="text-xs mt-1">
                                    {topic.fileExtension.toUpperCase()}
                                  </Badge>
                                )}
                                {/* Show source type badge */}
                                {topic.sourceType === 'youtube' && (
                                  <Badge variant="secondary" className="text-xs mt-1 bg-red-100 text-red-700">
                                    YouTube
                                  </Badge>
                                )}
                                {topic.sourceType === 'audio' && (
                                  <Badge variant="secondary" className="text-xs mt-1 bg-green-100 text-green-700">
                                    Recording
                                  </Badge>
                                )}
                              </div>
                            </div>

                            <div className="flex items-center gap-4">
                              <span className="text-sm text-gray-500">{topic.date}</span>
                              {topic.collaborators && (
                                <div className="flex -space-x-2">
                                  {topic.collaborators.slice(0, 3).map((_, index) => (
                                    <div
                                      key={index}
                                      className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 border-2 border-white"
                                    />
                                  ))}
                                </div>
                              )}
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            ))
          )}
        </div>

        <CreateTopicModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onCreateTopic={handleCreateTopic}
        />


      </div>
    </div>
  );
};
