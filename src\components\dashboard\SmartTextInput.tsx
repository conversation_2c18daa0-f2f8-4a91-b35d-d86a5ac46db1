import React, { useState, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { FileText, Loader2, <PERSON>, Sparkles, Brain } from 'lucide-react';
import { toast } from 'sonner';

interface SmartTextInputProps {
  onTextProcessed: (title: string, content: string, contentType?: string) => void;
  className?: string;
}

const SmartTextInput: React.FC<SmartTextInputProps> = ({ onTextProcessed, className }) => {
  const [text, setText] = useState('');
  const [title, setTitle] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const detectContentType = (content: string): string => {
    const lowerContent = content.toLowerCase();
    
    if (lowerContent.includes('meeting') || lowerContent.includes('agenda') || lowerContent.includes('action items')) {
      return 'meeting';
    }
    if (lowerContent.includes('lecture') || lowerContent.includes('chapter') || lowerContent.includes('lesson')) {
      return 'lecture';
    }
    if (lowerContent.includes('research') || lowerContent.includes('study') || lowerContent.includes('analysis')) {
      return 'research';
    }
    if (lowerContent.includes('notes') || lowerContent.includes('summary') || lowerContent.includes('key points')) {
      return 'notes';
    }
    
    return 'general';
  };

  const generateSmartTitle = (content: string): string => {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length === 0) return 'Untitled Document';

    // Try to find a title-like line (first non-empty line or line with title indicators)
    const firstLine = lines[0].trim();
    
    // If first line looks like a title (short, no punctuation at end except :)
    if (firstLine.length < 100 && !firstLine.endsWith('.') && !firstLine.endsWith('!') && !firstLine.endsWith('?')) {
      return firstLine.replace(/^#+\s*/, ''); // Remove markdown headers
    }

    // Extract key phrases for auto-title
    const contentType = detectContentType(content);
    const words = content.split(/\s+/).slice(0, 50).join(' ');
    
    // Generate title based on content type and first few words
    switch (contentType) {
      case 'meeting':
        return `Meeting Notes - ${new Date().toLocaleDateString()}`;
      case 'lecture':
        return `Lecture Notes - ${firstLine.substring(0, 50)}...`;
      case 'research':
        return `Research Notes - ${firstLine.substring(0, 50)}...`;
      default:
        return firstLine.length > 50 ? `${firstLine.substring(0, 50)}...` : firstLine || 'Text Document';
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    const pastedText = e.clipboardData.getData('text');
    if (pastedText.length > 100) {
      // Auto-generate title for substantial content
      const smartTitle = generateSmartTitle(pastedText);
      setTitle(smartTitle);
      
      // Show content type detection
      const contentType = detectContentType(pastedText);
      if (contentType !== 'general') {
        toast.success(`📝 Detected ${contentType} content - auto-generated title`);
      }
    }
  };

  const handleTextChange = (value: string) => {
    setText(value);
    
    // Auto-generate title if text is substantial and no title is set
    if (value.length > 200 && !title.trim()) {
      const smartTitle = generateSmartTitle(value);
      setTitle(smartTitle);
    }
  };

  const handleQuickCreate = async () => {
    if (!text.trim()) {
      toast.error('Please enter some text content');
      return;
    }

    setIsProcessing(true);

    try {
      const finalTitle = title.trim() || generateSmartTitle(text);
      const contentType = detectContentType(text);
      
      onTextProcessed(finalTitle, text, contentType);
      
      // Clear form
      setText('');
      setTitle('');
      
      toast.success(`✅ Created topic: ${finalTitle}`);
    } catch (error) {
      console.error('Failed to create topic:', error);
      toast.error('Failed to create topic');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.ctrlKey && e.key === 'Enter' && !isProcessing) {
      handleQuickCreate();
    }
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'meeting': return '🤝';
      case 'lecture': return '🎓';
      case 'research': return '🔬';
      case 'notes': return '📝';
      default: return '📄';
    }
  };

  const contentType = detectContentType(text);

  return (
    <Card className={`border shadow-sm bg-gradient-to-br from-green-500 to-blue-600 text-white hover:shadow-md transition-all duration-200 ${className || ''}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="p-2 bg-white/20 rounded-lg">
            <Type className="w-5 h-5" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-sm">Smart Text Input</h3>
            <p className="text-green-100 text-xs">Paste or type content directly</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-white/80 hover:text-white hover:bg-white/20"
          >
            <Sparkles className="w-4 h-4" />
          </Button>
        </div>

        <div className="space-y-3">
          {showAdvanced && (
            <Input
              placeholder="Topic title (auto-generated if empty)"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="bg-white/20 border-white/30 text-white placeholder:text-white/70 focus:bg-white/30 focus:border-white/50"
            />
          )}

          <div className="relative">
            <Textarea
              ref={textareaRef}
              placeholder="Paste your text here... (meeting notes, lecture content, research, etc.)"
              value={text}
              onChange={(e) => handleTextChange(e.target.value)}
              onPaste={handlePaste}
              onKeyDown={handleKeyPress}
              disabled={isProcessing}
              className="min-h-[120px] bg-white/20 border-white/30 text-white placeholder:text-white/70 focus:bg-white/30 focus:border-white/50 resize-none"
            />
            
            {text.length > 0 && (
              <div className="absolute bottom-2 right-2 text-xs text-white/60">
                {text.length} characters
              </div>
            )}
          </div>

          {text.length > 50 && (
            <div className="flex items-center gap-2 text-xs text-white/80 bg-white/10 rounded p-2">
              <span>{getContentTypeIcon(contentType)}</span>
              <span>Detected: {contentType} content</span>
              {!title && (
                <span className="text-white/60">• Auto-title will be generated</span>
              )}
            </div>
          )}

          <div className="flex gap-2">
            <Button
              onClick={handleQuickCreate}
              disabled={isProcessing || !text.trim()}
              className="flex-1 bg-white/20 hover:bg-white/30 text-white border-0 h-8 text-xs"
              size="sm"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Brain className="w-3 h-3 mr-1" />
                  Create Topic
                </>
              )}
            </Button>
            
            {text.length > 0 && (
              <Button
                onClick={() => {
                  setText('');
                  setTitle('');
                }}
                variant="ghost"
                size="sm"
                className="text-white/80 hover:text-white hover:bg-white/20 h-8 px-3"
              >
                Clear
              </Button>
            )}
          </div>

          <div className="text-xs text-white/60 text-center">
            Tip: Ctrl+Enter to quick create • Auto-detects content type
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SmartTextInput;
