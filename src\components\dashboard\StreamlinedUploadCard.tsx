import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface StreamlinedUploadCardProps {
  icon: LucideIcon;
  title: string;
  subtitle: string;
  onClick?: () => void;
  className?: string;
  iconColor?: string;
  bgColor?: string;
}

const StreamlinedUploadCard: React.FC<StreamlinedUploadCardProps> = ({
  icon: Icon,
  title,
  subtitle,
  onClick,
  className = '',
  iconColor = 'text-gray-600',
  bgColor = 'bg-gray-50'
}) => {
  return (
    <Card
      className={`group cursor-pointer border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-200 bg-white rounded-xl ${className}`}
      onClick={onClick}
    >
      <CardContent className="p-8 text-center">
        <div className={`w-16 h-16 ${bgColor} rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-all duration-200`}>
          <Icon className={`w-8 h-8 ${iconColor}`} />
        </div>
        <h3 className="font-semibold text-gray-900 text-lg mb-3 group-hover:text-gray-700 transition-colors duration-200">{title}</h3>
        <p className="text-sm text-gray-600 leading-relaxed">{subtitle}</p>
      </CardContent>
    </Card>
  );
};

export default StreamlinedUploadCard;
