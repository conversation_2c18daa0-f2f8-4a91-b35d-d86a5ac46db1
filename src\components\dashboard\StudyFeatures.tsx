import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Brain, 
  FileText, 
  HelpCircle, 
  Layers, 
  Bot, 
  Eye, 
  Mic, 
  GraduationCap,
  Phone,
  Headphones,
  Video,
  Calendar,
  Sparkles,
  Clock,
  Target,
  Zap
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import SparkEAI from './SparkEAI';

interface StudyFeaturesProps {
  studyMaterial?: string;
  onFeatureSelect: (feature: string, options?: any) => void;
}

const StudyFeatures = ({ studyMaterial, onFeatureSelect }: StudyFeaturesProps) => {
  const [showSparkE, setShowSparkE] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);

  const features = [
    {
      id: 'notes',
      title: 'Smart Notes',
      description: 'Generate comprehensive study notes from your materials with AI.',
      icon: FileText,
      color: 'from-blue-500 to-blue-600',
      category: 'Content Generation',
      functional: true
    },
    {
      id: 'flashcards',
      title: 'Interactive Flashcards',
      description: 'Create customizable flashcards with flip animations and progress tracking.',
      icon: Layers,
      color: 'from-purple-500 to-purple-600',
      category: 'Content Generation',
      functional: true
    },
    {
      id: 'quiz',
      title: 'Smart Quizzes',
      description: 'Generate practice quizzes with multiple choice, true/false, and scoring.',
      icon: HelpCircle,
      color: 'from-orange-500 to-orange-600',
      category: 'Content Generation',
      functional: true
    },
    {
      id: 'sparke',
      title: 'AI Chat Tutor',
      description: 'Chat with your personal AI tutor about your study materials.',
      icon: Bot,
      color: 'from-indigo-500 to-purple-600',
      category: 'AI Tutor',
      special: true,
      functional: true
    },
    {
      id: 'record',
      title: 'Audio Recording',
      description: 'Record lectures and convert them to study materials automatically.',
      icon: Mic,
      color: 'from-red-500 to-red-600',
      category: 'Recording',
      functional: true
    },
    {
      id: 'upload',
      title: 'File Upload',
      description: 'Upload PDFs, documents, images, and videos for AI processing.',
      icon: FileText,
      color: 'from-green-500 to-green-600',
      category: 'Content Input',
      functional: true
    },
    {
      id: 'summary',
      title: 'Smart Summary',
      description: 'Get concise summaries of your study materials with key points.',
      icon: Brain,
      color: 'from-pink-500 to-pink-600',
      category: 'Content Generation',
      functional: true
    },
    {
      id: 'calendar',
      title: 'Study Planner',
      description: 'Plan your study sessions and track your learning progress.',
      icon: Calendar,
      color: 'from-emerald-500 to-emerald-600',
      category: 'Planning',
      functional: true
    }
  ];

  const memoryTechniques = [
    {
      title: 'Speed Learning',
      description: 'Learn 20 lessons in one night with proven techniques',
      icon: Zap,
      tips: [
        'Use the Pomodoro Technique (25 min study, 5 min break)',
        'Create mind maps for visual learning',
        'Use spaced repetition for better retention',
        'Teach concepts out loud to yourself',
        'Use mnemonics for complex information'
      ]
    },
    {
      title: 'Memory Palace',
      description: 'Ancient technique for memorizing large amounts of information',
      icon: Brain,
      tips: [
        'Choose a familiar location (your house)',
        'Create a specific route through the location',
        'Place information at specific points along the route',
        'Use vivid, unusual imagery',
        'Practice walking through your palace regularly'
      ]
    },
    {
      title: 'Active Recall',
      description: 'Test yourself instead of just re-reading',
      icon: Target,
      tips: [
        'Close your notes and try to recall information',
        'Use flashcards for key concepts',
        'Explain concepts without looking at notes',
        'Create practice tests for yourself',
        'Use the Feynman Technique'
      ]
    }
  ];

  const handleFeatureClick = (feature: any) => {
    if (feature.id === 'sparke') {
      setShowSparkE(true);
    } else {
      onFeatureSelect(feature.id, { studyMaterial });
    }
  };

  const groupedFeatures = features.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {} as Record<string, typeof features>);

  return (
    <div className="space-y-8">
      {/* Memory Techniques Section */}
      <div>
        <div className="flex items-center gap-2 mb-6">
          <Brain className="w-6 h-6 text-indigo-600" />
          <h2 className="text-2xl font-bold text-gray-800">Memory Techniques</h2>
          <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
            <Sparkles className="w-3 h-3 mr-1" />
            Pro Tips
          </Badge>
        </div>
        
        <div className="grid md:grid-cols-3 gap-4 mb-8">
          {memoryTechniques.map((technique, index) => (
            <Card key={index} className="border-0 shadow-lg bg-gradient-to-br from-yellow-50 to-orange-50 hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg">
                    <technique.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{technique.title}</CardTitle>
                    <p className="text-sm text-gray-600">{technique.description}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {technique.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} className="text-sm text-gray-700 flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                      {tip}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* AI Study Features */}
      {Object.entries(groupedFeatures).map(([category, categoryFeatures]) => (
        <div key={category}>
          <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
            {category === 'AI Tutor' && <Bot className="w-5 h-5 text-indigo-600" />}
            {category === 'Content Generation' && <FileText className="w-5 h-5 text-blue-600" />}
            {category === 'Recording' && <Mic className="w-5 h-5 text-red-600" />}
            {category === 'Assessment' && <GraduationCap className="w-5 h-5 text-teal-600" />}
            {category === 'Planning' && <Calendar className="w-5 h-5 text-emerald-600" />}
            {category}
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categoryFeatures.map((feature) => (
              <Card 
                key={feature.id}
                className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group ${
                  feature.special ? 'ring-2 ring-indigo-200' : ''
                }`}
                onClick={() => handleFeatureClick(feature)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={`p-3 bg-gradient-to-r ${feature.color} rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                        {feature.title}
                        {feature.functional ? (
                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                            ✓ Ready
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-xs">
                            Coming Soon
                          </Badge>
                        )}
                        {feature.special && (
                          <Badge className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs">
                            <Sparkles className="w-3 h-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </h4>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ))}

      {/* Spark.E AI Dialog */}
      <Dialog open={showSparkE} onOpenChange={setShowSparkE}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <SparkEAI 
            studyMaterial={studyMaterial} 
            onClose={() => setShowSparkE(false)} 
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StudyFeatures;
