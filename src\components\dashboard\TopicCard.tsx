
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BookOpen, Calendar, ChevronRight, FileText, Brain, HelpCircle, Layers } from 'lucide-react';

interface TopicCardProps {
  id: string;
  title: string;
  contentCount: {
    notes: number;
    summary: number;
    quiz: number;
    flashcards: number;
  };
  createdAt: string;
  onClick: () => void;
}

const TopicCard = ({ title, contentCount, createdAt, onClick }: TopicCardProps) => {
  const totalContent = Object.values(contentCount).reduce((sum, count) => sum + count, 0);

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'notes': return <FileText className="w-3 h-3" />;
      case 'summary': return <Layers className="w-3 h-3" />;
      case 'quiz': return <HelpCircle className="w-3 h-3" />;
      case 'flashcards': return <Brain className="w-3 h-3" />;
      default: return null;
    }
  };

  return (
    <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer border-0 bg-white/80 backdrop-blur-sm hover:bg-white hover:scale-[1.02]" onClick={onClick}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold text-gray-800 line-clamp-2 leading-tight">
                {title}
              </CardTitle>
              <div className="flex items-center gap-2 text-sm text-gray-500 mt-2">
                <Calendar className="w-4 h-4" />
                {new Date(createdAt).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                })}
              </div>
            </div>
          </div>
          <Button variant="ghost" size="sm" className="group-hover:bg-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity">
            <ChevronRight className="w-4 h-4 text-indigo-600" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">AI Generated Content</span>
            <Badge variant="secondary" className="bg-gradient-to-r from-indigo-50 to-purple-50 text-indigo-700 border-indigo-200">
              {totalContent} {totalContent === 1 ? 'item' : 'items'}
            </Badge>
          </div>

          {totalContent > 0 ? (
            <div className="grid grid-cols-2 gap-2">
              {contentCount.notes > 0 && (
                <div className="flex items-center gap-2 bg-blue-50 text-blue-700 px-3 py-2 rounded-lg text-sm">
                  {getContentIcon('notes')}
                  <span>{contentCount.notes} Notes</span>
                </div>
              )}
              {contentCount.summary > 0 && (
                <div className="flex items-center gap-2 bg-green-50 text-green-700 px-3 py-2 rounded-lg text-sm">
                  {getContentIcon('summary')}
                  <span>{contentCount.summary} Summary</span>
                </div>
              )}
              {contentCount.quiz > 0 && (
                <div className="flex items-center gap-2 bg-orange-50 text-orange-700 px-3 py-2 rounded-lg text-sm">
                  {getContentIcon('quiz')}
                  <span>{contentCount.quiz} Quiz</span>
                </div>
              )}
              {contentCount.flashcards > 0 && (
                <div className="flex items-center gap-2 bg-purple-50 text-purple-700 px-3 py-2 rounded-lg text-sm">
                  {getContentIcon('flashcards')}
                  <span>{contentCount.flashcards} Cards</span>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-4">
              <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-2">
                <Brain className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-sm text-gray-500">Ready for AI processing</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TopicCard;
