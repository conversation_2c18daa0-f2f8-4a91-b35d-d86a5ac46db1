import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MessageSquare, 
  Upload, 
  Mic, 
  HardDrive, 
  Calculator,
  Video,
  Files,
  Image,
  Crown,
  Zap,
  Star
} from 'lucide-react';
import { 
  getUserSubscription, 
  getUserUsage, 
  getPlanLimits, 
  getUsagePercentage,
  getRemainingUsage 
} from '@/services/subscriptionService';

interface UsageDashboardProps {
  onUpgrade: () => void;
  userEmail?: string;
}

const planIcons = {
    free: <Star className="w-4 h-4" />,
    monthly: <Star className="w-4 h-4" />,
    yearly: <Zap className="w-4 h-4" />,
    premium: <Crown className="w-4 h-4" />
  };

const planColors = {
    free: 'bg-gray-500',
    monthly: 'bg-blue-500',
    yearly: 'bg-purple-500',
    premium: 'bg-yellow-500'
  };

export const UsageDashboard: React.FC<UsageDashboardProps> = ({ onUpgrade, userEmail }) => {
  const subscription = getUserSubscription();
  const usage = getUserUsage();
  const limits = getPlanLimits(subscription.plan);

  const usageItems = [
    {
      icon: <MessageSquare className="w-5 h-5" />,
      label: 'AI Questions',
      key: 'aiQuestions' as const,
      color: 'text-blue-600'
    },
    {
      icon: <Upload className="w-5 h-5" />,
      label: 'File Uploads',
      key: 'fileUploads' as const,
      color: 'text-green-600'
    },
    {
      icon: <Mic className="w-5 h-5" />,
      label: 'Audio Transcriptions',
      key: 'audioTranscriptions' as const,
      color: 'text-purple-600'
    },
    {
      icon: <HardDrive className="w-5 h-5" />,
      label: 'Library Storage',
      key: 'libraryStorage' as const,
      color: 'text-orange-600',
      unit: 'MB'
    }
  ];

  const features = [
    {
      icon: <Calculator className="w-5 h-5" />,
      label: 'AI Math Expert',
      key: 'mathExpert' as const,
      color: 'text-indigo-600'
    },
    {
      icon: <Video className="w-5 h-5" />,
      label: 'Live Recording',
      key: 'liveRecording' as const,
      color: 'text-red-600'
    },
    {
      icon: <Files className="w-5 h-5" />,
      label: 'Multiple Files/Session',
      key: 'multipleFiles' as const,
      color: 'text-teal-600'
    },
    {
      icon: <Image className="w-5 h-5" />,
      label: 'Image Analysis',
      key: 'imageAnalysis' as const,
      color: 'text-pink-600'
    }
  ];

  const formatUsage = (value: number, unit?: string) => {
    if (unit === 'MB') {
      if (value >= 1024) {
        return `${(value / 1024).toFixed(1)} GB`;
      }
      return `${value} MB`;
    }
    return value.toString();
  };

  const formatLimit = (value: number | boolean, unit?: string) => {
    if (typeof value === 'boolean') {
      return value ? 'Available' : 'Not Available';
    }
    if (value === -1) return 'Unlimited';
    if (unit === 'MB') {
      if (value >= 1024) {
        return `${(value / 1024).toFixed(1)} GB`;
      }
      return `${value} MB`;
    }
    return value.toString();
  };

  const isAdmin = userEmail === '<EMAIL>';

  return (
    <div className="space-y-6">
      {/* Plan Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              {planIcons[subscription.plan]}
              Current Plan
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge className={`${planColors[subscription.plan]} text-white capitalize`}>
                {subscription.plan}
              </Badge>
              {subscription.plan === 'free' && (
                <Button onClick={onUpgrade} size="sm">
                  Upgrade
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Status</p>
              <p className="font-semibold capitalize">{subscription.status}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Next Billing</p>
              <p className="font-semibold">
                {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
              </p>
            </div>
          </div>
          {isAdmin && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800 font-medium">
                👑 Admin Access - All features unlocked
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Usage This Month</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {usageItems.map((item) => {
              const currentUsage = usage[item.key];
              const limit = limits[item.key as keyof typeof limits];
              const percentage = getUsagePercentage(item.key);
              const remaining = getRemainingUsage(item.key);

              if (typeof limit === 'boolean') return null;

              return (
                <div key={item.key} className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className={item.color}>{item.icon}</span>
                    <span className="font-medium">{item.label}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span>
                      {formatUsage(currentUsage, item.unit)} / {formatLimit(limit as number, item.unit)}
                    </span>
                    <span className="text-gray-500">
                      {remaining === 'Unlimited' ? 'Unlimited' : `${remaining} remaining`}
                    </span>
                  </div>
                  
                  {limit !== -1 && (
                    <Progress 
                      value={percentage} 
                      className={`h-2 ${percentage > 80 ? 'text-red-500' : percentage > 60 ? 'text-yellow-500' : 'text-green-500'}`}
                    />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Feature Access */}
      <Card>
        <CardHeader>
          <CardTitle>Feature Access</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {features.map((feature) => {
              const hasAccess = limits[feature.key] === true || isAdmin;
              
              return (
                <div key={feature.key} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-2">
                    <span className={feature.color}>{feature.icon}</span>
                    <span className="font-medium">{feature.label}</span>
                  </div>
                  <Badge variant={hasAccess ? 'default' : 'secondary'}>
                    {hasAccess ? 'Available' : 'Upgrade Required'}
                  </Badge>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Upgrade CTA for free users */}
      {subscription.plan === 'free' && !isAdmin && (
        <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-purple-900 mb-2">
              Unlock Your Full Potential
            </h3>
            <p className="text-purple-700 mb-4">
              Upgrade to get unlimited access to all AI-powered study tools
            </p>
            <Button 
              onClick={onUpgrade}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 text-white"
            >
              View Pricing Plans
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UsageDashboard;
