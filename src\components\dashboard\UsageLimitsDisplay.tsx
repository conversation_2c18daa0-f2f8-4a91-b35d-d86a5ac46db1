import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Mic, 
  Upload, 
  Brain, 
  Crown, 
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { getUserSubscription, getUserUsage } from '@/services/subscriptionService';
import { getFreeTierLimits } from '@/services/paywallService';

interface UsageLimitsDisplayProps {
  userEmail?: string;
  onUpgrade?: () => void;
  compact?: boolean;
}

export const UsageLimitsDisplay: React.FC<UsageLimitsDisplayProps> = ({
  userEmail,
  onUpgrade,
  compact = false
}) => {
  const subscription = getUserSubscription();
  const usage = getUserUsage();
  const limits = getFreeTierLimits();

  // Don't show for premium users
  if (subscription.plan !== 'free') {
    return null;
  }

  const usageItems = [
    {
      label: 'Study Topics',
      icon: <FileText className="w-4 h-4" />,
      used: usage.fileUploads,
      limit: limits.topics,
      color: 'blue'
    },
    {
      label: 'Recording Time',
      icon: <Mic className="w-4 h-4" />,
      used: usage.audioTranscriptions,
      limit: 1, // Represents 5 minutes
      color: 'green',
      displayText: usage.audioTranscriptions >= 1 ? '5 min used' : '5 min available'
    },
    {
      label: 'AI Generations',
      icon: <Brain className="w-4 h-4" />,
      used: usage.aiQuestions,
      limit: limits.aiGenerations,
      color: 'purple'
    }
  ];

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const getStatusIcon = (used: number, limit: number) => {
    if (used >= limit) {
      return <AlertTriangle className="w-4 h-4 text-red-500" />;
    }
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  if (compact) {
    return (
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Crown className="w-4 h-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">Free Plan Usage</span>
            </div>
            <Button 
              size="sm" 
              onClick={onUpgrade}
              className="bg-orange-600 hover:bg-orange-700 text-white"
            >
              Upgrade
            </Button>
          </div>
          
          <div className="grid grid-cols-3 gap-3">
            {usageItems.map((item, index) => {
              const percentage = Math.min((item.used / item.limit) * 100, 100);
              return (
                <div key={index} className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    {item.icon}
                  </div>
                  <div className="text-xs text-gray-600 mb-1">{item.label}</div>
                  <div className="text-xs font-medium">
                    {item.displayText || `${item.used}/${item.limit}`}
                  </div>
                  <Progress 
                    value={percentage} 
                    className="h-1 mt-1"
                  />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-orange-200 bg-gradient-to-r from-orange-50 to-yellow-50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Crown className="w-5 h-5 text-orange-600" />
            Free Plan Usage
          </CardTitle>
          <Badge variant="outline" className="border-orange-300 text-orange-700">
            Free Tier
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {usageItems.map((item, index) => {
          const percentage = Math.min((item.used / item.limit) * 100, 100);
          const isAtLimit = item.used >= item.limit;
          
          return (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {item.icon}
                  <span className="font-medium text-gray-700">{item.label}</span>
                  {getStatusIcon(item.used, item.limit)}
                </div>
                <span className="text-sm text-gray-600">
                  {item.displayText || `${item.used} / ${item.limit}`}
                </span>
              </div>
              
              <div className="space-y-1">
                <Progress 
                  value={percentage} 
                  className="h-2"
                />
                {isAtLimit && (
                  <p className="text-xs text-red-600 flex items-center gap-1">
                    <AlertTriangle className="w-3 h-3" />
                    Limit reached - upgrade to continue
                  </p>
                )}
              </div>
            </div>
          );
        })}
        
        <div className="pt-3 border-t border-orange-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-700">Want unlimited access?</p>
              <p className="text-xs text-gray-600">Upgrade to remove all limits</p>
            </div>
            <Button 
              onClick={onUpgrade}
              className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white"
            >
              <Crown className="w-4 h-4 mr-2" />
              Upgrade Now
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UsageLimitsDisplay;
