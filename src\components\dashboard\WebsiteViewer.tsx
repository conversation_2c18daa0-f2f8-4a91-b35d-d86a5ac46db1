import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Globe,
  ExternalLink,
  RefreshCw,
  Maximize2,
  Minimize2,
  Brain,
  BookOpen,
  Zap,
  HelpCircle,
  X
} from 'lucide-react';
import { toast } from 'sonner';

interface WebsiteViewerProps {
  url: string;
  title?: string;
  onGenerateContent: (type: 'notes' | 'summary' | 'flashcards' | 'quiz') => void;
  onClose: () => void;
  isProcessing?: boolean;
}

const WebsiteViewer: React.FC<WebsiteViewerProps> = ({
  url,
  title,
  onGenerateContent,
  onClose,
  isProcessing = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const openInNewTab = () => {
    window.open(url, '_blank', 'noopener,noref<PERSON>rer');
  };

  const getDisplayUrl = () => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return url;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className={`w-full max-h-[90vh] overflow-hidden ${isExpanded ? 'max-w-7xl' : 'max-w-5xl'}`}>
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Globe className="w-6 h-6 text-blue-500" />
              <div>
                <CardTitle className="text-lg">{title || 'Website'}</CardTitle>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge variant="secondary" className="flex items-center space-x-1">
                    <Globe className="w-3 h-3" />
                    <span>{getDisplayUrl()}</span>
                  </Badge>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={openInNewTab}
                size="sm"
                variant="outline"
              >
                <ExternalLink className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => setIsExpanded(!isExpanded)}
                size="sm"
                variant="outline"
              >
                {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <div className="flex">
            {/* Website Preview */}
            <div className={`${isExpanded ? 'w-3/4' : 'w-full'} relative`}>
              <div className="h-96 flex items-center justify-center bg-gray-50">
                <div className="text-center max-w-md">
                  <Globe className="w-16 h-16 mx-auto mb-6 text-blue-500" />
                  <h3 className="font-semibold text-gray-800 mb-3 text-lg">Website Ready for AI Analysis</h3>
                  <p className="text-sm text-gray-600 mb-6">
                    Due to browser security restrictions, we can't display this website directly.
                    However, our AI can still analyze and extract content from the URL.
                  </p>
                  <div className="space-y-3">
                    <Button onClick={openInNewTab} variant="outline" className="w-full">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Open Website in New Tab
                    </Button>
                    <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded-lg">
                      <strong>URL:</strong> {url}
                    </div>
                  </div>
                  <p className="text-xs text-green-600 mt-4 font-medium">
                    ✓ Ready for AI content generation
                  </p>
                </div>
              </div>
            </div>

            {/* AI Generation Panel */}
            <div className={`${isExpanded ? 'w-1/4 border-l' : 'hidden'} p-4 bg-gray-50`}>
              <h3 className="font-semibold mb-4">AI Analysis</h3>
              <div className="space-y-3">
                <Button
                  onClick={() => onGenerateContent('notes')}
                  className="w-full justify-start bg-blue-500 hover:bg-blue-600"
                  disabled={isProcessing}
                >
                  <Brain className="w-4 h-4 mr-2" />
                  Generate Notes
                </Button>

                <Button
                  onClick={() => onGenerateContent('summary')}
                  className="w-full justify-start bg-green-500 hover:bg-green-600"
                  disabled={isProcessing}
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  Generate Summary
                </Button>

                <Button
                  onClick={() => onGenerateContent('flashcards')}
                  className="w-full justify-start bg-purple-500 hover:bg-purple-600"
                  disabled={isProcessing}
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Generate Flashcards
                </Button>

                <Button
                  onClick={() => onGenerateContent('quiz')}
                  className="w-full justify-start bg-orange-500 hover:bg-orange-600"
                  disabled={isProcessing}
                >
                  <HelpCircle className="w-4 h-4 mr-2" />
                  Generate Quiz
                </Button>
              </div>

              {isProcessing && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />
                    <span className="text-sm text-blue-700">Processing with AI...</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Bottom AI Controls (when not expanded) */}
          {!isExpanded && (
            <div className="border-t p-4 bg-gray-50">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button
                  onClick={() => onGenerateContent('notes')}
                  className="bg-blue-500 hover:bg-blue-600"
                  disabled={isProcessing}
                >
                  <Brain className="w-4 h-4 mr-2" />
                  AI Notes
                </Button>

                <Button
                  onClick={() => onGenerateContent('summary')}
                  className="bg-green-500 hover:bg-green-600"
                  disabled={isProcessing}
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  AI Summary
                </Button>

                <Button
                  onClick={() => onGenerateContent('flashcards')}
                  className="bg-purple-500 hover:bg-purple-600"
                  disabled={isProcessing}
                >
                  <Zap className="w-4 h-4 mr-2" />
                  AI Flashcards
                </Button>

                <Button
                  onClick={() => onGenerateContent('quiz')}
                  className="bg-orange-500 hover:bg-orange-600"
                  disabled={isProcessing}
                >
                  <HelpCircle className="w-4 h-4 mr-2" />
                  AI Quiz
                </Button>
              </div>

              {isProcessing && (
                <div className="mt-3 text-center">
                  <div className="inline-flex items-center space-x-2 text-blue-600">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Analyzing website with AI...</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default WebsiteViewer;
