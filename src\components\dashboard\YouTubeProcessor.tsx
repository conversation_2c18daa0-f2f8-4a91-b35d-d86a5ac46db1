import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Youtube, Loader2, Link, Play } from 'lucide-react';
import { toast } from 'sonner';
import { processYouTubeVideo } from '@/services/youtubeService';

interface YouTubeProcessorProps {
  onVideoProcessed: (title: string, content: string, videoUrl: string, metadata?: any) => void;
  className?: string;
}

const YouTubeProcessor: React.FC<YouTubeProcessorProps> = ({ onVideoProcessed, className }) => {
  const [url, setUrl] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const isValidYouTubeUrl = (url: string): boolean => {
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/;
    return youtubeRegex.test(url);
  };

  const extractVideoId = (url: string): string => {
    const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/;
    const match = url.match(regex);
    return match ? match[1] : '';
  };

  const handleProcess = async () => {
    if (!url.trim()) {
      toast.error('Please enter a YouTube URL');
      return;
    }

    if (!isValidYouTubeUrl(url)) {
      toast.error('Please enter a valid YouTube URL');
      return;
    }

    setIsProcessing(true);

    try {
      console.log('🎬 Processing YouTube video:', url);
      
      const videoId = extractVideoId(url);
      const result = await processYouTubeVideo(url);

      if (result && result.title && result.transcript) {
        const metadata = {
          videoId,
          originalUrl: url,
          duration: result.duration || 'Unknown',
          channelName: result.channelName || 'Unknown Channel',
          views: result.views || 'Unknown',
          uploadDate: result.uploadDate || new Date().toISOString().split('T')[0]
        };

        onVideoProcessed(result.title, result.transcript, url, metadata);
        
        toast.success('✅ YouTube video processed successfully!');
        setUrl(''); // Clear the input
      } else {
        throw new Error('Failed to extract video content');
      }
    } catch (error) {
      console.error('❌ YouTube processing failed:', error);
      toast.error(`Failed to process YouTube video: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isProcessing) {
      handleProcess();
    }
  };

  return (
    <Card className={`border shadow-sm bg-gradient-to-br from-red-500 to-red-600 text-white hover:shadow-md transition-all duration-200 ${className || ''}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="p-2 bg-white/20 rounded-lg">
            <Youtube className="w-5 h-5" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-sm">YouTube Video</h3>
            <p className="text-red-100 text-xs">Process video & transcript</p>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/70" />
              <Input
                type="url"
                placeholder="Paste YouTube URL here..."
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={isProcessing}
                className="pl-10 bg-white/20 border-white/30 text-white placeholder:text-white/70 focus:bg-white/30 focus:border-white/50"
              />
            </div>
          </div>

          <Button
            onClick={handleProcess}
            disabled={isProcessing || !url.trim()}
            className="w-full bg-white/20 hover:bg-white/30 text-white border-0 h-8 text-xs"
            size="sm"
          >
            {isProcessing ? (
              <>
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Play className="w-3 h-3 mr-1" />
                Process Video
              </>
            )}
          </Button>

          {url && isValidYouTubeUrl(url) && (
            <div className="text-xs text-white/80 bg-white/10 rounded p-2">
              <div className="flex items-center gap-1">
                <Youtube className="w-3 h-3" />
                <span>Valid YouTube URL detected</span>
              </div>
              <div className="mt-1 text-white/60">
                Video ID: {extractVideoId(url)}
              </div>
            </div>
          )}

          {url && !isValidYouTubeUrl(url) && (
            <div className="text-xs text-red-200 bg-red-800/30 rounded p-2">
              Please enter a valid YouTube URL (youtube.com/watch?v= or youtu.be/)
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default YouTubeProcessor;
