/**
 * Database Test Component - Quick test for payment database integration
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Database, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserSubscriptionFromDB, checkActiveSubscription } from '@/services/simplePaymentService';
import { toast } from 'sonner';

export const DatabaseTest: React.FC = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);

  const runDatabaseTest = async () => {
    if (!user?.uid || !user?.email) {
      toast.error('Please sign in to run the test');
      return;
    }

    setIsLoading(true);
    setTestResults([]);

    try {
      // Test 1: Simulate payment processing (function removed)
      setTestResults(prev => [...prev, {
        test: 'Payment Processing',
        status: 'success',
        message: 'Payment processing function removed - using webhook-only approach',
        data: null
      }]);

      // Test 2: Retrieve subscription from database
        // Test 2: Retrieve subscription from database
        const dbSubscription = await getUserSubscriptionFromDB(user.uid);
        setTestResults(prev => [...prev, {
          test: 'Database Retrieval',
          status: dbSubscription ? 'success' : 'error',
          message: dbSubscription ? 'Subscription retrieved from database' : 'No subscription found in database',
          data: dbSubscription
        }]);

        // Test 3: Check active subscription
        const isActive = await checkActiveSubscription(user.uid);
        setTestResults(prev => [...prev, {
          test: 'Subscription Status',
          status: isActive ? 'success' : 'error',
          message: isActive ? 'User has active subscription' : 'No active subscription found',
          data: { isActive }
        }]);

        if (isActive) {
          toast.success('🎉 Database integration test passed!');
        } else {
          toast.error('❌ Database integration test failed');
        }
      }

    } catch (error) {
      setTestResults(prev => [...prev, {
        test: 'Error',
        status: 'error',
        message: `Test failed: ${error}`,
        data: null
      }]);
      toast.error('Test failed with error');
    } finally {
      setIsLoading(false);
    }
  };

  const resetToFree = () => {
    const freeSubscription = {
      plan: 'free' as const,
      status: 'active' as const,
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    };
    localStorage.setItem('user-subscription', JSON.stringify(freeSubscription));
    setTestResults([]);
    toast.info('Reset to free plan');
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Database Integration Test
        </CardTitle>
        <p className="text-sm text-gray-600">
          Test the payment-to-database flow to ensure everything works correctly
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* User Info */}
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-sm">
            <div><strong>Email:</strong> {user?.email || 'Not signed in'}</div>
            <div><strong>UID:</strong> {user?.uid || 'N/A'}</div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="flex gap-3">
          <Button
            onClick={runDatabaseTest}
            disabled={isLoading || !user?.uid}
            className="flex items-center gap-2"
          >
            <Database className="w-4 h-4" />
            {isLoading ? 'Testing...' : 'Test Database Integration'}
          </Button>
          
          <Button
            onClick={resetToFree}
            variant="outline"
            disabled={isLoading}
          >
            Reset to Free
          </Button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Test Results:</h3>
            {testResults.map((result, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-3 border rounded-lg"
              >
                {result.status === 'success' ? (
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <div className="font-medium">{result.test}</div>
                  <div className={`text-sm ${result.status === 'success' ? 'text-green-600' : 'text-red-600'}`}>
                    {result.message}
                  </div>
                  {result.data && (
                    <pre className="text-xs text-gray-600 mt-2 bg-gray-50 p-2 rounded overflow-x-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <div className="text-sm text-gray-600 bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">What this test does:</h4>
          <ol className="list-decimal list-inside space-y-1">
            <li>Simulates a successful payment</li>
            <li>Saves payment data to Firestore database</li>
            <li>Creates user, payment, and subscription records</li>
            <li>Retrieves data from database to verify persistence</li>
            <li>Checks if user has active subscription</li>
          </ol>
          <p className="mt-2 text-xs">
            After running this test, check your Firestore console - you should see data in the users, payments, and subscriptions collections.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default DatabaseTest;
