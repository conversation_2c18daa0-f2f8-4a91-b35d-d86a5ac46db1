import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, User, Mail, Shield } from 'lucide-react';

export const OAuthTest: React.FC = () => {
  const { user, signInWithGoogle, signOut } = useAuth();
  const [testResult, setTestResult] = useState<{
    status: 'idle' | 'testing' | 'success' | 'error';
    message: string;
    details?: any;
  }>({ status: 'idle', message: '' });

  const handleTestGoogleOAuth = async () => {
    setTestResult({ status: 'testing', message: 'Testing Google OAuth...' });
    
    try {
      await signInWithGoogle();
      setTestResult({ 
        status: 'success', 
        message: 'Google OAuth test successful!',
        details: {
          email: user?.email,
          displayName: user?.displayName,
          uid: user?.uid
        }
      });
    } catch (error: any) {
      setTestResult({ 
        status: 'error', 
        message: error.message || 'OAuth test failed',
        details: {
          code: error.code,
          message: error.message
        }
      });
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setTestResult({ status: 'idle', message: '' });
    } catch (error: any) {
      console.error('Sign out error:', error);
    }
  };

  const getStatusIcon = () => {
    switch (testResult.status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'testing':
        return <AlertCircle className="w-5 h-5 text-yellow-600 animate-pulse" />;
      default:
        return <Shield className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (testResult.status) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'testing':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-6 h-6 text-blue-600" />
            Google OAuth Configuration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Configuration Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-700">Firebase Project</h4>
              <Badge variant="outline">{import.meta.env.VITE_FIREBASE_PROJECT_ID}</Badge>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-700">Auth Domain</h4>
              <Badge variant="outline">{import.meta.env.VITE_FIREBASE_AUTH_DOMAIN}</Badge>
            </div>
          </div>

          {/* Current User Status */}
          {user ? (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <User className="w-4 h-4 text-green-600" />
                <span className="font-semibold text-green-800">Authenticated User</span>
              </div>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <Mail className="w-3 h-3 text-green-600" />
                  <span>{user.email}</span>
                </div>
                {user.displayName && (
                  <div className="flex items-center gap-2">
                    <User className="w-3 h-3 text-green-600" />
                    <span>{user.displayName}</span>
                  </div>
                )}
                <div className="text-xs text-green-600">UID: {user.uid}</div>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-600" />
                <span className="font-semibold text-gray-800">No authenticated user</span>
              </div>
            </div>
          )}

          {/* Test Results */}
          {testResult.message && (
            <div className={`p-4 border rounded-lg ${getStatusColor()}`}>
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon()}
                <span className="font-semibold">{testResult.message}</span>
              </div>
              {testResult.details && (
                <pre className="text-xs bg-white/50 p-2 rounded mt-2 overflow-auto">
                  {JSON.stringify(testResult.details, null, 2)}
                </pre>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3">
            {!user ? (
              <Button 
                onClick={handleTestGoogleOAuth}
                disabled={testResult.status === 'testing'}
                className="flex items-center gap-2"
              >
                <svg className="w-4 h-4" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                {testResult.status === 'testing' ? 'Testing...' : 'Test Google OAuth'}
              </Button>
            ) : (
              <Button 
                onClick={handleSignOut}
                variant="outline"
                className="flex items-center gap-2"
              >
                Sign Out
              </Button>
            )}
          </div>

          {/* Configuration Instructions */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">OAuth Configuration Required:</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <p><strong>Client ID:</strong> 391336079060-e7qbd0g7lk8mqof18n6h2dspfj8bhqof.apps.googleusercontent.com</p>
              <p><strong>Authorized Origins:</strong> http://localhost:8081, https://ezmindai.com</p>
              <p><strong>Redirect URIs:</strong> https://studywise-hsli2.firebaseapp.com/__/auth/handler</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
