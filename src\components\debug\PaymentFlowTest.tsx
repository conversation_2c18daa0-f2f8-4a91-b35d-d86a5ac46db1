/**
 * Payment Flow Test Component
 * Tests the complete payment-to-database integration
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle, Database, CreditCard, User } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

import { updateUserSubscription, getUser, getUserSubscriptionFromFirestore } from '@/services/firestoreService';
import { getUserSubscriptionSync } from '@/services/subscriptionService';
import { toast } from 'sonner';

interface TestResult {
  step: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  data?: any;
}

export const PaymentFlowTest: React.FC = () => {
  const { user } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const addResult = (step: string, status: 'success' | 'error', message: string, data?: any) => {
    setResults(prev => [...prev, { step, status, message, data }]);
  };

  const runCompleteTest = async () => {
    if (!user?.uid || !user?.email) {
      toast.error('Please sign in to run the test');
      return;
    }

    setIsRunning(true);
    setResults([]);

    try {
      // Step 1: Test Payment Processing (Simulation)
      addResult('payment', 'pending', 'Simulating payment processing...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      const sessionId = `test_${Date.now()}`;
      const planType = 'monthly';
      
      // Step 2: Test localStorage Update
      addResult('localStorage', 'pending', 'Updating localStorage...');
      
      addResult('localStorage', 'success', 'Subscription saved to localStorage', subscription);

      // Step 3: Test Firestore Database Update
      addResult('firestore', 'pending', 'Saving to Firestore database...');
      try {
        await updateUserSubscription(user.uid, subscription, sessionId, planType);
        addResult('firestore', 'success', 'Subscription saved to Firestore database');
      } catch (error) {
        addResult('firestore', 'error', `Firestore save failed: ${error}`);
      }

      // Step 4: Test Database Retrieval
      addResult('retrieval', 'pending', 'Retrieving from database...');
      try {
        const firestoreUser = await getUser(user.uid);
        const firestoreSubscription = await getUserSubscriptionFromFirestore(user.uid);
        
        if (firestoreUser && firestoreSubscription) {
          addResult('retrieval', 'success', 'Data retrieved from Firestore', {
            user: firestoreUser,
            subscription: firestoreSubscription
          });
        } else {
          addResult('retrieval', 'error', 'No data found in Firestore');
        }
      } catch (error) {
        addResult('retrieval', 'error', `Database retrieval failed: ${error}`);
      }

      // Step 5: Test Data Sync
      addResult('sync', 'pending', 'Testing data synchronization...');
      const localSubscription = getUserSubscriptionSync();
      
      if (localSubscription.plan === 'premium' && localSubscription.status === 'active') {
        addResult('sync', 'success', 'Data synchronized correctly', localSubscription);
      } else {
        addResult('sync', 'error', 'Data sync failed - subscription not active');
      }

      // Step 6: Test User Upgrade Status
      addResult('upgrade', 'pending', 'Checking user upgrade status...');
      if (localSubscription.plan === 'premium') {
        addResult('upgrade', 'success', '✅ User successfully upgraded to premium!');
        toast.success('🎉 Payment flow test completed successfully!');
      } else {
        addResult('upgrade', 'error', '❌ User upgrade failed - still on free plan');
        toast.error('❌ Payment flow test failed');
      }

    } catch (error) {
      addResult('error', 'error', `Test failed: ${error}`);
      toast.error('Test failed with error');
    } finally {
      setIsRunning(false);
    }
  };

  const resetTest = () => {
    setResults([]);
    // Reset to free plan
    const freeSubscription = {
      plan: 'free' as const,
      status: 'active' as const,
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    };
    localStorage.setItem('user-subscription', JSON.stringify(freeSubscription));
    toast.info('Test reset - user back to free plan');
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Payment Flow Database Integration Test
        </CardTitle>
        <p className="text-sm text-gray-600">
          Tests the complete payment-to-database flow to ensure user upgrades work correctly
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* User Info */}
        <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
          <User className="w-5 h-5 text-gray-600" />
          <div>
            <div className="font-medium">{user?.email || 'Not signed in'}</div>
            <div className="text-sm text-gray-600">UID: {user?.uid || 'N/A'}</div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="flex gap-3">
          <Button
            onClick={runCompleteTest}
            disabled={isRunning || !user?.uid}
            className="flex items-center gap-2"
          >
            <CreditCard className="w-4 h-4" />
            {isRunning ? 'Running Test...' : 'Run Complete Payment Test'}
          </Button>
          
          <Button
            onClick={resetTest}
            variant="outline"
            disabled={isRunning}
          >
            Reset Test
          </Button>
        </div>

        {/* Test Results */}
        {results.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Test Results:</h3>
            {results.map((result, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-3 border rounded-lg"
              >
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {result.step}
                    </Badge>
                    <span className={`text-sm font-medium ${getStatusColor(result.status)}`}>
                      {result.message}
                    </span>
                  </div>
                  {result.data && (
                    <pre className="text-xs text-gray-600 mt-2 bg-gray-50 p-2 rounded overflow-x-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <div className="text-sm text-gray-600 bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">What this test does:</h4>
          <ol className="list-decimal list-inside space-y-1">
            <li>Simulates a successful payment</li>
            <li>Updates subscription in localStorage</li>
            <li>Saves subscription to Firestore database</li>
            <li>Retrieves data from database to verify persistence</li>
            <li>Checks data synchronization</li>
            <li>Verifies user upgrade status</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentFlowTest;
