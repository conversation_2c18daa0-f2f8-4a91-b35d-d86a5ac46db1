import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserSubscription, getUserUsage, canPerformAction } from '@/services/subscriptionService';
import { hasActiveSubscription } from '@/services/stripeService';

export const SubscriptionStatus: React.FC = () => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState(getUserSubscription());
  const [usage, setUsage] = useState(getUserUsage());
  const [lastRefresh, setLastRefresh] = useState(new Date());

  const refresh = () => {
    setSubscription(getUserSubscription());
    setUsage(getUserUsage());
    setLastRefresh(new Date());
  };

  useEffect(() => {
    const handleStorageChange = () => {
      refresh();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const hasActiveSub = user?.email ? hasActiveSubscription(user.email) : false;
  const canCreateTopics = canPerformAction('aiQuestions', user?.email);
  const canUploadFiles = canPerformAction('fileUploads', user?.email);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {hasActiveSub ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <XCircle className="w-5 h-5 text-red-600" />
            )}
            Subscription Status
          </CardTitle>
          <Button onClick={refresh} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-semibold text-gray-700">Plan Status</h4>
            <div className="flex items-center gap-2">
              <Badge variant={hasActiveSub ? 'default' : 'secondary'}>
                {subscription.plan.toUpperCase()}
              </Badge>
              <Badge variant={subscription.status === 'active' ? 'default' : 'destructive'}>
                {subscription.status}
              </Badge>
            </div>
            <p className="text-sm text-gray-600">
              Expires: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold text-gray-700">User Info</h4>
            <p className="text-sm text-gray-600">
              Email: {user?.email || 'Not signed in'}
            </p>
            <p className="text-sm text-gray-600">
              Last refresh: {lastRefresh.toLocaleTimeString()}
            </p>
          </div>
        </div>

        {/* Feature Access */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-700">Feature Access</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center gap-2">
              {canCreateTopics ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <XCircle className="w-4 h-4 text-red-600" />
              )}
              <span className="text-sm">Create Topics</span>
            </div>
            
            <div className="flex items-center gap-2">
              {canUploadFiles ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <XCircle className="w-4 h-4 text-red-600" />
              )}
              <span className="text-sm">Upload Files</span>
            </div>
            
            <div className="flex items-center gap-2">
              {subscription.plan === 'premium' ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <XCircle className="w-4 h-4 text-red-600" />
              )}
              <span className="text-sm">Premium Features</span>
            </div>
            
            <div className="flex items-center gap-2">
              {hasActiveSub ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <XCircle className="w-4 h-4 text-red-600" />
              )}
              <span className="text-sm">Active Subscription</span>
            </div>
          </div>
        </div>

        {/* Usage Stats */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-700">Current Usage</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
            <div className="text-center">
              <div className="font-semibold">{usage.aiQuestions}</div>
              <div className="text-gray-600">Topics</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{usage.fileUploads}</div>
              <div className="text-gray-600">Files</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{usage.audioTranscriptions}</div>
              <div className="text-gray-600">Audio</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{usage.libraryStorage}</div>
              <div className="text-gray-600">Library</div>
            </div>
          </div>
        </div>

        {/* Storage Keys Debug */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-700">Storage Debug</h4>
          <div className="bg-gray-50 rounded-lg p-3 text-xs font-mono space-y-1">
            <div>user-subscription: {localStorage.getItem('user-subscription') ? '✅' : '❌'}</div>
            <div>subscription-{user?.email}: {user?.email && localStorage.getItem(`subscription-${user.email}`) ? '✅' : '❌'}</div>
            <div>user-usage: {localStorage.getItem('user-usage') ? '✅' : '❌'}</div>
          </div>
        </div>

        {/* Status Indicators */}
        {!hasActiveSub && subscription.plan === 'free' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-yellow-800">Free Plan Active</p>
                <p className="text-yellow-700">Limited to 3 topics, 5-minute recordings, 5MB uploads</p>
              </div>
            </div>
          </div>
        )}

        {hasActiveSub && subscription.plan === 'premium' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-green-800">Premium Plan Active</p>
                <p className="text-green-700">Unlimited access to all features</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
