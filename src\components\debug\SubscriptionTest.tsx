import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Crown, Zap, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { hasActiveSubscription } from '@/services/stripeService';
import { getUserSubscription } from '@/services/subscriptionService';
import { toast } from 'sonner';

export const SubscriptionTest: React.FC = () => {
  const { user } = useAuth();
  const [isActivating, setIsActivating] = useState(false);
  const [subscription, setSubscription] = useState(getUserSubscription());

  const refreshSubscription = () => {
    setSubscription(getUserSubscription());
  };

  const activateSubscription = async (planType: 'monthly' | 'yearly') => {
    if (!user?.email) {
      toast.error('Please sign in first');
      return;
    }

    setIsActivating(true);
    try {
      const sessionId = `test_${Date.now()}_${planType}`;
      

      toast.success(`🎉 ${planType} subscription activated!`);
      
      // Trigger storage event to update other components
      window.dispatchEvent(new StorageEvent('storage', {
        key: `subscription-${user.email}`,
        newValue: JSON.stringify(newSubscription)
      }));
      
      refreshSubscription();
    } catch (error) {
      toast.error('Failed to activate subscription');
    } finally {
      setIsActivating(false);
    }
  };

  const deactivateSubscription = () => {
    if (!user?.email) return;
    
    localStorage.removeItem(`subscription-${user.email}`);
    toast.success('Subscription deactivated');
    refreshSubscription();
  };

  const hasActiveSub = user?.email ? hasActiveSubscription(user.email) : false;

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="w-6 h-6 text-purple-600" />
            Subscription Test Panel
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Status */}
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-700">Current Status</h4>
            <div className="flex items-center gap-3">
              {hasActiveSub ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <XCircle className="w-5 h-5 text-red-600" />
              )}
              <span className="font-medium">
                {hasActiveSub ? 'Pro Subscription Active' : 'Free Plan'}
              </span>
              <Badge variant={hasActiveSub ? 'default' : 'secondary'}>
                {subscription.plan}
              </Badge>
            </div>
            
            {hasActiveSub && (
              <div className="text-sm text-gray-600">
                <p>Status: {subscription.status}</p>
                <p>Expires: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}</p>
              </div>
            )}
          </div>

          {/* User Info */}
          <div className="space-y-2">
            <h4 className="font-semibold text-gray-700">User Info</h4>
            <div className="text-sm text-gray-600">
              <p>Email: {user?.email || 'Not signed in'}</p>
              <p>Display Name: {user?.displayName || 'Not set'}</p>
            </div>
          </div>

          {/* Test Actions */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-700">Test Actions</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button
                onClick={() => activateSubscription('monthly')}
                disabled={isActivating || !user?.email}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isActivating ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Zap className="w-4 h-4 mr-2" />
                )}
                Activate Monthly
              </Button>
              
              <Button
                onClick={() => activateSubscription('yearly')}
                disabled={isActivating || !user?.email}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isActivating ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Crown className="w-4 h-4 mr-2" />
                )}
                Activate Yearly
              </Button>
            </div>

            <div className="flex gap-3">
              <Button
                onClick={deactivateSubscription}
                disabled={!hasActiveSub || !user?.email}
                variant="outline"
                className="flex-1"
              >
                Deactivate Subscription
              </Button>
              
              <Button
                onClick={refreshSubscription}
                variant="outline"
                size="sm"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-800 mb-2">How to Test Stripe Integration:</h4>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. <strong>Sign in</strong> to the app first</li>
              <li>2. <strong>Click "Activate Monthly/Yearly"</strong> to simulate successful payment</li>
              <li>3. <strong>Check dashboard</strong> - paywall should disappear, unlimited features unlocked</li>
              <li>4. <strong>Try creating 4+ topics</strong> - should work without paywall</li>
              <li>5. <strong>Click "Deactivate"</strong> to test free plan limits again</li>
            </ol>
          </div>

          {/* Real Stripe Test */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">Stripe Success Simulation:</h4>
            <p className="text-sm text-yellow-700 mb-3">
              Test what happens when Stripe redirects back after successful payment:
            </p>
            <div className="flex gap-2">
              <Button
                onClick={() => window.open('/payment-success?session_id=cs_test_123&plan=monthly&success=true', '_blank')}
                variant="outline"
                size="sm"
              >
                Test Monthly Success
              </Button>
              <Button
                onClick={() => window.open('/payment-success?session_id=cs_test_456&plan=yearly&success=true', '_blank')}
                variant="outline"
                size="sm"
              >
                Test Yearly Success
              </Button>
            </div>
          </div>

          {/* Real Stripe Test */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-800 mb-2">Real Stripe Test:</h4>
            <p className="text-sm text-blue-700 mb-3">
              To test the actual Stripe integration, go to the pricing page and use test card: <code>4242 4242 4242 4242</code>
            </p>
            <Button
              onClick={() => window.open('/pricing', '_blank')}
              variant="outline"
              size="sm"
            >
              Open Pricing Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
