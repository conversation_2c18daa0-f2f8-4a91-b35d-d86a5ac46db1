import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  Brain, 
  Zap, 
  CheckCircle, 
  XCircle, 
  Loader2,
  TestTube,
  MessageSquare
} from 'lucide-react';
import { generateContent, generateNotes, generateQuiz } from '@/services/vertexAIService';
import { toast } from 'sonner';

const VertexAITest: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<{
    basic?: { success: boolean; response?: string; error?: string };
    notes?: { success: boolean; response?: string; error?: string };
    quiz?: { success: boolean; response?: string; error?: string };
  }>({});
  const [customPrompt, setCustomPrompt] = useState('');
  const [customResponse, setCustomResponse] = useState('');

  const runBasicTest = async () => {
    setIsLoading(true);
    try {
      const response = await generateContent("Say 'Hello from Vertex AI!' and explain what you are in one sentence.");
      setTestResults(prev => ({
        ...prev,
        basic: { success: true, response }
      }));
      toast.success('Basic test passed!');
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        basic: { success: false, error: error.message }
      }));
      toast.error('Basic test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const runNotesTest = async () => {
    setIsLoading(true);
    try {
      const response = await generateNotes('Photosynthesis', 'high school');
      setTestResults(prev => ({
        ...prev,
        notes: { success: true, response }
      }));
      toast.success('Notes generation test passed!');
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        notes: { success: false, error: error.message }
      }));
      toast.error('Notes test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const runQuizTest = async () => {
    setIsLoading(true);
    try {
      const response = await generateQuiz('Basic Math', 'easy', 2);
      setTestResults(prev => ({
        ...prev,
        quiz: { success: true, response }
      }));
      toast.success('Quiz generation test passed!');
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        quiz: { success: false, error: error.message }
      }));
      toast.error('Quiz test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const runCustomTest = async () => {
    if (!customPrompt.trim()) {
      toast.error('Please enter a custom prompt');
      return;
    }

    setIsLoading(true);
    try {
      const response = await generateContent(customPrompt);
      setCustomResponse(response);
      toast.success('Custom test completed!');
    } catch (error) {
      setCustomResponse(`Error: ${error.message}`);
      toast.error('Custom test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const runAllTests = async () => {
    await runBasicTest();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay
    await runNotesTest();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay
    await runQuizTest();
  };

  const TestResult = ({ test, title }: { test?: { success: boolean; response?: string; error?: string }, title: string }) => (
    <div className="flex items-center gap-2 p-3 border rounded-lg">
      <div className="flex items-center gap-2 flex-1">
        {test ? (
          test.success ? (
            <CheckCircle className="w-5 h-5 text-green-500" />
          ) : (
            <XCircle className="w-5 h-5 text-red-500" />
          )
        ) : (
          <div className="w-5 h-5 rounded-full border-2 border-gray-300" />
        )}
        <span className="font-medium">{title}</span>
      </div>
      {test && (
        <Badge variant={test.success ? 'default' : 'destructive'}>
          {test.success ? 'Passed' : 'Failed'}
        </Badge>
      )}
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="w-6 h-6" />
            Vertex AI Integration Test
          </CardTitle>
          <p className="text-gray-600">
            Test the Vertex AI integration to ensure all features are working correctly.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Controls */}
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={runBasicTest} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Brain className="w-4 h-4 mr-2" />}
              Test Basic Generation
            </Button>
            <Button 
              onClick={runNotesTest} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Zap className="w-4 h-4 mr-2" />}
              Test Notes Generation
            </Button>
            <Button 
              onClick={runQuizTest} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <MessageSquare className="w-4 h-4 mr-2" />}
              Test Quiz Generation
            </Button>
            <Button 
              onClick={runAllTests} 
              disabled={isLoading}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:opacity-90"
            >
              {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <TestTube className="w-4 h-4 mr-2" />}
              Run All Tests
            </Button>
          </div>

          {/* Test Results */}
          <div className="space-y-3">
            <h3 className="font-semibold">Test Results</h3>
            <TestResult test={testResults.basic} title="Basic Content Generation" />
            <TestResult test={testResults.notes} title="Notes Generation" />
            <TestResult test={testResults.quiz} title="Quiz Generation" />
          </div>

          {/* Custom Test */}
          <div className="space-y-3">
            <h3 className="font-semibold">Custom Test</h3>
            <Input
              placeholder="Enter a custom prompt to test..."
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
            />
            <Button 
              onClick={runCustomTest} 
              disabled={isLoading || !customPrompt.trim()}
              className="w-full"
            >
              {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Brain className="w-4 h-4 mr-2" />}
              Test Custom Prompt
            </Button>
            {customResponse && (
              <Textarea
                value={customResponse}
                readOnly
                className="min-h-[100px]"
                placeholder="Response will appear here..."
              />
            )}
          </div>

          {/* Detailed Results */}
          {Object.values(testResults).some(test => test?.response || test?.error) && (
            <div className="space-y-4">
              <h3 className="font-semibold">Detailed Results</h3>
              {testResults.basic?.response && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="font-medium text-green-800 mb-2">Basic Test Response:</h4>
                  <p className="text-green-700 text-sm">{testResults.basic.response}</p>
                </div>
              )}
              {testResults.basic?.error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-2">Basic Test Error:</h4>
                  <p className="text-red-700 text-sm">{testResults.basic.error}</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default VertexAITest;
