import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  Flame, 
  Trophy, 
  Star, 
  Target, 
  Calendar,
  TrendingUp,
  Award,
  Zap,
  BookOpen,
  Brain
} from 'lucide-react';
import {
  getStudyStreak,
  getUserAchievements,
  getUserStats,
  getRecentAchievements,
  TopicProgress
} from '@/services/gamificationService';
import { useAuth } from '@/contexts/AuthContext';

interface GamificationDashboardProps {
  className?: string;
  topicProgresses?: TopicProgress[];
}

export const GamificationDashboard = ({ className, topicProgresses = [] }: GamificationDashboardProps) => {
  const { user } = useAuth();
  const [streak, setStreak] = useState(getStudyStreak(user?.email));
  const [achievements, setAchievements] = useState(getUserAchievements(user?.email));
  const [stats, setStats] = useState(getUserStats(user?.email));
  const [recentAchievements, setRecentAchievements] = useState(getRecentAchievements(user?.email));

  useEffect(() => {
    // Refresh data when user changes
    if (user?.email) {
      setStreak(getStudyStreak(user.email));
      setAchievements(getUserAchievements(user.email));
      setStats(getUserStats(user.email));
      setRecentAchievements(getRecentAchievements(user.email));
    }
  }, [user?.email]);

  const unlockedAchievements = achievements.filter(a => a.unlockedAt);
  const nextLevelXP = (stats.level * 100) - stats.experiencePoints;
  const currentLevelProgress = (stats.experiencePoints % 100);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Study Streak & Level */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Study Streak */}
        <Card className="border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-red-50">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Flame className="w-5 h-5 text-orange-500" />
              Study Streak
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-3">
              <div>
                <div className="text-3xl font-bold text-orange-600">{streak.currentStreak}</div>
                <div className="text-sm text-gray-600">days in a row</div>
              </div>
              <div className="text-right">
                <div className="text-lg font-semibold text-gray-700">{streak.longestStreak}</div>
                <div className="text-xs text-gray-500">best streak</div>
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="w-4 h-4" />
              <span>{streak.totalStudyDays} total study days</span>
            </div>
          </CardContent>
        </Card>

        {/* Level & XP */}
        <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Star className="w-5 h-5 text-purple-500" />
              Level {stats.level}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Experience Points</span>
                <span className="text-sm text-gray-600">{stats.experiencePoints} XP</span>
              </div>
              <Progress value={currentLevelProgress} className="h-2" />
              <div className="text-xs text-gray-500 text-center">
                {nextLevelXP} XP to level {stats.level + 1}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-500" />
            Study Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.totalTopics}</div>
              <div className="text-sm text-gray-600">Topics Created</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.totalFlashcardsCreated}</div>
              <div className="text-sm text-gray-600">Flashcards Made</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.totalQuizzesTaken}</div>
              <div className="text-sm text-gray-600">Quizzes Taken</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.averageQuizScore}%</div>
              <div className="text-sm text-gray-600">Avg Quiz Score</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Topic Progress */}
      {topicProgresses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5 text-green-500" />
              Topic Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topicProgresses.slice(0, 5).map((progress) => (
                <div key={progress.topicId} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-sm truncate">{progress.topicTitle}</span>
                    <Badge variant="outline" className="text-xs">
                      {Math.round(progress.completionPercentage)}%
                    </Badge>
                  </div>
                  <Progress value={progress.completionPercentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Achievements */}
      {recentAchievements.length > 0 && (
        <Card className="border-2 border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5 text-yellow-500" />
              Recent Achievements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentAchievements.map((achievement) => (
                <div key={achievement.id} className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <div className="font-semibold text-sm">{achievement.title}</div>
                    <div className="text-xs text-gray-600">{achievement.description}</div>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    New!
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* All Achievements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-500" />
            Achievements ({unlockedAchievements.length}/{achievements.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {achievements.map((achievement) => (
              <div
                key={achievement.id}
                className={`p-3 rounded-lg border text-center transition-all duration-200 ${
                  achievement.unlockedAt
                    ? 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200 shadow-sm'
                    : 'bg-gray-50 border-gray-200 opacity-60'
                }`}
              >
                <div className="text-2xl mb-2">{achievement.icon}</div>
                <div className="font-semibold text-xs mb-1">{achievement.title}</div>
                <div className="text-xs text-gray-600 mb-2">{achievement.description}</div>
                {achievement.unlockedAt ? (
                  <Badge variant="secondary" className="text-xs">
                    Unlocked
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-xs">
                    Locked
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
