import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { toast } from 'sonner';
import { 
  Crown, 
  Sparkles, 
  Brain, 
  Check,
  X,
  ArrowR<PERSON>,
  Star
} from 'lucide-react';
import { StripePricingTable } from '@/components/pricing/StripePricingTable';
import { useAuth } from '@/contexts/AuthContext';

interface NewUserPaywallProps {
  isOpen: boolean;
  onClose: () => void;
  onContinueFree: () => void;
  userEmail?: string;
  userName?: string;
}

export const NewUserPaywall: React.FC<NewUserPaywallProps> = ({
  isOpen,
  onClose,
  onContinueFree,
  userEmail,
  userName
}) => {
  const [showPricing, setShowPricing] = useState(false);
  const { user } = useAuth();



  const handleContinueFree = () => {
    console.log('User chose to continue with free plan');
    onContinueFree();
    onClose();
  };

  const handleViewPricing = () => {
    setShowPricing(true);
  };

  const handleSelectPlan = async (plan: 'monthly' | 'yearly') => {
    if (!user?.uid || !user?.email) {
      toast.error('Please sign in to select a plan');
      return;
    }

    try {
      toast.loading('Activating your subscription...');
      
      // Generate a session ID for tracking
      const sessionId = `paywall_${Date.now()}_${plan}`;
      
      // Process payment success directly - THIS IS NOW HANDLED BY STRIPE WEBHOOKS
      // const { processPaymentSuccess } = await import('@/services/simplePaymentService');
      // const result = await processPaymentSuccess(user.uid, user.email, sessionId, plan);
      // 
      // if (result.success) {
      //   toast.dismiss();
      //   toast.success('🎉 Subscription activated successfully!');
      //   
      //   // Close paywall and refresh
      //   onClose();
      //   window.location.reload();
      // } else {
      //   toast.dismiss();
      //   toast.error('Failed to activate subscription. Please try again.');
      // }
    } catch (error) {
      toast.dismiss();
      toast.error('An error occurred. Please try again.');
      console.error('Subscription activation error:', error);
    }
  };

  if (showPricing) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="text-center pb-6">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <Crown className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -top-1 -right-1">
                  <Sparkles className="w-6 h-6 text-yellow-500" />
                </div>
              </div>
            </div>
            
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Choose Your EZMind AI Plan
            </DialogTitle>
            
            <p className="text-gray-600 mt-2">
              Select the plan that best fits your learning needs
            </p>
          </DialogHeader>

          {/* Stripe Pricing Table */}
          <div className="mb-6">
            <StripePricingTable 
              userEmail={user?.email || userEmail} 
              clientReferenceId={user?.uid} 
            />
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
            <Button
              onClick={handleContinueFree}
              variant="outline"
              className="flex-1"
            >
              <X className="w-4 h-4 mr-2" />
              Continue with Free Plan
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="text-center pt-4 border-t border-gray-100">
            <div className="flex justify-center items-center gap-6 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Check className="w-4 h-4 text-green-600" />
                <span>Cancel Anytime</span>
              </div>
              <div className="flex items-center gap-1">
                <Check className="w-4 h-4 text-green-600" />
                <span>Secure Payments</span>
              </div>
              <div className="flex items-center gap-1">
                <Check className="w-4 h-4 text-green-600" />
                <span>30-Day Money Back</span>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader className="text-center pb-6">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <div className="absolute -top-1 -right-1">
                <Sparkles className="w-6 h-6 text-yellow-500" />
              </div>
            </div>
          </div>
          
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Welcome to EZMind AI{userName ? `, ${userName}` : ''}! 🎉
          </DialogTitle>
          
          <p className="text-gray-600 mt-2">
            Choose your plan to get started with AI-powered learning.
          </p>
        </DialogHeader>

        {/* Quick Comparison */}
        <div className="mb-6">
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Star className="w-4 h-4" />
                  <span className="font-semibold">Free Plan</span>
                </div>
                <p className="text-sm text-gray-600">3 topics • 5min recording • 5MB uploads</p>
              </div>
              <div>
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Crown className="w-4 h-4 text-purple-600" />
                  <span className="font-semibold text-purple-600">Premium</span>
                </div>
                <p className="text-sm text-purple-600">Unlimited everything • Priority support</p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleContinueFree}
              variant="outline"
              className="flex-1 py-3"
            >
              <Star className="w-4 h-4 mr-2" />
              Start with Free Plan
              <span className="text-xs text-gray-500 ml-2">(3 topics, 5min recording)</span>
            </Button>
            
            <Button
              onClick={handleViewPricing}
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 py-3"
            >
              <Crown className="w-4 h-4 mr-2" />
              View Premium Plans
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="text-center pt-6 border-t border-gray-100">
          <div className="flex justify-center items-center gap-6 text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <Check className="w-4 h-4 text-green-600" />
              <span>No Credit Card Required</span>
            </div>
            <div className="flex items-center gap-1">
              <Check className="w-4 h-4 text-green-600" />
              <span>Upgrade Anytime</span>
            </div>
            <div className="flex items-center gap-1">
              <Check className="w-4 h-4 text-green-600" />
              <span>Cancel Anytime</span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NewUserPaywall;
