import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Lock, Zap, Crown, Star, X } from 'lucide-react';
import { getUserSubscription, getUserUsage } from '@/services/subscriptionService';
import { StripeCheckout } from '@/components/stripe/StripeCheckout';
import { toast } from 'sonner';


interface PaywallModalProps {
  isOpen: boolean;
  onClose: () => void;
  feature: string;
  description: string;
  requiredPlan?: 'monthly' | 'yearly' | 'premium';
  userEmail?: string;
}

const planIcons = {
  monthly: <Star className="w-5 h-5" />,
  yearly: <Zap className="w-5 h-5" />,
  premium: <Crown className="w-5 h-5" />
};

const planColors = {
  monthly: 'from-purple-500 to-pink-600',
  yearly: 'from-purple-600 to-pink-700',
  premium: 'from-yellow-500 to-orange-600'
};

export const PaywallModal: React.FC<PaywallModalProps> = ({
  isOpen,
  onClose,
  feature,
  description,
  requiredPlan = 'basic',
  userEmail
}) => {
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const subscription = getUserSubscription();
  const usage = getUserUsage();

  const handleUpgrade = async (planType: 'monthly' | 'yearly') => {
    setIsUpgrading(true);
    try {
      const result = await createCheckoutSession(planType, userEmail);
      if (result.success) {
        toast.success('Redirecting to checkout...');
        onClose();
      } else {
        toast.error('Failed to start checkout process');
      }
    } catch (error) {
      console.error('Upgrade failed:', error);
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsUpgrading(false);
    }
  };

  const getUsageInfo = () => {
    const freeLimits = {
      aiQuestions: 5,
      fileUploads: 3,
      audioTranscriptions: 1
    };

    const usageInfo = [];

    usageInfo.push({
      label: 'AI Generations',
      used: usage.aiQuestions,
      limit: freeLimits.aiQuestions,
      percentage: Math.min((usage.aiQuestions / freeLimits.aiQuestions) * 100, 100)
    });

    usageInfo.push({
      label: 'File Uploads',
      used: usage.fileUploads,
      limit: freeLimits.fileUploads,
      percentage: Math.min((usage.fileUploads / freeLimits.fileUploads) * 100, 100)
    });

    usageInfo.push({
      label: 'Audio Recording',
      used: usage.audioTranscriptions,
      limit: freeLimits.audioTranscriptions,
      percentage: Math.min((usage.audioTranscriptions / freeLimits.audioTranscriptions) * 100, 100)
    });

    return usageInfo;
  };

  const usageInfo = getUsageInfo();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <Lock className="w-5 h-5 text-orange-500" />
              Upgrade Required
            </DialogTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Feature Description */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature}</h3>
            <p className="text-gray-600">{description}</p>
          </div>

          {/* Current Plan Status */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-700">Current Plan</span>
              <Badge variant="outline" className="capitalize">
                {subscription.plan}
              </Badge>
            </div>

            {/* Usage Information */}
            {usageInfo.length > 0 && (
              <div className="space-y-3">
                {usageInfo.map((info, index) => (
                  <div key={index}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600">{info.label}</span>
                      <span className="text-gray-900">
                        {info.used} / {info.limit}
                      </span>
                    </div>
                    <Progress value={info.percentage} className="h-2" />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-4">
            <span className={`text-sm ${billingCycle === 'monthly' ? 'text-gray-900 font-semibold' : 'text-gray-500'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
              className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm ${billingCycle === 'yearly' ? 'text-gray-900 font-semibold' : 'text-gray-500'}`}>
              Yearly
            </span>
            {billingCycle === 'yearly' && (
              <Badge className="bg-green-100 text-green-700 text-xs">
                Save 20%
              </Badge>
            )}
          </div>

          {/* Upgrade Options */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700 text-center">
              Upgrade to unlock unlimited access
            </h4>

            <Button
              onClick={() => handleUpgrade(billingCycle)}
              disabled={isUpgrading}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:opacity-90 text-white font-semibold py-3"
            >
              <div className="flex items-center justify-center gap-2">
                <Crown className="w-5 h-5" />
                <span>
                  {isUpgrading ? 'Processing...' :
                    `Upgrade to Pro - $${billingCycle === 'monthly' ? '10/month' : '100/year'}`
                  }
                </span>
              </div>
            </Button>
          </div>

          {/* Benefits */}
          <div className="text-center text-sm text-gray-500">
            <p>✓ 7-day free trial</p>
            <p>✓ Cancel anytime</p>
            <p>✓ 30-day money-back guarantee</p>
          </div>
        </div>
      </DialogContent>


    </Dialog>
  );
};

export default PaywallModal;
