import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  Play, 
  Pause, 
  Square, 
  SkipBack, 
  SkipForward, 
  Volume2, 
  VolumeX,
  Repeat,
  Shuffle,
  Music,
  Clock,
  FileAudio
} from 'lucide-react';

interface AudioFilePreviewProps {
  fileName: string;
  content: string;
  fileUrl?: string;
  metadata?: any;
}

const AudioFilePreview: React.FC<AudioFilePreviewProps> = ({
  fileName,
  content,
  fileUrl,
  metadata
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [waveformData, setWaveformData] = useState<number[]>([]);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    if (audioRef.current) {
      const audio = audioRef.current;
      
      const handleLoadedMetadata = () => {
        setDuration(audio.duration);
        generateWaveform();
      };
      
      const handleTimeUpdate = () => {
        setCurrentTime(audio.currentTime);
      };
      
      const handleEnded = () => {
        setIsPlaying(false);
        setCurrentTime(0);
      };

      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      audio.addEventListener('timeupdate', handleTimeUpdate);
      audio.addEventListener('ended', handleEnded);

      return () => {
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
        audio.removeEventListener('timeupdate', handleTimeUpdate);
        audio.removeEventListener('ended', handleEnded);
      };
    }
  }, [fileUrl]);

  const generateWaveform = () => {
    // Generate mock waveform data for visualization
    const points = 100;
    const data = Array.from({ length: points }, () => Math.random() * 100);
    setWaveformData(data);
  };

  const togglePlayPause = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (value: number[]) => {
    if (!audioRef.current) return;
    const newTime = (value[0] / 100) * duration;
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (value: number[]) => {
    if (!audioRef.current) return;
    const newVolume = value[0] / 100;
    audioRef.current.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    if (!audioRef.current) return;
    if (isMuted) {
      audioRef.current.volume = volume;
      setIsMuted(false);
    } else {
      audioRef.current.volume = 0;
      setIsMuted(true);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getFileInfo = () => {
    const fileExtension = fileName.split('.').pop()?.toUpperCase() || 'AUDIO';
    const fileSizeKB = metadata?.size ? Math.round(metadata.size / 1024) : 'Unknown';
    
    return {
      format: fileExtension,
      size: fileSizeKB,
      bitrate: metadata?.bitrate || 'Unknown',
      sampleRate: metadata?.sampleRate || 'Unknown'
    };
  };

  const fileInfo = getFileInfo();

  return (
    <div className="bg-gradient-to-b from-gray-900 to-black text-white min-h-full">
      {/* Windows Media Player Header */}
      <div className="bg-gray-800 p-4 border-b border-gray-700">
        <div className="flex items-center gap-3">
          <Music className="w-6 h-6 text-blue-400" />
          <div>
            <h2 className="text-lg font-medium">{fileName}</h2>
            <p className="text-sm text-gray-400">Windows Media Player</p>
          </div>
        </div>
      </div>

      {/* Main Player Area */}
      <div className="p-6">
        {/* Album Art / Visualization Area */}
        <div className="flex gap-6 mb-6">
          {/* Album Art Placeholder */}
          <div className="w-48 h-48 bg-gray-800 rounded-lg flex items-center justify-center border border-gray-700">
            <div className="text-center">
              <FileAudio className="w-16 h-16 text-gray-500 mx-auto mb-2" />
              <p className="text-sm text-gray-400">Audio File</p>
            </div>
          </div>

          {/* Track Info */}
          <div className="flex-1">
            <div className="mb-4">
              <h3 className="text-xl font-semibold mb-2">{fileName}</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Format:</span>
                  <span className="ml-2 text-white">{fileInfo.format}</span>
                </div>
                <div>
                  <span className="text-gray-400">Size:</span>
                  <span className="ml-2 text-white">{fileInfo.size} KB</span>
                </div>
                <div>
                  <span className="text-gray-400">Duration:</span>
                  <span className="ml-2 text-white">{formatTime(duration)}</span>
                </div>
                <div>
                  <span className="text-gray-400">Bitrate:</span>
                  <span className="ml-2 text-white">{fileInfo.bitrate}</span>
                </div>
              </div>
            </div>

            {/* Waveform Visualization */}
            <div className="bg-gray-800 rounded-lg p-4 mb-4">
              <h4 className="text-sm text-gray-400 mb-2">Waveform</h4>
              <div className="flex items-end gap-1 h-20">
                {waveformData.map((height, index) => (
                  <div
                    key={index}
                    className={`bg-blue-500 transition-all duration-150 ${
                      (index / waveformData.length) * 100 <= (currentTime / duration) * 100
                        ? 'bg-blue-400'
                        : 'bg-gray-600'
                    }`}
                    style={{
                      height: `${height}%`,
                      width: '2px',
                      minHeight: '2px'
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center gap-4 text-sm text-gray-400 mb-2">
            <span>{formatTime(currentTime)}</span>
            <div className="flex-1">
              <Slider
                value={[duration ? (currentTime / duration) * 100 : 0]}
                onValueChange={handleSeek}
                max={100}
                step={0.1}
                className="w-full"
              />
            </div>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-gray-800"
          >
            <Shuffle className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-gray-800"
          >
            <SkipBack className="w-5 h-5" />
          </Button>
          
          <Button
            onClick={togglePlayPause}
            className="bg-blue-600 hover:bg-blue-700 text-white w-12 h-12 rounded-full"
          >
            {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-gray-800"
          >
            <SkipForward className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-gray-800"
          >
            <Repeat className="w-5 h-5" />
          </Button>
        </div>

        {/* Volume Control */}
        <div className="flex items-center justify-center gap-4 mb-6">
          <Button
            onClick={toggleMute}
            variant="ghost"
            size="sm"
            className="text-white hover:bg-gray-800"
          >
            {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
          </Button>
          <div className="w-32">
            <Slider
              value={[isMuted ? 0 : volume * 100]}
              onValueChange={handleVolumeChange}
              max={100}
              step={1}
            />
          </div>
          <span className="text-sm text-gray-400 w-8">
            {Math.round(isMuted ? 0 : volume * 100)}%
          </span>
        </div>

        {/* Transcription Section */}
        {content && (
          <div className="bg-gray-800 rounded-lg p-4">
            <h4 className="text-lg font-medium mb-3 flex items-center gap-2">
              <FileAudio className="w-5 h-5" />
              Audio Transcription
            </h4>
            <div className="bg-gray-900 rounded p-4 max-h-64 overflow-y-auto">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed text-gray-300">
                {content}
              </pre>
            </div>
          </div>
        )}
      </div>

      {/* Hidden Audio Element */}
      {fileUrl && (
        <audio
          ref={audioRef}
          src={fileUrl}
          preload="metadata"
        />
      )}

      {/* Status Bar */}
      <div className="bg-gray-800 text-gray-400 text-xs p-2 flex items-center justify-between border-t border-gray-700">
        <div className="flex items-center gap-4">
          <span>Ready</span>
          <span>•</span>
          <span>{isPlaying ? 'Playing' : 'Stopped'}</span>
        </div>
        <div className="flex items-center gap-2">
          <Clock className="w-3 h-3" />
          <span>{formatTime(currentTime)} / {formatTime(duration)}</span>
        </div>
      </div>
    </div>
  );
};

export default AudioFilePreview;
