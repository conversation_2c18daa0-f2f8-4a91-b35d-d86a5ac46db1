import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Download, 
  Maximize2,
  Minimize2,
  FileText,
  Image,
  Play,
  Pause,
  Volume2,
  Settings
} from 'lucide-react';
import WordDocumentPreview from './WordDocumentPreview';
import PDFDocumentPreview from './PDFDocumentPreview';
import PowerPointPreview from './PowerPointPreview';
import AudioFilePreview from './AudioFilePreview';
import VideoFilePreview from './VideoFilePreview';
import YouTubeVideoPreview from './YouTubeVideoPreview';
import WebsitePreview from './WebsitePreview';

interface NativeFilePreviewProps {
  fileName: string;
  content: string;
  htmlContent?: string;
  fileType: 'pdf' | 'word' | 'powerpoint' | 'text' | 'image' | 'audio' | 'video' | 'youtube' | 'website';
  fileUrl?: string;
  fileSize?: number;
  metadata?: any;
  onClose: () => void;
  onCreateTopic: () => void;
  embedded?: boolean; // New prop to control embedded mode
}

export const NativeFilePreview: React.FC<NativeFilePreviewProps> = ({
  fileName,
  content,
  htmlContent,
  fileType,
  fileUrl,
  fileSize,
  metadata,
  onClose,
  onCreateTopic,
  embedded = false
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoom, setZoom] = useState(100);

  const getFileIcon = () => {
    switch (fileType) {
      case 'word': return <FileText className="w-5 h-5 text-blue-600" />;
      case 'pdf': return <FileText className="w-5 h-5 text-red-600" />;
      case 'powerpoint': return <FileText className="w-5 h-5 text-orange-600" />;
      case 'image': return <Image className="w-5 h-5 text-purple-600" />;
      case 'audio': return <Volume2 className="w-5 h-5 text-green-600" />;
      case 'video': return <Play className="w-5 h-5 text-red-600" />;
      case 'youtube': return <Play className="w-5 h-5 text-red-600" />;
      case 'website': return <FileText className="w-5 h-5 text-blue-600" />;
      default: return <FileText className="w-5 h-5 text-gray-600" />;
    }
  };

  const getWindowTitle = () => {
    switch (fileType) {
      case 'word': return `${fileName} - Microsoft Word`;
      case 'pdf': return `${fileName} - Adobe Acrobat Reader`;
      case 'powerpoint': return `${fileName} - Microsoft PowerPoint`;
      case 'image': return `${fileName} - Windows Photo Viewer`;
      case 'audio': return `${fileName} - Windows Media Player`;
      case 'video': return `${fileName} - Windows Media Player`;
      case 'youtube': return `YouTube - ${fileName}`;
      case 'website': return `${fileName} - Microsoft Edge`;
      default: return `${fileName} - Notepad`;
    }
  };

  const getFileTypeColor = () => {
    switch (fileType) {
      case 'word': return 'bg-blue-50 border-blue-200';
      case 'pdf': return 'bg-red-50 border-red-200';
      case 'powerpoint': return 'bg-orange-50 border-orange-200';
      case 'image': return 'bg-purple-50 border-purple-200';
      case 'audio': return 'bg-green-50 border-green-200';
      case 'video': return 'bg-red-50 border-red-200';
      case 'youtube': return 'bg-red-50 border-red-200';
      case 'website': return 'bg-blue-50 border-blue-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const renderPreviewContent = () => {
    switch (fileType) {
      case 'word':
        return (
          <WordDocumentPreview
            fileName={fileName}
            content={content}
            htmlContent={htmlContent}
            zoom={zoom}
            metadata={metadata}
          />
        );
      case 'pdf':
        return (
          <PDFDocumentPreview
            fileName={fileName}
            content={content}
            fileUrl={fileUrl}
            zoom={zoom}
            metadata={metadata}
          />
        );
      case 'powerpoint':
        return (
          <PowerPointPreview
            fileName={fileName}
            content={content}
            zoom={zoom}
            metadata={metadata}
          />
        );
      case 'audio':
        return (
          <AudioFilePreview
            fileName={fileName}
            content={content}
            fileUrl={fileUrl}
            metadata={metadata}
          />
        );
      case 'video':
        return (
          <VideoFilePreview
            fileName={fileName}
            content={content}
            fileUrl={fileUrl}
            metadata={metadata}
          />
        );
      case 'youtube':
        return (
          <YouTubeVideoPreview
            fileName={fileName}
            content={content}
            videoUrl={fileUrl}
            metadata={metadata}
          />
        );
      case 'website':
        return (
          <WebsitePreview
            fileName={fileName}
            content={content}
            websiteUrl={fileUrl}
            metadata={metadata}
          />
        );
      default:
        return (
          <div className="p-6 bg-white">
            <pre className="whitespace-pre-wrap text-sm font-mono leading-relaxed">
              {content}
            </pre>
          </div>
        );
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-white' : embedded ? '' : 'mt-4'}`}>
      <Card className={`${embedded ? 'border-0 shadow-none' : `border-2 ${getFileTypeColor()}`} ${isFullscreen ? 'h-full rounded-none' : ''}`}>
        {/* Native Window Header - Only show if not embedded */}
        {!embedded && (
          <div className="flex items-center justify-between p-3 bg-gray-100 border-b border-gray-300">
          <div className="flex items-center gap-3">
            {getFileIcon()}
            <span className="font-medium text-gray-800 text-sm">
              {getWindowTitle()}
            </span>
            <Badge variant="secondary" className="text-xs">
              {fileType.toUpperCase()}
            </Badge>
          </div>
          
          {/* Window Controls */}
          <div className="flex items-center gap-2">
            {/* Zoom Controls */}
            {(fileType === 'word' || fileType === 'pdf' || fileType === 'powerpoint' || fileType === 'image') && (
              <div className="flex items-center gap-1 mr-2">
                <Button
                  onClick={() => setZoom(Math.max(25, zoom - 25))}
                  variant="ghost"
                  size="sm"
                  className="p-1 h-7 w-7"
                >
                  <ZoomOut className="w-3 h-3" />
                </Button>
                <span className="text-xs text-gray-600 min-w-[3rem] text-center">
                  {zoom}%
                </span>
                <Button
                  onClick={() => setZoom(Math.min(300, zoom + 25))}
                  variant="ghost"
                  size="sm"
                  className="p-1 h-7 w-7"
                >
                  <ZoomIn className="w-3 h-3" />
                </Button>
              </div>
            )}
            
            {/* Fullscreen Toggle */}
            <Button
              onClick={() => setIsFullscreen(!isFullscreen)}
              variant="ghost"
              size="sm"
              className="p-1 h-7 w-7"
            >
              {isFullscreen ? <Minimize2 className="w-3 h-3" /> : <Maximize2 className="w-3 h-3" />}
            </Button>
            
            {/* Close Button */}
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="p-1 h-7 w-7 hover:bg-red-100 hover:text-red-600"
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </div>
        )}

        {/* Preview Content */}
        <CardContent className="p-0">
          <div className={`${isFullscreen ? 'h-[calc(100vh-120px)]' : embedded ? 'max-h-[500px]' : 'max-h-[600px]'} overflow-auto`}>
            {renderPreviewContent()}
          </div>
        </CardContent>

        {/* Action Bar - Only show if not embedded */}
        {!embedded && (
          <div className="flex items-center justify-between p-4 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>File: {fileName}</span>
            {fileSize && (
              <span>Size: {(fileSize / 1024).toFixed(1)} KB</span>
            )}
            <span>Content: {content.length.toLocaleString()} chars</span>
          </div>
          
          <div className="flex gap-3">
            <Button
              onClick={onCreateTopic}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              ✨ Generate AI Study Materials
            </Button>
            <Button
              onClick={onClose}
              variant="outline"
            >
              Close
            </Button>
          </div>
        </div>
        )}
      </Card>
    </div>
  );
};

export default NativeFilePreview;
