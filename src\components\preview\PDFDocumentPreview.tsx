import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  ChevronLeft, 
  ChevronRight, 
  RotateCw, 
  Download, 
  Search,
  FileText,
  ZoomIn,
  ZoomOut,
  Maximize2
} from 'lucide-react';
import * as pdfjsLib from 'pdfjs-dist';

interface PDFDocumentPreviewProps {
  fileName: string;
  content: string;
  fileUrl?: string;
  zoom: number;
  metadata?: any;
}

const PDFDocumentPreview: React.FC<PDFDocumentPreviewProps> = ({
  fileName,
  content,
  fileUrl,
  zoom,
  metadata
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pdfDocument, setPdfDocument] = useState<any>(null);
  const [pageImages, setPageImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [rotation, setRotation] = useState(0);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    loadPDF();
  }, [fileUrl]);

  useEffect(() => {
    if (pdfDocument && currentPage) {
      renderPage(currentPage);
    }
  }, [pdfDocument, currentPage, zoom, rotation]);

  const loadPDF = async () => {
    if (!fileUrl) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      
      // Configure PDF.js worker
      if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
        pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
          'pdfjs-dist/build/pdf.worker.min.js',
          import.meta.url
        ).toString();
      }

      const pdf = await pdfjsLib.getDocument(fileUrl).promise;
      setPdfDocument(pdf);
      setTotalPages(pdf.numPages);
      
      // Pre-render all pages as thumbnails
      const images: string[] = [];
      for (let i = 1; i <= Math.min(pdf.numPages, 10); i++) {
        const page = await pdf.getPage(i);
        const viewport = page.getViewport({ scale: 0.2 });
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        
        await page.render({
          canvasContext: context,
          viewport: viewport
        }).promise;
        
        images.push(canvas.toDataURL());
      }
      setPageImages(images);
      setIsLoading(false);
    } catch (error) {
      console.error('Error loading PDF:', error);
      setIsLoading(false);
    }
  };

  const renderPage = async (pageNum: number) => {
    if (!pdfDocument || !canvasRef.current) return;

    try {
      const page = await pdfDocument.getPage(pageNum);
      const viewport = page.getViewport({ 
        scale: zoom / 100,
        rotation: rotation 
      });
      
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      await page.render({
        canvasContext: context,
        viewport: viewport
      }).promise;
    } catch (error) {
      console.error('Error rendering page:', error);
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading PDF document...</p>
        </div>
      </div>
    );
  }

  if (!pdfDocument && !content) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-100">
        <div className="text-center">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Unable to load PDF document</p>
          <p className="text-sm text-gray-500 mt-2">Showing extracted text content instead</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-200 min-h-full">
      {/* Adobe Reader-like Toolbar */}
      <div className="bg-gray-800 text-white p-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* Navigation Controls */}
          <div className="flex items-center gap-2">
            <Button
              onClick={() => goToPage(currentPage - 1)}
              disabled={currentPage <= 1}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-gray-700"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={currentPage}
                onChange={(e) => goToPage(parseInt(e.target.value))}
                className="w-16 h-8 text-center bg-gray-700 border-gray-600 text-white"
                min={1}
                max={totalPages}
              />
              <span className="text-sm">of {totalPages}</span>
            </div>
            
            <Button
              onClick={() => goToPage(currentPage + 1)}
              disabled={currentPage >= totalPages}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-gray-700"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          {/* Tool Controls */}
          <div className="flex items-center gap-2">
            <Button
              onClick={handleRotate}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-gray-700"
            >
              <RotateCw className="w-4 h-4" />
            </Button>
            
            <div className="flex items-center gap-1">
              <Search className="w-4 h-4" />
              <Input
                placeholder="Search in document..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48 h-8 bg-gray-700 border-gray-600 text-white placeholder-gray-400"
              />
            </div>
          </div>
        </div>

        {/* Document Info */}
        <div className="flex items-center gap-4 text-sm">
          <Badge variant="secondary" className="bg-gray-700 text-white">
            {fileName}
          </Badge>
          <span>Zoom: {zoom}%</span>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex h-full">
        {/* Thumbnail Sidebar */}
        <div className="w-48 bg-gray-100 border-r border-gray-300 p-2 overflow-y-auto">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Pages</h3>
          <div className="space-y-2">
            {pageImages.map((image, index) => (
              <div
                key={index}
                onClick={() => setCurrentPage(index + 1)}
                className={`cursor-pointer border-2 rounded p-1 ${
                  currentPage === index + 1 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <img 
                  src={image} 
                  alt={`Page ${index + 1}`}
                  className="w-full h-auto"
                />
                <p className="text-xs text-center mt-1 text-gray-600">
                  {index + 1}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Document Viewer */}
        <div className="flex-1 overflow-auto p-4 flex justify-center">
          {pdfDocument ? (
            <div className="bg-white shadow-lg">
              <canvas 
                ref={canvasRef}
                className="max-w-full h-auto"
                style={{
                  transform: `scale(${zoom / 100})`,
                  transformOrigin: 'top center'
                }}
              />
            </div>
          ) : (
            // Fallback to text content
            <div className="bg-white shadow-lg p-8 max-w-4xl">
              <div className="mb-4 pb-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-800">
                  {fileName}
                </h2>
                <p className="text-sm text-gray-600">
                  PDF content extracted as text
                </p>
              </div>
              <pre className="whitespace-pre-wrap text-sm leading-relaxed font-mono">
                {content}
              </pre>
            </div>
          )}
        </div>
      </div>

      {/* Status Bar */}
      <div className="bg-gray-800 text-white text-xs p-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <span>Page {currentPage} of {totalPages}</span>
          <span>Ready</span>
        </div>
        <div className="flex items-center gap-2">
          <span>Adobe Acrobat Reader</span>
        </div>
      </div>
    </div>
  );
};

export default PDFDocumentPreview;
