import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronLeft, 
  ChevronRight, 
  Play, 
  Pause, 
  RotateCw,
  Presentation,
  FileText,
  Image as ImageIcon,
  List,
  BarChart3,
  Layers
} from 'lucide-react';

interface PowerPointPreviewProps {
  fileName: string;
  content: string;
  zoom: number;
  metadata?: any;
}

interface Slide {
  id: number;
  title: string;
  content: string;
  type: 'title' | 'content' | 'image' | 'chart' | 'blank';
}

const PowerPointPreview: React.FC<PowerPointPreviewProps> = ({
  fileName,
  content,
  zoom,
  metadata
}) => {
  const [currentSlide, setCurrentSlide] = useState(1);
  const [slides, setSlides] = useState<Slide[]>([]);
  const [viewMode, setViewMode] = useState<'normal' | 'outline' | 'slideshow'>('normal');
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    parseContentIntoSlides();
  }, [content]);

  const parseContentIntoSlides = () => {
    // Parse the content to extract slides
    const lines = content.split('\n');
    const parsedSlides: Slide[] = [];
    let currentSlideContent = '';
    let currentSlideTitle = '';
    let slideId = 1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Detect slide breaks (common patterns)
      if (line.startsWith('# ') || line.startsWith('## ') || 
          line.toLowerCase().includes('slide ') || 
          line.match(/^(Slide|Page|Section)\s*\d+/i)) {
        
        // Save previous slide if it exists
        if (currentSlideTitle || currentSlideContent) {
          parsedSlides.push({
            id: slideId++,
            title: currentSlideTitle || `Slide ${slideId - 1}`,
            content: currentSlideContent.trim(),
            type: determineSlideType(currentSlideContent)
          });
        }
        
        // Start new slide
        currentSlideTitle = line.replace(/^#+\s*/, '').replace(/^(Slide|Page|Section)\s*\d+:?\s*/i, '');
        currentSlideContent = '';
      } else if (line) {
        currentSlideContent += line + '\n';
      }
    }

    // Add the last slide
    if (currentSlideTitle || currentSlideContent) {
      parsedSlides.push({
        id: slideId,
        title: currentSlideTitle || `Slide ${slideId}`,
        content: currentSlideContent.trim(),
        type: determineSlideType(currentSlideContent)
      });
    }

    // If no slides were parsed, create a single slide with all content
    if (parsedSlides.length === 0) {
      parsedSlides.push({
        id: 1,
        title: fileName.replace(/\.(ppt|pptx)$/i, ''),
        content: content,
        type: 'content'
      });
    }

    setSlides(parsedSlides);
  };

  const determineSlideType = (content: string): Slide['type'] => {
    if (content.toLowerCase().includes('chart') || content.toLowerCase().includes('graph')) {
      return 'chart';
    }
    if (content.toLowerCase().includes('image') || content.toLowerCase().includes('figure')) {
      return 'image';
    }
    if (content.includes('•') || content.includes('-') || content.includes('*')) {
      return 'content';
    }
    if (content.length < 50) {
      return 'title';
    }
    return 'content';
  };

  const getSlideIcon = (type: Slide['type']) => {
    switch (type) {
      case 'title': return <FileText className="w-4 h-4" />;
      case 'content': return <List className="w-4 h-4" />;
      case 'image': return <ImageIcon className="w-4 h-4" />;
      case 'chart': return <BarChart3 className="w-4 h-4" />;
      default: return <Layers className="w-4 h-4" />;
    }
  };

  const goToSlide = (slideNumber: number) => {
    if (slideNumber >= 1 && slideNumber <= slides.length) {
      setCurrentSlide(slideNumber);
    }
  };

  const nextSlide = () => {
    if (currentSlide < slides.length) {
      setCurrentSlide(currentSlide + 1);
    }
  };

  const prevSlide = () => {
    if (currentSlide > 1) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  const formatSlideContent = (content: string) => {
    // Format content with basic styling
    return content
      .split('\n')
      .map((line, index) => {
        const trimmed = line.trim();
        if (!trimmed) return null;
        
        // Bullet points
        if (trimmed.startsWith('•') || trimmed.startsWith('-') || trimmed.startsWith('*')) {
          return (
            <li key={index} className="ml-4 mb-2">
              {trimmed.substring(1).trim()}
            </li>
          );
        }
        
        // Headers
        if (trimmed.startsWith('**') && trimmed.endsWith('**')) {
          return (
            <h3 key={index} className="font-bold text-lg mb-3 text-gray-800">
              {trimmed.slice(2, -2)}
            </h3>
          );
        }
        
        // Regular paragraphs
        return (
          <p key={index} className="mb-3 leading-relaxed">
            {trimmed}
          </p>
        );
      })
      .filter(Boolean);
  };

  const currentSlideData = slides[currentSlide - 1];

  return (
    <div className="flex h-full bg-gray-100">
      {/* Slide Thumbnails Panel */}
      <div className="w-64 bg-white border-r border-gray-300 overflow-y-auto">
        <div className="p-3 border-b border-gray-200 bg-gray-50">
          <h3 className="font-semibold text-sm text-gray-700 flex items-center gap-2">
            <Presentation className="w-4 h-4" />
            Slides ({slides.length})
          </h3>
        </div>
        
        <div className="p-2 space-y-2">
          {slides.map((slide, index) => (
            <div
              key={slide.id}
              onClick={() => goToSlide(slide.id)}
              className={`p-3 rounded-lg cursor-pointer transition-all duration-200 border ${
                currentSlide === slide.id
                  ? 'bg-blue-50 border-blue-300 shadow-sm'
                  : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2 mb-2">
                <span className="text-xs font-medium text-gray-500">
                  {slide.id}
                </span>
                {getSlideIcon(slide.type)}
                <Badge variant="outline" className="text-xs">
                  {slide.type}
                </Badge>
              </div>
              
              {/* Slide Thumbnail */}
              <div className="bg-white border border-gray-200 rounded p-2 mb-2">
                <div className="text-xs font-medium text-gray-700 mb-1 truncate">
                  {slide.title}
                </div>
                <div className="text-xs text-gray-500 line-clamp-3">
                  {slide.content.substring(0, 100)}...
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Slide View */}
      <div className="flex-1 flex flex-col">
        {/* Slide Controls */}
        <div className="flex items-center justify-between p-3 bg-white border-b border-gray-300">
          <div className="flex items-center gap-3">
            <Button
              onClick={prevSlide}
              disabled={currentSlide === 1}
              variant="outline"
              size="sm"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            
            <span className="text-sm font-medium text-gray-700">
              Slide {currentSlide} of {slides.length}
            </span>
            
            <Button
              onClick={nextSlide}
              disabled={currentSlide === slides.length}
              variant="outline"
              size="sm"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              onClick={() => setViewMode(viewMode === 'normal' ? 'outline' : 'normal')}
              variant="outline"
              size="sm"
            >
              {viewMode === 'normal' ? 'Outline' : 'Normal'}
            </Button>
            
            <Button
              onClick={() => setIsPlaying(!isPlaying)}
              variant="outline"
              size="sm"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
          </div>
        </div>

        {/* Slide Content */}
        <div className="flex-1 overflow-auto p-6 flex justify-center">
          {currentSlideData && (
            <div 
              className="bg-white shadow-lg border border-gray-300 rounded-lg overflow-hidden"
              style={{
                width: `${Math.min(800, 800 * (zoom / 100))}px`,
                height: `${Math.min(600, 600 * (zoom / 100))}px`,
                transform: `scale(${zoom / 100})`,
                transformOrigin: 'top center'
              }}
            >
              <div className="p-8 h-full flex flex-col">
                {/* Slide Title */}
                <h1 className="text-2xl font-bold text-gray-800 mb-6 text-center border-b border-gray-200 pb-4">
                  {currentSlideData.title}
                </h1>
                
                {/* Slide Content */}
                <div className="flex-1 overflow-auto">
                  <div className="prose max-w-none">
                    {formatSlideContent(currentSlideData.content)}
                  </div>
                </div>
                
                {/* Slide Footer */}
                <div className="mt-6 pt-4 border-t border-gray-200 text-center">
                  <span className="text-sm text-gray-500">
                    {fileName} • Slide {currentSlide}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PowerPointPreview;
