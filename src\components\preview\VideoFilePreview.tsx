import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  Play, 
  Pause, 
  Square, 
  SkipBack, 
  SkipForward, 
  Volume2, 
  VolumeX,
  Maximize2,
  Settings,
  Film,
  Clock,
  Monitor,
  FileVideo
} from 'lucide-react';

interface VideoFilePreviewProps {
  fileName: string;
  content: string;
  fileUrl?: string;
  metadata?: any;
}

const VideoFilePreview: React.FC<VideoFilePreviewProps> = ({
  fileName,
  content,
  fileUrl,
  metadata
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (videoRef.current) {
      const video = videoRef.current;
      
      const handleLoadedMetadata = () => {
        setDuration(video.duration);
      };
      
      const handleTimeUpdate = () => {
        setCurrentTime(video.currentTime);
      };
      
      const handleEnded = () => {
        setIsPlaying(false);
        setCurrentTime(0);
      };

      const handleLoadStart = () => setIsLoading(true);
      const handleCanPlay = () => setIsLoading(false);

      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('timeupdate', handleTimeUpdate);
      video.addEventListener('ended', handleEnded);
      video.addEventListener('loadstart', handleLoadStart);
      video.addEventListener('canplay', handleCanPlay);

      return () => {
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
        video.removeEventListener('timeupdate', handleTimeUpdate);
        video.removeEventListener('ended', handleEnded);
        video.removeEventListener('loadstart', handleLoadStart);
        video.removeEventListener('canplay', handleCanPlay);
      };
    }
  }, [fileUrl]);

  const togglePlayPause = () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (value: number[]) => {
    if (!videoRef.current) return;
    const newTime = (value[0] / 100) * duration;
    videoRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (value: number[]) => {
    if (!videoRef.current) return;
    const newVolume = value[0] / 100;
    videoRef.current.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    if (!videoRef.current) return;
    if (isMuted) {
      videoRef.current.volume = volume;
      setIsMuted(false);
    } else {
      videoRef.current.volume = 0;
      setIsMuted(true);
    }
  };

  const toggleFullscreen = () => {
    if (!containerRef.current) return;
    
    if (!isFullscreen) {
      containerRef.current.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    setIsFullscreen(!isFullscreen);
  };

  const formatTime = (time: number) => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getVideoInfo = () => {
    const fileExtension = fileName.split('.').pop()?.toUpperCase() || 'VIDEO';
    const fileSizeKB = metadata?.size ? Math.round(metadata.size / 1024) : 'Unknown';
    
    return {
      format: fileExtension,
      size: fileSizeKB,
      resolution: metadata?.resolution || 'Unknown',
      codec: metadata?.codec || 'Unknown'
    };
  };

  const videoInfo = getVideoInfo();

  return (
    <div 
      ref={containerRef}
      className="bg-black text-white min-h-full relative"
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      {/* Windows Media Player Header */}
      <div className="bg-gray-900 p-4 border-b border-gray-800">
        <div className="flex items-center gap-3">
          <Film className="w-6 h-6 text-blue-400" />
          <div>
            <h2 className="text-lg font-medium">{fileName}</h2>
            <p className="text-sm text-gray-400">Windows Media Player</p>
          </div>
        </div>
      </div>

      {/* Video Player Area */}
      <div className="relative bg-black flex items-center justify-center min-h-96">
        {fileUrl ? (
          <video
            ref={videoRef}
            src={fileUrl}
            className="max-w-full max-h-full"
            onClick={togglePlayPause}
            poster={metadata?.thumbnail}
          />
        ) : (
          <div className="text-center">
            <FileVideo className="w-24 h-24 text-gray-600 mx-auto mb-4" />
            <p className="text-gray-400">Video file not available for preview</p>
            <p className="text-sm text-gray-500 mt-2">Showing metadata and transcription below</p>
          </div>
        )}

        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-white">Loading video...</p>
            </div>
          </div>
        )}

        {/* Video Controls Overlay */}
        {showControls && fileUrl && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
            {/* Progress Bar */}
            <div className="mb-4">
              <Slider
                value={[duration ? (currentTime / duration) * 100 : 0]}
                onValueChange={handleSeek}
                max={100}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Control Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  onClick={togglePlayPause}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-gray-800"
                >
                  {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-gray-800"
                >
                  <Square className="w-4 h-4" />
                </Button>

                <div className="flex items-center gap-2">
                  <Button
                    onClick={toggleMute}
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-gray-800"
                  >
                    {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                  </Button>
                  <div className="w-20">
                    <Slider
                      value={[isMuted ? 0 : volume * 100]}
                      onValueChange={handleVolumeChange}
                      max={100}
                      step={1}
                    />
                  </div>
                </div>

                <span className="text-sm text-gray-300">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-gray-800"
                >
                  <Settings className="w-4 h-4" />
                </Button>
                
                <Button
                  onClick={toggleFullscreen}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-gray-800"
                >
                  <Maximize2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Video Information Panel */}
      <div className="bg-gray-900 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Video Details */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Video Information
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Format:</span>
                <span>{videoInfo.format}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">File Size:</span>
                <span>{videoInfo.size} KB</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Duration:</span>
                <span>{formatTime(duration)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Resolution:</span>
                <span>{videoInfo.resolution}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Codec:</span>
                <span>{videoInfo.codec}</span>
              </div>
            </div>
          </div>

          {/* Playback Statistics */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Playback Status
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Status:</span>
                <Badge variant={isPlaying ? "default" : "secondary"}>
                  {isPlaying ? 'Playing' : 'Paused'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Current Time:</span>
                <span>{formatTime(currentTime)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Volume:</span>
                <span>{Math.round(isMuted ? 0 : volume * 100)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Progress:</span>
                <span>{duration ? Math.round((currentTime / duration) * 100) : 0}%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Transcription Section */}
        {content && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <FileVideo className="w-5 h-5" />
              Video Transcription
            </h3>
            <div className="bg-gray-800 rounded-lg p-4 max-h-64 overflow-y-auto">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed text-gray-300">
                {content}
              </pre>
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="bg-gray-800 text-gray-400 text-xs p-2 flex items-center justify-between border-t border-gray-700">
        <div className="flex items-center gap-4">
          <span>Ready</span>
          <span>•</span>
          <span>{isPlaying ? 'Playing' : 'Stopped'}</span>
        </div>
        <div className="flex items-center gap-2">
          <Clock className="w-3 h-3" />
          <span>{formatTime(currentTime)} / {formatTime(duration)}</span>
        </div>
      </div>
    </div>
  );
};

export default VideoFilePreview;
