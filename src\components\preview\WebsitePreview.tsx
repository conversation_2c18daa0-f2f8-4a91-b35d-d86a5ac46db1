import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Globe, 
  ArrowLeft, 
  ArrowRight, 
  RotateCw, 
  Home, 
  Star, 
  Shield, 
  Settings,
  Search,
  Bookmark,
  Download,
  Share,
  ExternalLink
} from 'lucide-react';

interface WebsitePreviewProps {
  fileName: string;
  content: string;
  websiteUrl?: string;
  metadata?: any;
}

const WebsitePreview: React.FC<WebsitePreviewProps> = ({
  fileName,
  content,
  websiteUrl,
  metadata
}) => {
  const [currentUrl, setCurrentUrl] = useState(websiteUrl || '');
  const [pageTitle, setPageTitle] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSecure, setIsSecure] = useState(false);

  useEffect(() => {
    if (websiteUrl) {
      setCurrentUrl(websiteUrl);
      setIsSecure(websiteUrl.startsWith('https://'));
      
      // Extract page title from metadata or URL
      if (metadata?.title) {
        setPageTitle(metadata.title);
      } else {
        const domain = new URL(websiteUrl).hostname;
        setPageTitle(domain);
      }
    }
  }, [websiteUrl, metadata]);

  const getDomainInfo = () => {
    if (!currentUrl) return { domain: '', protocol: '' };
    
    try {
      const url = new URL(currentUrl);
      return {
        domain: url.hostname,
        protocol: url.protocol,
        pathname: url.pathname
      };
    } catch {
      return { domain: currentUrl, protocol: '', pathname: '' };
    }
  };

  const domainInfo = getDomainInfo();

  const formatContent = (text: string) => {
    // Basic HTML-like formatting for better readability
    return text
      .split('\n\n')
      .map(paragraph => paragraph.trim())
      .filter(paragraph => paragraph.length > 0)
      .map((paragraph, index) => {
        // Check if it looks like a heading
        if (paragraph.length < 100 && !paragraph.includes('.') && paragraph === paragraph.toUpperCase()) {
          return (
            <h2 key={index} className="text-xl font-bold text-gray-900 mb-4 mt-6">
              {paragraph}
            </h2>
          );
        }
        
        // Check if it looks like a subheading
        if (paragraph.length < 80 && paragraph.endsWith(':')) {
          return (
            <h3 key={index} className="text-lg font-semibold text-gray-800 mb-3 mt-4">
              {paragraph}
            </h3>
          );
        }
        
        // Regular paragraph
        return (
          <p key={index} className="text-gray-700 leading-relaxed mb-4">
            {paragraph}
          </p>
        );
      });
  };

  return (
    <div className="bg-white min-h-full">
      {/* Browser Header */}
      <div className="bg-gray-100 border-b border-gray-300">
        {/* Tab Bar */}
        <div className="flex items-center bg-gray-200 px-2 py-1">
          <div className="bg-white border border-gray-300 rounded-t-lg px-4 py-2 flex items-center gap-2 max-w-xs">
            <Globe className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-800 truncate">
              {pageTitle || domainInfo.domain}
            </span>
          </div>
          <div className="flex-1"></div>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" className="p-1">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Navigation Bar */}
        <div className="flex items-center gap-2 p-2">
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" disabled>
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" disabled>
              <ArrowRight className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <RotateCw className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Home className="w-4 h-4" />
            </Button>
          </div>

          {/* Address Bar */}
          <div className="flex-1 flex items-center bg-white border border-gray-300 rounded-md px-3 py-1">
            <div className="flex items-center gap-2 mr-2">
              {isSecure ? (
                <Shield className="w-4 h-4 text-green-600" />
              ) : (
                <Globe className="w-4 h-4 text-gray-600" />
              )}
            </div>
            <Input
              value={currentUrl}
              onChange={(e) => setCurrentUrl(e.target.value)}
              className="border-0 p-0 text-sm focus:ring-0"
              placeholder="Search or enter web address"
            />
          </div>

          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm">
              <Star className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Bookmark className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Share className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Page Content */}
      <div className="min-h-96">
        {isLoading ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading website...</p>
            </div>
          </div>
        ) : (
          <div className="max-w-4xl mx-auto p-6">
            {/* Website Header */}
            <div className="mb-6 pb-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    {pageTitle || fileName}
                  </h1>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <Globe className="w-4 h-4" />
                      {domainInfo.domain}
                    </span>
                    {isSecure && (
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        <Shield className="w-3 h-3 mr-1" />
                        Secure
                      </Badge>
                    )}
                  </div>
                </div>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <ExternalLink className="w-4 h-4" />
                  Open Original
                </Button>
              </div>
            </div>

            {/* Website Metadata */}
            {metadata && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Page Info</h3>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div>Title: {metadata.title || 'Unknown'}</div>
                    <div>Type: {metadata.type || 'Website'}</div>
                    <div>Language: {metadata.language || 'Unknown'}</div>
                  </div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Content</h3>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div>Words: {content.split(/\s+/).length}</div>
                    <div>Characters: {content.length}</div>
                    <div>Paragraphs: {content.split('\n\n').length}</div>
                  </div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Source</h3>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div>Domain: {domainInfo.domain}</div>
                    <div>Protocol: {domainInfo.protocol}</div>
                    <div>Secure: {isSecure ? 'Yes' : 'No'}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Main Content */}
            <div className="bg-white">
              <article className="prose prose-lg max-w-none">
                {formatContent(content)}
              </article>
            </div>

            {/* Page Actions */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span>Last updated: {new Date().toLocaleDateString()}</span>
                  <span>•</span>
                  <span>Content extracted for AI processing</span>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="w-4 h-4 mr-2" />
                    Save Page
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Browser Status Bar */}
      <div className="bg-gray-100 border-t border-gray-300 text-xs p-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <span>Done</span>
          <span>•</span>
          <span>{content.length} characters loaded</span>
        </div>
        <div className="flex items-center gap-2">
          <span>Microsoft Edge</span>
          <span>•</span>
          <span>100% zoom</span>
        </div>
      </div>
    </div>
  );
};

export default WebsitePreview;
