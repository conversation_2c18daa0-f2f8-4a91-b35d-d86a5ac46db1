import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  AlignLeft, 
  AlignCenter, 
  AlignRight, 
  Bold, 
  Italic, 
  Underline,
  List,
  ListOrdered,
  Image as ImageIcon
} from 'lucide-react';

interface WordDocumentPreviewProps {
  fileName: string;
  content: string;
  htmlContent?: string;
  zoom: number;
  metadata?: any;
}

const WordDocumentPreview: React.FC<WordDocumentPreviewProps> = ({
  fileName,
  content,
  htmlContent,
  zoom,
  metadata
}) => {
  const [viewMode, setViewMode] = useState<'print' | 'web' | 'outline'>('print');

  // Enhanced HTML content processing for Word-like appearance
  const processWordHTML = (html: string) => {
    if (!html) return content;
    
    // Add Word-like styling
    return html
      .replace(/<p>/g, '<p style="margin: 0 0 12px 0; line-height: 1.15; font-family: <PERSON><PERSON>ri, sans-serif; font-size: 11pt;">')
      .replace(/<h1>/g, '<h1 style="font-family: Calibri Light, sans-serif; font-size: 16pt; color: #2F5496; margin: 0 0 12px 0;">')
      .replace(/<h2>/g, '<h2 style="font-family: Calibri Light, sans-serif; font-size: 13pt; color: #2F5496; margin: 0 0 12px 0;">')
      .replace(/<h3>/g, '<h3 style="font-family: Calibri, sans-serif; font-size: 12pt; color: #1F3864; margin: 0 0 12px 0;">')
      .replace(/<img/g, '<img style="max-width: 100%; height: auto; margin: 12px 0;"')
      .replace(/<ul>/g, '<ul style="margin: 0 0 12px 0; padding-left: 36px;">')
      .replace(/<ol>/g, '<ol style="margin: 0 0 12px 0; padding-left: 36px;">')
      .replace(/<li>/g, '<li style="margin: 0 0 6px 0; line-height: 1.15;">')
      .replace(/<table>/g, '<table style="border-collapse: collapse; margin: 12px 0; width: 100%;">')
      .replace(/<td>/g, '<td style="border: 1px solid #D0D7DE; padding: 6px 12px; font-size: 11pt;">')
      .replace(/<th>/g, '<th style="border: 1px solid #D0D7DE; padding: 6px 12px; background-color: #F6F8FA; font-weight: bold; font-size: 11pt;">');
  };

  const formatPlainText = (text: string) => {
    return text
      .split('\n\n')
      .map(paragraph => paragraph.trim())
      .filter(paragraph => paragraph.length > 0)
      .map((paragraph, index) => (
        <p 
          key={index}
          style={{
            margin: '0 0 12px 0',
            lineHeight: '1.15',
            fontFamily: 'Calibri, sans-serif',
            fontSize: '11pt',
            textAlign: 'left'
          }}
        >
          {paragraph}
        </p>
      ));
  };

  return (
    <div className="bg-gray-100 min-h-full">
      {/* Word-like Ribbon/Toolbar */}
      <div className="bg-white border-b border-gray-300 p-2">
        <div className="flex items-center gap-4">
          {/* View Mode Tabs */}
          <div className="flex bg-gray-100 rounded">
            <Button
              onClick={() => setViewMode('print')}
              variant={viewMode === 'print' ? 'default' : 'ghost'}
              size="sm"
              className="text-xs rounded-r-none"
            >
              Print Layout
            </Button>
            <Button
              onClick={() => setViewMode('web')}
              variant={viewMode === 'web' ? 'default' : 'ghost'}
              size="sm"
              className="text-xs rounded-none"
            >
              Web Layout
            </Button>
            <Button
              onClick={() => setViewMode('outline')}
              variant={viewMode === 'outline' ? 'default' : 'ghost'}
              size="sm"
              className="text-xs rounded-l-none"
            >
              Outline
            </Button>
          </div>

          {/* Formatting Indicators */}
          <div className="flex items-center gap-2 text-gray-500">
            <Badge variant="outline" className="text-xs">
              <FileText className="w-3 h-3 mr-1" />
              Calibri 11pt
            </Badge>
            <Badge variant="outline" className="text-xs">
              Pages: {Math.ceil(content.length / 3000)}
            </Badge>
            <Badge variant="outline" className="text-xs">
              Words: {content.split(/\s+/).length}
            </Badge>
          </div>
        </div>
      </div>

      {/* Document Content Area */}
      <div className="p-8 flex justify-center">
        <div 
          className="bg-white shadow-lg"
          style={{
            width: viewMode === 'web' ? '100%' : '8.5in',
            minHeight: viewMode === 'web' ? 'auto' : '11in',
            padding: '1in',
            transform: `scale(${zoom / 100})`,
            transformOrigin: 'top center',
            marginBottom: zoom !== 100 ? `${(zoom - 100) * 2}px` : '0'
          }}
        >
          {/* Document Header */}
          <div className="mb-6 pb-4 border-b border-gray-200">
            <div className="flex items-center gap-2 text-gray-600 text-sm">
              <FileText className="w-4 h-4" />
              <span>{fileName}</span>
            </div>
          </div>

          {/* Document Content */}
          <div className="prose prose-sm max-w-none">
            {htmlContent ? (
              <div 
                dangerouslySetInnerHTML={{ 
                  __html: processWordHTML(htmlContent) 
                }}
                style={{
                  fontFamily: 'Calibri, sans-serif',
                  fontSize: '11pt',
                  lineHeight: '1.15',
                  color: '#000000'
                }}
              />
            ) : (
              <div style={{
                fontFamily: 'Calibri, sans-serif',
                fontSize: '11pt',
                lineHeight: '1.15',
                color: '#000000'
              }}>
                {formatPlainText(content)}
              </div>
            )}
          </div>

          {/* Document Footer */}
          <div className="mt-8 pt-4 border-t border-gray-200 text-center text-xs text-gray-500">
            Page 1 of {Math.ceil(content.length / 3000)}
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="bg-blue-600 text-white text-xs p-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <span>Page 1 of {Math.ceil(content.length / 3000)}</span>
          <span>Words: {content.split(/\s+/).length}</span>
          <span>Characters: {content.length}</span>
        </div>
        <div className="flex items-center gap-2">
          <span>Zoom: {zoom}%</span>
          <span>English (United States)</span>
        </div>
      </div>
    </div>
  );
};

export default WordDocumentPreview;
