import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  ThumbsUp, 
  ThumbsDown, 
  Share, 
  Download, 
  MoreHorizontal,
  Eye,
  Calendar,
  Clock,
  Youtube,
  FileText,
  User
} from 'lucide-react';

interface YouTubeVideoPreviewProps {
  fileName: string;
  content: string;
  videoUrl?: string;
  metadata?: any;
}

const YouTubeVideoPreview: React.FC<YouTubeVideoPreviewProps> = ({
  fileName,
  content,
  videoUrl,
  metadata
}) => {
  const [videoId, setVideoId] = useState<string>('');
  const [videoInfo, setVideoInfo] = useState<any>(null);

  useEffect(() => {
    if (videoUrl) {
      const id = extractVideoId(videoUrl);
      setVideoId(id);
      
      // Set mock video info (in real implementation, you'd fetch from YouTube API)
      setVideoInfo({
        title: metadata?.title || fileName || 'YouTube Video',
        channelName: metadata?.channelName || 'Unknown Channel',
        views: metadata?.views || '1,234,567',
        likes: metadata?.likes || '12K',
        uploadDate: metadata?.uploadDate || '2024-01-15',
        duration: metadata?.duration || '10:30',
        description: metadata?.description || 'Video description would appear here...',
        subscribers: metadata?.subscribers || '100K'
      });
    }
  }, [videoUrl, metadata, fileName]);

  const extractVideoId = (url: string): string => {
    const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/;
    const match = url.match(regex);
    return match ? match[1] : '';
  };

  const formatNumber = (num: string | number): string => {
    if (typeof num === 'string') return num;
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <div className="bg-gray-100 min-h-full">
      {/* YouTube Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-3">
          <Youtube className="w-8 h-8 text-red-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">YouTube</h2>
            <p className="text-sm text-gray-600">Video Player</p>
          </div>
        </div>
      </div>

      {/* Video Player Container */}
      <div className="bg-black">
        <div className="max-w-6xl mx-auto">
          {videoId ? (
            <div className="relative" style={{ paddingBottom: '56.25%', height: 0 }}>
              <iframe
                src={`https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1`}
                title="YouTube video player"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                className="absolute top-0 left-0 w-full h-full"
              />
            </div>
          ) : (
            <div className="flex items-center justify-center h-96">
              <div className="text-center text-white">
                <Youtube className="w-24 h-24 text-red-600 mx-auto mb-4" />
                <p className="text-xl mb-2">YouTube Video</p>
                <p className="text-gray-400">Video player not available</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Video Information */}
      <div className="max-w-6xl mx-auto bg-white">
        <div className="p-6">
          {/* Video Title and Actions */}
          <div className="mb-4">
            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              {videoInfo?.title || fileName}
            </h1>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{videoInfo?.views} views</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{videoInfo?.uploadDate ? formatDate(videoInfo.uploadDate) : 'Unknown date'}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{videoInfo?.duration}</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <ThumbsUp className="w-4 h-4" />
                  <span>{videoInfo?.likes}</span>
                </Button>
                <Button variant="outline" size="sm">
                  <ThumbsDown className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Share className="w-4 h-4" />
                  Share
                </Button>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Download className="w-4 h-4" />
                  Download
                </Button>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Channel Information */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg mb-6">
            <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-gray-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">{videoInfo?.channelName}</h3>
              <p className="text-sm text-gray-600">{videoInfo?.subscribers} subscribers</p>
            </div>
            <Button className="bg-red-600 hover:bg-red-700 text-white">
              Subscribe
            </Button>
          </div>

          {/* Video Description */}
          <div className="mb-6">
            <h3 className="font-medium text-gray-900 mb-2">Description</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-700 leading-relaxed">
                {videoInfo?.description}
              </p>
            </div>
          </div>

          {/* Video Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{videoInfo?.views}</div>
              <div className="text-sm text-gray-600">Views</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{videoInfo?.likes}</div>
              <div className="text-sm text-gray-600">Likes</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{videoInfo?.duration}</div>
              <div className="text-sm text-gray-600">Duration</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{videoInfo?.subscribers}</div>
              <div className="text-sm text-gray-600">Subscribers</div>
            </div>
          </div>

          {/* Transcript Section */}
          {content && (
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Video Transcript
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm leading-relaxed text-gray-700 font-sans">
                  {content}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Comments Section Placeholder */}
      <div className="max-w-6xl mx-auto bg-white border-t border-gray-200">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Comments
          </h3>
          <div className="bg-gray-50 rounded-lg p-8 text-center">
            <p className="text-gray-600">Comments would appear here in a full implementation</p>
          </div>
        </div>
      </div>

      {/* YouTube Footer */}
      <div className="bg-gray-800 text-white text-sm p-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Youtube className="w-5 h-5 text-red-500" />
            <span>YouTube Video Player</span>
          </div>
          <div className="flex items-center gap-4">
            <span>Video ID: {videoId}</span>
            <Badge variant="secondary">
              {videoInfo?.duration}
            </Badge>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YouTubeVideoPreview;
