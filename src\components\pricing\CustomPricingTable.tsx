import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Sparkles, Zap } from 'lucide-react';

interface CustomPricingTableProps {
  className?: string;
  onSelectPlan?: (plan: 'monthly' | 'yearly') => void;
}

const CustomPricingTable: React.FC<CustomPricingTableProps> = ({ className, onSelectPlan }) => {
  const handleSelectPlan = (plan: 'monthly' | 'yearly') => {
    if (onSelectPlan) {
      onSelectPlan(plan);
    }
  };

  const monthlyPrice = 9.99;
  const yearlyPrice = 59.99;
  const yearlyMonthlyEquivalent = yearlyPrice / 12;
  const savingsPercentage = Math.round((1 - yearlyMonthlyEquivalent / monthlyPrice) * 100);

  return (
    <div className={`${className} py-8`}>
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
        <p className="text-lg text-gray-600">Unlock the full potential of EZMind AI</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        {/* Monthly Plan */}
        <Card className="relative border-2 border-gray-200 hover:border-purple-300 transition-colors">
          <CardHeader className="text-center pb-4">
            <CardTitle className="text-xl font-bold text-gray-900">Monthly</CardTitle>
            <div className="mt-4">
              <span className="text-4xl font-bold text-gray-900">${monthlyPrice}</span>
              <span className="text-gray-600">/month</span>
            </div>
            <p className="text-sm text-gray-500 mt-2">Billed monthly</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Unlimited AI generations</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Advanced study tools</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Priority support</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Export & sharing features</span>
              </div>
            </div>
            <Button 
              onClick={() => handleSelectPlan('monthly')}
              className="w-full mt-6 bg-purple-600 hover:bg-purple-700"
            >
              <Crown className="w-4 h-4 mr-2" />
              Choose Monthly Plan
            </Button>
          </CardContent>
        </Card>

        {/* Yearly Plan */}
        <Card className="relative border-2 border-purple-500 hover:border-purple-600 transition-colors">
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1">
              <Sparkles className="w-3 h-3 mr-1" />
              BEST VALUE - {savingsPercentage}% OFF
            </Badge>
          </div>
          <CardHeader className="text-center pb-4 pt-6">
            <CardTitle className="text-xl font-bold text-gray-900">Yearly</CardTitle>
            <div className="mt-4">
              <div className="flex items-center justify-center gap-2">
                <span className="text-2xl text-gray-400 line-through">${(monthlyPrice * 12).toFixed(0)}</span>
                <span className="text-4xl font-bold text-purple-600">${yearlyPrice}</span>
              </div>
              <div className="text-center mt-2">
                <span className="text-lg text-purple-600 font-semibold">${yearlyMonthlyEquivalent.toFixed(2)}/month</span>
                <span className="text-gray-600"> (billed yearly)</span>
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3 mt-3">
              <p className="text-sm font-medium text-purple-800">
                Save ${((monthlyPrice * 12) - yearlyPrice).toFixed(0)} compared to monthly!
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Unlimited AI generations</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Advanced study tools</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Priority support</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Export & sharing features</span>
              </div>
              <div className="flex items-center gap-3">
                <Zap className="w-5 h-5 text-purple-500" />
                <span className="text-gray-700 font-medium">2 months FREE!</span>
              </div>
            </div>
            <Button 
              onClick={() => handleSelectPlan('yearly')}
              className="w-full mt-6 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              <Crown className="w-4 h-4 mr-2" />
              Choose Yearly Plan - Save {savingsPercentage}%
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="text-center mt-8">
        <p className="text-sm text-gray-500">
          All plans include a 7-day money-back guarantee. Cancel anytime.
        </p>
      </div>
    </div>
  );
};

export default CustomPricingTable;