import React, { useEffect } from 'react';

interface PricingPlansProps {
  onSelectPlan: (planId: string) => void;
  currentPlan?: string;
}

export const PricingPlans: React.FC<PricingPlansProps> = ({ onSelectPlan, currentPlan }) => {
  useEffect(() => {
    // Load Stripe pricing table script
    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/pricing-table.js';
    script.async = true;
    document.head.appendChild(script);

    return () => {
      // Cleanup script on unmount
      const existingScript = document.querySelector('script[src="https://js.stripe.com/v3/pricing-table.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  return (
    <div className="py-12 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Unlock the full potential of EZMind AI
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Choose the plan that best fits your learning needs
          </p>
        </div>

        <div className="flex justify-center">
          <stripe-pricing-table 
            pricing-table-id="prctbl_1RZRc2BV6Oovfe41KA8gsV6G"
            publishable-key="pk_live_51R1kMLBV6Oovfe415qZa7OJam5CoiIQCgCZGfRtJ9h8yRf4siDUeqcF0WNH08zEey0NDVV2bE55qwr2qlHXh8EnN00o30VJrYc"
          ></stripe-pricing-table>
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4">
            All plans include a 7-day free trial. Cancel anytime.
          </p>
          <div className="flex items-center justify-center gap-8 text-sm text-gray-500">
            <span>✓ Instant activation</span>
            <span>✓ Cancel anytime</span>
            <span>✓ 30-day money-back guarantee</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPlans;
