import React, { useEffect, useRef, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface StripePricingTableProps {
  className?: string;
}

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'stripe-pricing-table': {
        'pricing-table-id': string;
        'publishable-key': string;
        'customer-email'?: string;
        'client-reference-id'?: string;
      };
    }
  }
}

export const StripePricingTable: React.FC<StripePricingTableProps> = ({ className }) => {
  const scriptLoaded = useRef(false);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    // Check if script is already loaded
    const existingScript = document.querySelector('script[src="https://js.stripe.com/v3/pricing-table.js"]');

    if (existingScript) {
      setIsLoading(false);
      return;
    }

    // Load Stripe pricing table script if not already loaded
    if (!scriptLoaded.current) {
      const script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/pricing-table.js';
      script.async = true;

      script.onload = () => {
        setIsLoading(false);
        console.log('✅ Stripe pricing table script loaded');
      };

      script.onerror = () => {
        setIsLoading(false);
        console.error('❌ Failed to load Stripe pricing table script');
      };

      document.head.appendChild(script);
      scriptLoaded.current = true;
    }
  }, []);

  if (isLoading) {
    return (
      <div className={`${className} flex items-center justify-center py-16`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading pricing options...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <stripe-pricing-table
        pricing-table-id="prctbl_1RZRc2BV6Oovfe41KA8gsV6G"
        publishable-key="pk_live_51R1kMLBV6Oovfe415qZa7OJam5CoiIQCgCZGfRtJ9h8yRf4siDUeqcF0WNH08zEey0NDVV2bE55qwr2qlHXh8EnN00o30VJrYc"
        customer-email={user?.email || ''}
        client-reference-id={user?.email || ''}
      />
    </div>
  );
};

export default StripePricingTable;
