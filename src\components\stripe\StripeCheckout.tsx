import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Crown, CreditCard, Lock, CheckCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

import { toast } from 'sonner';

// Initialize Stripe
const stripePromise = loadStripe('pk_live_51R1kMLBV6Oovfe415qZa7OJam5CoiIQCgCZGfRtJ9h8yRf4siDUeqcF0WNH08zEey0NDVV2bE55qwr2qlHXh8EnN00o30VJrYc');

interface CheckoutFormProps {
  planType: 'monthly' | 'yearly';
  onSuccess: () => void;
  onCancel: () => void;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({ planType, onSuccess, onCancel }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentSucceeded, setPaymentSucceeded] = useState(false);

  const planDetails = {
    monthly: { price: 10, interval: 'month', description: 'Monthly Plan' },
    yearly: { price: 100, interval: 'year', description: 'Yearly Plan (Save $20)' }
  };

  const plan = planDetails[planType];

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !user?.email) {
      toast.error('Please sign in to continue');
      return;
    }

    setIsProcessing(true);

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      toast.error('Card element not found');
      setIsProcessing(false);
      return;
    }

    try {
      // Create payment method
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          email: user.email,
          name: user.displayName || user.email,
        },
      });

      if (error) {
        console.error('Payment method creation failed:', error);
        toast.error(error.message || 'Payment failed');
        setIsProcessing(false);
        return;
      }

      // Simulate successful payment (in real app, you'd confirm payment with backend)
      console.log('🎉 Payment method created successfully:', paymentMethod.id);
      
      // Simulate payment confirmation
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Activate subscription
      const sessionId = `pm_${paymentMethod.id}_${Date.now()}`;
      
      
      console.log('✅ Subscription activated:', subscription);
      
      setPaymentSucceeded(true);
      toast.success('🎉 Payment successful! Welcome to EZMind AI Pro!');
      
      // Trigger storage events
      window.dispatchEvent(new StorageEvent('storage', {
        key: `subscription-${user.email}`,
        newValue: JSON.stringify(subscription)
      }));
      
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'user-subscription',
        newValue: JSON.stringify(subscription)
      }));

      // Call success callback
      setTimeout(() => {
        onSuccess();
      }, 2000);

    } catch (error) {
      console.error('Payment processing error:', error);
      toast.error('Payment processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  if (paymentSucceeded) {
    return (
      <div className="text-center py-8">
        <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h3>
        <p className="text-gray-600 mb-4">Your EZMind AI Pro subscription is now active.</p>
        <Badge className="bg-green-100 text-green-700">
          Premium Features Unlocked
        </Badge>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Plan Summary */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Crown className="w-6 h-6 text-purple-600" />
            <div>
              <h3 className="font-semibold text-gray-900">EZMind AI Pro</h3>
              <p className="text-sm text-gray-600">{plan.description}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">${plan.price}</div>
            <div className="text-sm text-gray-600">per {plan.interval}</div>
          </div>
        </div>
      </div>

      {/* Card Input */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Payment Information
        </label>
        <div className="border border-gray-300 rounded-lg p-4 bg-white">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
                invalid: {
                  color: '#9e2146',
                },
              },
            }}
          />
        </div>
      </div>

      {/* Security Notice */}
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Lock className="w-4 h-4" />
        <span>Your payment information is secure and encrypted</span>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          type="button"
          onClick={onCancel}
          variant="outline"
          className="flex-1"
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!stripe || isProcessing}
          className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
        >
          {isProcessing ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Processing...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              Pay ${plan.price}
            </div>
          )}
        </Button>
      </div>

      {/* Test Card Info */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
        <p className="text-sm text-yellow-800 font-medium mb-1">Test Mode</p>
        <p className="text-xs text-yellow-700">
          Use test card: 4242 4242 4242 4242, any future date, any CVC
        </p>
      </div>
    </form>
  );
};

interface StripeCheckoutProps {
  planType: 'monthly' | 'yearly';
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const StripeCheckout: React.FC<StripeCheckoutProps> = ({
  planType,
  isOpen,
  onClose,
  onSuccess
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">Complete Your Purchase</CardTitle>
        </CardHeader>
        <CardContent>
          <Elements stripe={stripePromise}>
            <CheckoutForm
              planType={planType}
              onSuccess={() => {
                onSuccess();
                onClose();
              }}
              onCancel={onClose}
            />
          </Elements>
        </CardContent>
      </Card>
    </div>
  );
};

export default StripeCheckout;
