import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CreditCard, MessageSquare, FileText, BookOpen, Loader2 } from 'lucide-react';

interface ContentGenerationConfigProps {
  onGenerate: (type: string, options: any) => Promise<void>;
  isLoading: boolean;
}

export const ContentGenerationConfig = ({ onGenerate, isLoading }: ContentGenerationConfigProps) => {
  const [flashcardCount, setFlashcardCount] = useState('10');
  const [flashcardDifficulty, setFlashcardDifficulty] = useState('intermediate');
  const [quizCount, setQuizCount] = useState('5');
  const [quizDifficulty, setQuizDifficulty] = useState('medium');
  const [quizType, setQuizType] = useState('multiple-choice');
  const [customFlashcardCount, setCustomFlashcardCount] = useState('');
  const [customQuizCount, setCustomQuizCount] = useState('');

  const handleFlashcardGeneration = async () => {
    const count = flashcardCount === 'custom' ? parseInt(customFlashcardCount) : parseInt(flashcardCount);
    if (flashcardCount === 'custom' && (!customFlashcardCount || count < 1 || count > 100)) {
      alert('Please enter a valid number of flashcards (1-100)');
      return;
    }
    
    await onGenerate('flashcards', { 
      count, 
      difficulty: flashcardDifficulty 
    });
  };

  const handleQuizGeneration = async () => {
    const count = quizCount === 'custom' ? parseInt(customQuizCount) : parseInt(quizCount);
    if (quizCount === 'custom' && (!customQuizCount || count < 1 || count > 50)) {
      alert('Please enter a valid number of questions (1-50)');
      return;
    }
    
    await onGenerate('quiz', { 
      count, 
      difficulty: quizDifficulty,
      type: quizType
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Notes Generation */}
      <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50 hover:shadow-lg transition-all duration-300">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <FileText className="w-5 h-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">Study Notes</CardTitle>
              <CardDescription className="text-sm">Comprehensive notes</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <Button
            onClick={() => onGenerate('notes', {})}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white"
          >
            {isLoading ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <FileText className="w-4 h-4 mr-2" />}
            Generate Notes
          </Button>
        </CardContent>
      </Card>

      {/* Summary Generation */}
      <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50 hover:shadow-lg transition-all duration-300">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">Summary</CardTitle>
              <CardDescription className="text-sm">Key points overview</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <Button
            onClick={() => onGenerate('summary', {})}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white"
          >
            {isLoading ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <BookOpen className="w-4 h-4 mr-2" />}
            Generate Summary
          </Button>
        </CardContent>
      </Card>

      {/* Flashcards Generation */}
      <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50 hover:shadow-lg transition-all duration-300">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <CreditCard className="w-5 h-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">Flashcards</CardTitle>
              <CardDescription className="text-sm">Interactive study cards</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          <div>
            <Label className="text-xs font-medium text-gray-700">Quantity</Label>
            <Select value={flashcardCount} onValueChange={setFlashcardCount}>
              <SelectTrigger className="h-8 text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 cards</SelectItem>
                <SelectItem value="10">10 cards</SelectItem>
                <SelectItem value="20">20 cards</SelectItem>
                <SelectItem value="30">30 cards</SelectItem>
                <SelectItem value="50">50 cards</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {flashcardCount === 'custom' && (
            <div>
              <Label className="text-xs font-medium text-gray-700">Custom Count</Label>
              <Input
                type="number"
                min="1"
                max="100"
                value={customFlashcardCount}
                onChange={(e) => setCustomFlashcardCount(e.target.value)}
                placeholder="Enter number"
                className="h-8 text-sm"
              />
            </div>
          )}

          <div>
            <Label className="text-xs font-medium text-gray-700">Difficulty</Label>
            <Select value={flashcardDifficulty} onValueChange={setFlashcardDifficulty}>
              <SelectTrigger className="h-8 text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={handleFlashcardGeneration}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white h-8 text-sm"
          >
            {isLoading ? <Loader2 className="w-3 h-3 animate-spin mr-1" /> : <CreditCard className="w-3 h-3 mr-1" />}
            Generate
          </Button>
        </CardContent>
      </Card>

      {/* Quiz Generation */}
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-red-50 hover:shadow-lg transition-all duration-300">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">Quiz</CardTitle>
              <CardDescription className="text-sm">Practice questions</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          <div>
            <Label className="text-xs font-medium text-gray-700">Questions</Label>
            <Select value={quizCount} onValueChange={setQuizCount}>
              <SelectTrigger className="h-8 text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 questions</SelectItem>
                <SelectItem value="10">10 questions</SelectItem>
                <SelectItem value="15">15 questions</SelectItem>
                <SelectItem value="25">25 questions</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {quizCount === 'custom' && (
            <div>
              <Label className="text-xs font-medium text-gray-700">Custom Count</Label>
              <Input
                type="number"
                min="1"
                max="50"
                value={customQuizCount}
                onChange={(e) => setCustomQuizCount(e.target.value)}
                placeholder="Enter number"
                className="h-8 text-sm"
              />
            </div>
          )}

          <div>
            <Label className="text-xs font-medium text-gray-700">Difficulty</Label>
            <Select value={quizDifficulty} onValueChange={setQuizDifficulty}>
              <SelectTrigger className="h-8 text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="easy">Easy</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="hard">Hard</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs font-medium text-gray-700">Type</Label>
            <Select value={quizType} onValueChange={setQuizType}>
              <SelectTrigger className="h-8 text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="multiple-choice">Multiple Choice</SelectItem>
                <SelectItem value="true-false">True/False</SelectItem>
                <SelectItem value="short-answer">Short Answer</SelectItem>
                <SelectItem value="mixed">Mixed Types</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={handleQuizGeneration}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white h-8 text-sm"
          >
            {isLoading ? <Loader2 className="w-3 h-3 animate-spin mr-1" /> : <MessageSquare className="w-3 h-3 mr-1" />}
            Generate
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
