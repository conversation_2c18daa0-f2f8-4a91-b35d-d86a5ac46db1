import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, RotateCcw, Eye, EyeOff, CheckCircle, XCircle } from 'lucide-react';

interface Flashcard {
  id: string;
  front: string;
  back: string;
}

interface InteractiveFlashcardsProps {
  content: string;
  className?: string;
}

const InteractiveFlashcards: React.FC<InteractiveFlashcardsProps> = ({ content, className = '' }) => {
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [studiedCards, setStudiedCards] = useState<Set<string>>(new Set());
  const [correctCards, setCorrectCards] = useState<Set<string>>(new Set());
  const [incorrectCards, setIncorrectCards] = useState<Set<string>>(new Set());

  useEffect(() => {
    parseFlashcards(content);
  }, [content]);

  const parseFlashcards = (text: string) => {
    const cards: Flashcard[] = [];
    
    // Try different parsing patterns
    const patterns = [
      // Pattern 1: **Q:** ... **A:** ...
      /\*\*Q:\*\*\s*(.*?)\s*\*\*A:\*\*\s*(.*?)(?=\*\*Q:|$)/gs,
      // Pattern 2: **Question:** ... **Answer:** ...
      /\*\*Question:\*\*\s*(.*?)\s*\*\*Answer:\*\*\s*(.*?)(?=\*\*Question:|$)/gs,
      // Pattern 3: Q: ... A: ...
      /Q:\s*(.*?)\s*A:\s*(.*?)(?=Q:|$)/gs,
      // Pattern 4: Front: ... Back: ...
      /Front:\s*(.*?)\s*Back:\s*(.*?)(?=Front:|$)/gs,
      // Pattern 5: Number. Question ... Answer: ...
      /\d+\.\s*(.*?)\s*Answer:\s*(.*?)(?=\d+\.|$)/gs
    ];

    for (const pattern of patterns) {
      const matches = Array.from(text.matchAll(pattern));
      if (matches.length > 0) {
        matches.forEach((match, index) => {
          const front = match[1]?.trim().replace(/\*\*/g, '');
          const back = match[2]?.trim().replace(/\*\*/g, '');
          
          if (front && back && front.length > 0 && back.length > 0) {
            cards.push({
              id: `card-${index}`,
              front: front,
              back: back
            });
          }
        });
        break; // Use the first pattern that works
      }
    }

    // Fallback: Split by lines and try to pair them
    if (cards.length === 0) {
      const lines = text.split('\n').filter(line => line.trim().length > 0);
      for (let i = 0; i < lines.length - 1; i += 2) {
        const front = lines[i]?.trim().replace(/^\d+\.?\s*/, '').replace(/\*\*/g, '');
        const back = lines[i + 1]?.trim().replace(/\*\*/g, '');
        
        if (front && back && front.length > 10 && back.length > 10) {
          cards.push({
            id: `card-${i / 2}`,
            front: front,
            back: back
          });
        }
      }
    }

    setFlashcards(cards);
    setCurrentIndex(0);
    setIsFlipped(false);
  };

  const nextCard = () => {
    if (currentIndex < flashcards.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setIsFlipped(false);
    }
  };

  const prevCard = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setIsFlipped(false);
    }
  };

  const flipCard = () => {
    setIsFlipped(!isFlipped);
    if (!isFlipped) {
      setStudiedCards(prev => new Set([...prev, flashcards[currentIndex]?.id]));
    }
  };

  const markCorrect = () => {
    const cardId = flashcards[currentIndex]?.id;
    setCorrectCards(prev => new Set([...prev, cardId]));
    setIncorrectCards(prev => {
      const newSet = new Set(prev);
      newSet.delete(cardId);
      return newSet;
    });
    nextCard();
  };

  const markIncorrect = () => {
    const cardId = flashcards[currentIndex]?.id;
    setIncorrectCards(prev => new Set([...prev, cardId]));
    setCorrectCards(prev => {
      const newSet = new Set(prev);
      newSet.delete(cardId);
      return newSet;
    });
    nextCard();
  };

  const resetProgress = () => {
    setStudiedCards(new Set());
    setCorrectCards(new Set());
    setIncorrectCards(new Set());
    setCurrentIndex(0);
    setIsFlipped(false);
  };

  if (flashcards.length === 0) {
    return (
      <Card className={`p-6 text-center ${className}`}>
        <CardContent>
          <p className="text-gray-500">No flashcards could be parsed from the content. Please ensure the content follows a Q&A format.</p>
        </CardContent>
      </Card>
    );
  }

  const currentCard = flashcards[currentIndex];
  const progress = ((studiedCards.size / flashcards.length) * 100).toFixed(0);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            Card {currentIndex + 1} of {flashcards.length}
          </span>
          <div className="flex gap-2">
            <Badge variant="outline" className="text-green-600 border-green-200">
              <CheckCircle className="w-3 h-3 mr-1" />
              {correctCards.size}
            </Badge>
            <Badge variant="outline" className="text-red-600 border-red-200">
              <XCircle className="w-3 h-3 mr-1" />
              {incorrectCards.size}
            </Badge>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <p className="text-xs text-gray-500 text-center">{progress}% studied</p>
      </div>

      {/* Flashcard */}
      <div className="relative">
        <Card 
          className={`min-h-[300px] cursor-pointer transition-all duration-500 transform hover:scale-105 ${
            isFlipped ? 'bg-gradient-to-br from-purple-50 to-indigo-50' : 'bg-gradient-to-br from-blue-50 to-cyan-50'
          }`}
          onClick={flipCard}
        >
          <CardContent className="p-8 flex items-center justify-center text-center h-full min-h-[300px]">
            <div className="space-y-4">
              <div className="flex justify-center">
                <Badge variant="outline" className={isFlipped ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}>
                  {isFlipped ? 'Answer' : 'Question'}
                </Badge>
              </div>
              <p className="text-lg leading-relaxed max-w-2xl">
                {isFlipped ? currentCard.back : currentCard.front}
              </p>
              <div className="flex justify-center pt-4">
                {isFlipped ? (
                  <EyeOff className="w-5 h-5 text-gray-400" />
                ) : (
                  <Eye className="w-5 h-5 text-gray-400" />
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex justify-between items-center">
        <Button
          onClick={prevCard}
          disabled={currentIndex === 0}
          variant="outline"
          className="flex items-center gap-2"
        >
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>

        <div className="flex gap-2">
          <Button onClick={flipCard} variant="outline" className="flex items-center gap-2">
            <RotateCcw className="w-4 h-4" />
            Flip
          </Button>
          
          {isFlipped && (
            <>
              <Button onClick={markIncorrect} variant="outline" className="text-red-600 border-red-200 hover:bg-red-50">
                <XCircle className="w-4 h-4 mr-1" />
                Incorrect
              </Button>
              <Button onClick={markCorrect} variant="outline" className="text-green-600 border-green-200 hover:bg-green-50">
                <CheckCircle className="w-4 h-4 mr-1" />
                Correct
              </Button>
            </>
          )}
        </div>

        <Button
          onClick={nextCard}
          disabled={currentIndex === flashcards.length - 1}
          variant="outline"
          className="flex items-center gap-2"
        >
          Next
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      {/* Reset Button */}
      <div className="text-center">
        <Button onClick={resetProgress} variant="ghost" size="sm" className="text-gray-500">
          <RotateCcw className="w-4 h-4 mr-2" />
          Reset Progress
        </Button>
      </div>
    </div>
  );
};

export default InteractiveFlashcards;
