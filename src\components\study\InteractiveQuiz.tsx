import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check<PERSON>ircle, XCircle, Clock, Trophy, RotateCcw } from 'lucide-react';

interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation?: string;
}

interface QuizResult {
  questionId: string;
  selectedAnswer: number;
  isCorrect: boolean;
  timeSpent: number;
}

interface InteractiveQuizProps {
  content: string;
  className?: string;
}

const InteractiveQuiz: React.FC<InteractiveQuizProps> = ({ content, className = '' }) => {
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [results, setResults] = useState<QuizResult[]>([]);
  const [startTime, setStartTime] = useState<number>(Date.now());
  const [quizCompleted, setQuizCompleted] = useState(false);

  useEffect(() => {
    parseQuizQuestions(content);
  }, [content]);

  const parseQuizQuestions = (text: string) => {
    const parsedQuestions: QuizQuestion[] = [];
    
    // Pattern to match quiz questions with multiple choice answers
    const questionPattern = /(?:Question\s*\d*[:.]\s*|\d+\.\s*)(.*?)\n\s*(?:A[).]?\s*|a[).]?\s*)(.*?)\n\s*(?:B[).]?\s*|b[).]?\s*)(.*?)\n\s*(?:C[).]?\s*|c[).]?\s*)(.*?)\n\s*(?:D[).]?\s*|d[).]?\s*)(.*?)(?:\n\s*(?:Answer|Correct)[:\s]*([A-Da-d])|(?=\n\s*(?:Question|\d+\.)|$))/gs;
    
    const matches = Array.from(text.matchAll(questionPattern));
    
    matches.forEach((match, index) => {
      const question = match[1]?.trim();
      const optionA = match[2]?.trim();
      const optionB = match[3]?.trim();
      const optionC = match[4]?.trim();
      const optionD = match[5]?.trim();
      const correctAnswerLetter = match[6]?.trim().toUpperCase();
      
      if (question && optionA && optionB && optionC && optionD) {
        let correctAnswer = 0; // Default to A
        
        if (correctAnswerLetter) {
          switch (correctAnswerLetter) {
            case 'A': correctAnswer = 0; break;
            case 'B': correctAnswer = 1; break;
            case 'C': correctAnswer = 2; break;
            case 'D': correctAnswer = 3; break;
          }
        }
        
        parsedQuestions.push({
          id: `question-${index}`,
          question: question,
          options: [optionA, optionB, optionC, optionD],
          correctAnswer: correctAnswer
        });
      }
    });

    // Fallback parsing for simpler formats
    if (parsedQuestions.length === 0) {
      const lines = text.split('\n').filter(line => line.trim().length > 0);
      let currentQuestion: Partial<QuizQuestion> = {};
      let optionCount = 0;
      
      lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        
        // Check if it's a question
        if (trimmedLine.match(/^\d+\./) || trimmedLine.toLowerCase().includes('question')) {
          if (currentQuestion.question && currentQuestion.options && currentQuestion.options.length >= 2) {
            parsedQuestions.push({
              id: `question-${parsedQuestions.length}`,
              question: currentQuestion.question,
              options: currentQuestion.options,
              correctAnswer: 0 // Default to first option
            });
          }
          
          currentQuestion = {
            question: trimmedLine.replace(/^\d+\.\s*/, '').replace(/question\s*:?\s*/i, ''),
            options: []
          };
          optionCount = 0;
        }
        // Check if it's an option
        else if (trimmedLine.match(/^[A-Da-d][).]/) || (currentQuestion.question && optionCount < 4)) {
          if (!currentQuestion.options) currentQuestion.options = [];
          currentQuestion.options.push(trimmedLine.replace(/^[A-Da-d][).]\s*/, ''));
          optionCount++;
        }
      });
      
      // Add the last question
      if (currentQuestion.question && currentQuestion.options && currentQuestion.options.length >= 2) {
        parsedQuestions.push({
          id: `question-${parsedQuestions.length}`,
          question: currentQuestion.question,
          options: currentQuestion.options,
          correctAnswer: 0
        });
      }
    }

    setQuestions(parsedQuestions);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShowResult(false);
    setResults([]);
    setQuizCompleted(false);
    setStartTime(Date.now());
  };

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
  };

  const handleSubmitAnswer = () => {
    if (selectedAnswer === null) return;
    
    const currentQuestion = questions[currentQuestionIndex];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    const timeSpent = Date.now() - startTime;
    
    const result: QuizResult = {
      questionId: currentQuestion.id,
      selectedAnswer,
      isCorrect,
      timeSpent
    };
    
    setResults(prev => [...prev, result]);
    setShowResult(true);
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
      setShowResult(false);
      setStartTime(Date.now());
    } else {
      setQuizCompleted(true);
    }
  };

  const resetQuiz = () => {
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShowResult(false);
    setResults([]);
    setQuizCompleted(false);
    setStartTime(Date.now());
  };

  const getScorePercentage = () => {
    const correctAnswers = results.filter(r => r.isCorrect).length;
    return Math.round((correctAnswers / results.length) * 100);
  };

  if (questions.length === 0) {
    return (
      <Card className={`p-6 text-center ${className}`}>
        <CardContent>
          <p className="text-gray-500">No quiz questions could be parsed from the content. Please ensure the content includes multiple choice questions with options A, B, C, D.</p>
        </CardContent>
      </Card>
    );
  }

  if (quizCompleted) {
    const score = getScorePercentage();
    const correctCount = results.filter(r => r.isCorrect).length;
    
    return (
      <Card className={`${className}`}>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className={`p-4 rounded-full ${score >= 80 ? 'bg-green-100' : score >= 60 ? 'bg-yellow-100' : 'bg-red-100'}`}>
              <Trophy className={`w-8 h-8 ${score >= 80 ? 'text-green-600' : score >= 60 ? 'text-yellow-600' : 'text-red-600'}`} />
            </div>
          </div>
          <CardTitle className="text-2xl">Quiz Completed!</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <div className="text-4xl font-bold mb-2">{score}%</div>
            <p className="text-gray-600">You got {correctCount} out of {results.length} questions correct</p>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{correctCount}</div>
              <div className="text-sm text-green-700">Correct</div>
            </div>
            <div className="p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{results.length - correctCount}</div>
              <div className="text-sm text-red-700">Incorrect</div>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{results.length}</div>
              <div className="text-sm text-blue-700">Total</div>
            </div>
          </div>

          <Button onClick={resetQuiz} className="w-full">
            <RotateCcw className="w-4 h-4 mr-2" />
            Retake Quiz
          </Button>
        </CardContent>
      </Card>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            Question {currentQuestionIndex + 1} of {questions.length}
          </span>
          <Badge variant="outline">
            <Clock className="w-3 h-3 mr-1" />
            Quiz in Progress
          </Badge>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Question */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{currentQuestion.question}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {currentQuestion.options.map((option, index) => (
            <button
              key={index}
              onClick={() => handleAnswerSelect(index)}
              disabled={showResult}
              className={`w-full p-4 text-left rounded-lg border-2 transition-all duration-200 ${
                selectedAnswer === index
                  ? showResult
                    ? index === currentQuestion.correctAnswer
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : 'border-red-500 bg-red-50 text-red-700'
                    : 'border-blue-500 bg-blue-50'
                  : showResult && index === currentQuestion.correctAnswer
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center">
                <span className="w-6 h-6 rounded-full border-2 border-current flex items-center justify-center text-sm font-medium mr-3">
                  {String.fromCharCode(65 + index)}
                </span>
                <span>{option}</span>
                {showResult && selectedAnswer === index && (
                  <span className="ml-auto">
                    {index === currentQuestion.correctAnswer ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                  </span>
                )}
                {showResult && selectedAnswer !== index && index === currentQuestion.correctAnswer && (
                  <CheckCircle className="w-5 h-5 text-green-600 ml-auto" />
                )}
              </div>
            </button>
          ))}
        </CardContent>
      </Card>

      {/* Controls */}
      <div className="flex justify-between">
        <div></div>
        {!showResult ? (
          <Button 
            onClick={handleSubmitAnswer} 
            disabled={selectedAnswer === null}
            className="px-8"
          >
            Submit Answer
          </Button>
        ) : (
          <Button onClick={handleNextQuestion} className="px-8">
            {currentQuestionIndex < questions.length - 1 ? 'Next Question' : 'Finish Quiz'}
          </Button>
        )}
      </div>
    </div>
  );
};

export default InteractiveQuiz;
