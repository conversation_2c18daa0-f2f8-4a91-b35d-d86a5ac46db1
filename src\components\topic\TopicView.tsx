
import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';

import { ArrowLeft, FileText, BookOpen, Brain, Zap, Loader2, Upload, Download, Settings, Edit3, Check, X } from 'lucide-react';
import { generateNotes, generateSummary, generateQuiz, generateFlashcards } from '@/services/geminiService';
import { RichContentDisplay } from '@/components/ui/RichContentDisplay';
import { ContentGenerationConfig } from '@/components/study/ContentGenerationConfig';
import { toast } from 'sonner';
import { trackAIGeneration, trackAIGenerationComplete } from '@/utils/analytics';

import { NativeFilePreview } from '@/components/preview/NativeFilePreview';
import InteractiveFlashcards from '@/components/study/InteractiveFlashcards';
import InteractiveQuiz from '@/components/study/InteractiveQuiz';

interface TopicViewProps {
  topicId: string;
  onBack: () => void;
}

interface StudyTopic {
  id: string;
  title: string;
  original_content: string;
  created_at: string;
  fileType?: string;
  fileName?: string;
  fileUrl?: string;
  metadata?: any;
}

interface StudyContent {
  id: string;
  content_type: 'notes' | 'summary' | 'quiz' | 'flashcards';
  content: any;
  created_at: string;
}

const TopicView = ({ topicId, onBack }: TopicViewProps) => {
  const [topic, setTopic] = useState<StudyTopic | null>(null);
  const [content, setContent] = useState<StudyContent[]>([]);
  const [activeTab, setActiveTab] = useState('original');
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState<string | null>(null);

  const [uploadingFile, setUploadingFile] = useState(false);
  const [showNativePreview, setShowNativePreview] = useState(false);
  const [showGenerationOptions, setShowGenerationOptions] = useState<string | null>(null);
  const [generationOptions, setGenerationOptions] = useState({
    flashcards: { count: 10 },
    quiz: { count: 5, difficulty: 'medium' },
    notes: { difficulty: 'intermediate' },
    summary: {}
  });

  // Title editing state
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editedTitle, setEditedTitle] = useState('');
  const [savingTitle, setSavingTitle] = useState(false);

  useEffect(() => {
    fetchTopicData();
  }, [topicId]);

  // Title editing functions
  const startEditingTitle = () => {
    if (topic) {
      setEditedTitle(topic.title);
      setIsEditingTitle(true);
    }
  };

  const cancelEditingTitle = () => {
    setIsEditingTitle(false);
    setEditedTitle('');
  };

  const saveTitle = async () => {
    if (!topic || !editedTitle.trim()) {
      toast.error('Title cannot be empty');
      return;
    }

    if (editedTitle.trim() === topic.title) {
      // No changes made
      setIsEditingTitle(false);
      return;
    }

    setSavingTitle(true);
    try {
      const userEmail = localStorage.getItem('user-email') || 'anonymous';

      // Update the topic in localStorage
      const updatedTopic = {
        ...topic,
        title: editedTitle.trim()
      };

      // Save to localStorage with user-specific key
      localStorage.setItem(`topic-${userEmail}-${topicId}`, JSON.stringify({
        id: topicId,
        title: editedTitle.trim(),
        content: topic.original_content,
        createdAt: topic.created_at,
        fileType: topic.fileType,
        fileName: topic.fileName,
        fileUrl: topic.fileUrl,
        metadata: topic.metadata
      }));

      // Update local state
      setTopic(updatedTopic);
      setIsEditingTitle(false);

      toast.success('Title updated successfully!');
      console.log('✅ Topic title updated:', editedTitle.trim());
    } catch (error) {
      console.error('❌ Error updating title:', error);
      toast.error('Failed to update title');
    } finally {
      setSavingTitle(false);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveTitle();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEditingTitle();
    }
  };

  const handleTitleBlur = () => {
    // Auto-save on blur if there are changes
    if (editedTitle.trim() && editedTitle.trim() !== topic?.title) {
      saveTitle();
    } else {
      cancelEditingTitle();
    }
  };

  const fetchTopicData = async () => {
    try {
      // Get user email from localStorage
      const userEmail = localStorage.getItem('user-email') || 'anonymous';

      // Load topic from localStorage with user-specific key
      const topicData = localStorage.getItem(`topic-${userEmail}-${topicId}`);
      if (topicData) {
        const parsedTopic = JSON.parse(topicData);
        setTopic({
          id: parsedTopic.id,
          title: parsedTopic.title,
          original_content: parsedTopic.content,
          created_at: parsedTopic.createdAt,
          fileType: parsedTopic.fileType,
          fileName: parsedTopic.fileName,
          fileUrl: parsedTopic.fileUrl,
          metadata: parsedTopic.metadata
        });
      } else {
        throw new Error('Topic not found');
      }

      // Load generated content from localStorage with user-specific key
      const contentData = localStorage.getItem(`topic-content-${userEmail}-${topicId}`);
      if (contentData) {
        setContent(JSON.parse(contentData));
      }
    } catch (error) {
      console.error('Error fetching topic:', error);
      toast.error('Failed to load topic');
    } finally {
      setLoading(false);
    }
  };

  const exportToPDF = async (type: string, content: string) => {
    try {
      // Use browser's print functionality to generate PDF
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${topic?.title} - ${type.charAt(0).toUpperCase() + type.slice(1)}</title>
          <style>
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 40px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 40px;
              border-bottom: 2px solid #4F46E5;
              padding-bottom: 20px;
            }
            .header h1 {
              color: #333;
              margin: 0 0 10px 0;
              font-size: 28px;
            }
            .header p {
              margin: 5px 0;
              color: #666;
            }
            .content {
              max-width: 800px;
              margin: 0 auto;
              font-size: 14px;
            }
            .content h1 { font-size: 24px; margin: 30px 0 15px 0; color: #4F46E5; }
            .content h2 { font-size: 20px; margin: 25px 0 12px 0; color: #4F46E5; }
            .content h3 { font-size: 16px; margin: 20px 0 10px 0; color: #666; }
            .content p { margin: 10px 0; }
            .content ul, .content ol { margin: 10px 0; padding-left: 20px; }
            .content li { margin: 5px 0; }
            .content strong { font-weight: 600; }
            .footer {
              margin-top: 40px;
              text-align: center;
              color: #666;
              font-size: 12px;
              border-top: 1px solid #eee;
              padding-top: 20px;
            }
            .print-btn {
              position: fixed;
              top: 20px;
              right: 20px;
              background: #4F46E5;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 5px;
              cursor: pointer;
              font-size: 14px;
            }
            .print-btn:hover {
              background: #3730A3;
            }
          </style>
        </head>
        <body>
          <button class="print-btn no-print" onclick="window.print()">Print as PDF</button>
          <div class="header">
            <h1>${topic?.title}</h1>
            <p><strong>${type.charAt(0).toUpperCase() + type.slice(1)}</strong> - Generated by EZMind AI</p>
            <p>Created on ${new Date().toLocaleDateString()}</p>
          </div>
          <div class="content">
            ${content
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
              .replace(/\*(.*?)\*/g, '<em>$1</em>')
              .replace(/^### (.*$)/gm, '<h3>$1</h3>')
              .replace(/^## (.*$)/gm, '<h2>$1</h2>')
              .replace(/^# (.*$)/gm, '<h1>$1</h1>')
              .replace(/^- (.*$)/gm, '<li>$1</li>')
              .replace(/^\* (.*$)/gm, '<li>$1</li>')
              .replace(/^\d+\. (.*$)/gm, '<li>$1</li>')
              .replace(/\n\n/g, '</p><p>')
              .replace(/\n/g, '<br>')
              .replace(/^(.*)/, '<p>$1')
              .replace(/(.*$)/, '$1</p>')
              .replace(/<p><li>/g, '<ul><li>')
              .replace(/<\/li><\/p>/g, '</li></ul>')
              .replace(/<p><\/p>/g, '')
            }
          </div>
          <div class="footer">
            <p>Generated by EZMind AI - Your AI-Powered Study Assistant</p>
          </div>
          <script>
            // Auto-focus print dialog after page loads
            window.onload = function() {
              setTimeout(() => {
                window.print();
              }, 500);
            };
          </script>
        </body>
        </html>
      `;

      printWindow.document.write(htmlContent);
      printWindow.document.close();

      toast.success(`${type.charAt(0).toUpperCase() + type.slice(1)} opened for PDF export! Use Ctrl+P or the Print button to save as PDF.`);
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export content. Please try again.');
    }
  };

  const generateContent = async (type: 'notes' | 'summary' | 'quiz' | 'flashcards', options: any = {}) => {
    if (!topic) return;

    setGenerating(type);

    // Track AI generation start
    trackAIGeneration(type, topic.id);

    try {
      console.log(`🤖 Generating ${type} for topic:`, {
        topicId: topic.id,
        fileType: topic.fileType,
        contentLength: topic.original_content.length,
        isWebsite: topic.fileType === 'text/website'
      });

      let result = "";
      const currentOptions = { ...generationOptions[type], ...options };

      switch (type) {
        case 'notes':
          result = await generateNotes(topic.original_content, currentOptions.difficulty || 'intermediate');
          break;
        case 'summary':
          result = await generateSummary(topic.original_content);
          break;
        case 'quiz':
          result = await generateQuiz(
            topic.original_content,
            currentOptions.difficulty || 'medium',
            currentOptions.count || 5
          );
          break;
        case 'flashcards':
          result = await generateFlashcards(
            topic.original_content,
            currentOptions.count || 10
          );
          break;
      }

      console.log(`✅ Generated ${type} successfully, result length:`, result.length);

      // Save to localStorage
      const newContent = {
        id: Date.now().toString(),
        content_type: type,
        content: { text: result },
        created_at: new Date().toISOString(),
        options: currentOptions // Store the generation options
      };

      const userEmail = localStorage.getItem('user-email') || 'anonymous';
      const existingContent = localStorage.getItem(`topic-content-${userEmail}-${topicId}`);
      const contentArray = existingContent ? JSON.parse(existingContent) : [];

      // Remove existing content of same type
      const filteredContent = contentArray.filter((c: any) => c.content_type !== type);
      filteredContent.unshift(newContent);

      localStorage.setItem(`topic-content-${userEmail}-${topicId}`, JSON.stringify(filteredContent));

      await fetchTopicData();
      setActiveTab(type);

      // Track successful generation
      const itemCount = currentOptions.count || (type === 'notes' || type === 'summary' ? 1 : 10);
      trackAIGenerationComplete(type, itemCount);

      const countText = currentOptions.count ? ` (${currentOptions.count} ${type === 'quiz' ? 'questions' : 'cards'})` : '';
      toast.success(`${type.charAt(0).toUpperCase() + type.slice(1)}${countText} generated successfully!`);
    } catch (error) {
      console.error(`❌ Error generating ${type}:`, error);

      // Provide specific error messages based on content type
      let errorMessage = `Failed to generate ${type}. Please try again.`;

      if (topic.fileType === 'text/website') {
        errorMessage = `Failed to generate ${type} from website content. The website content may need more processing time. Please try again.`;
      }

      if (error.message?.includes('content')) {
        errorMessage = `The content appears to be too complex for ${type} generation. Try with a shorter or simpler content.`;
      }

      toast.error(errorMessage);
    } finally {
      setGenerating(null);
    }
  };





  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  };

  // Helper function to convert file to base64 (same as LibraryDashboard)
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data:application/pdf;base64, prefix
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  };

  // Process PDF directly with Gemini 2.5 Flash (same as LibraryDashboard)
  const processPDFWithGemini = async (base64Data: string): Promise<string> => {
    try {
      console.log('🚀 Sending PDF to Gemini 2.5 Flash for direct processing...');

      // Create AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, 60000); // 60 second timeout

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-001:generateContent?key=AIzaSyBd5ImRFOeTFhAQUgBVjhsTkFHsHmelbmI`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
        body: JSON.stringify({
          contents: [{
            parts: [
              {
                text: `Extract and format the text content from this PDF document. Preserve structure, headings, and key information. Be concise but comprehensive.`
              },
              {
                inline_data: {
                  mime_type: "application/pdf",
                  data: base64Data
                }
              }
            ]
          }],
          generationConfig: {
            temperature: 0.1,
            topK: 16,
            topP: 0.8,
            maxOutputTokens: 4096,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_NONE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_NONE"
            }
          ]
        })
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Gemini API error:', errorText);
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      if (data.candidates && data.candidates.length > 0) {
        const content = data.candidates[0].content.parts[0].text.trim();

        if (content.length > 0) {
          console.log('✅ PDF processed successfully with Gemini 2.5 Flash');
          return content;
        }
      }

      throw new Error('No content extracted from PDF by Gemini');

    } catch (error) {
      if (error.name === 'AbortError') {
        console.error('❌ PDF processing timed out');
        throw new Error('PDF processing timed out. Please try with a smaller file.');
      }
      console.error('❌ Error processing PDF with Gemini:', error);
      throw new Error(`Failed to process PDF with Gemini: ${error.message}`);
    }
  };

  // Format markdown content to HTML
  const formatMarkdownContent = (text: string): string => {
    if (!text) return '';

    return text
      // Convert bold **text** to <strong>text</strong>
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Convert italic *text* to <em>text</em>
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Convert headers ### to <h3>
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mt-6 mb-3">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
      // Convert bullet points
      .replace(/^- (.*$)/gm, '<li class="ml-4 mb-1">• $1</li>')
      .replace(/^\* (.*$)/gm, '<li class="ml-4 mb-1">• $1</li>')
      // Convert numbered lists
      .replace(/^\d+\. (.*$)/gm, '<li class="ml-4 mb-1 list-decimal">$1</li>')
      // Convert line breaks to <br> but preserve paragraphs
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/\n/g, '<br>')
      // Wrap in paragraph tags
      .replace(/^(.*)/, '<p class="mb-4">$1')
      .replace(/(.*$)/, '$1</p>')
      // Clean up empty paragraphs
      .replace(/<p class="mb-4"><\/p>/g, '')
      // Convert code blocks `code` to <code>
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>');
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploadingFile(true);
    try {
      let allFileContent = '';

      console.log('🔧 Processing', files.length, 'file(s)');

      // Process each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log('🔧 Processing file:', file.name, file.type, file.size);

        try {
          let fileContent = '';

          // Use the same file processing logic as LibraryDashboard
          if (file.type.includes('text') || file.name.endsWith('.txt')) {
            fileContent = await file.text();
          } else if (file.type.includes('pdf') || file.name.endsWith('.pdf')) {
            // Use Gemini AI for PDF processing (same as LibraryDashboard)
            console.log('📄 Processing PDF file with Gemini AI:', file.name, file.size);
            toast.info('📄 Processing PDF with AI...');

            try {
              // Convert file to base64 for Gemini API
              const base64Data = await fileToBase64(file);

              // Process PDF directly with Gemini
              fileContent = await processPDFWithGemini(base64Data);

              console.log('📄 PDF processed successfully with Gemini AI, length:', fileContent.length);
            } catch (pdfError) {
              console.warn('📄 Gemini PDF processing failed, trying fallback:', pdfError);
              toast.warning('AI PDF processing failed, trying alternative method...');

              // Fallback to document service
              try {
                const { processDocument } = await import('@/services/documentService');
                const result = await processDocument(file);
                fileContent = result.content;
                console.log('📄 PDF fallback processing completed with document service');
              } catch (fallbackError) {
                console.warn('📄 Document service also failed, using simple processor:', fallbackError);
                const { processFileSimple } = await import('@/services/simpleFileProcessor');
                const fallbackResult = await processFileSimple(file);
                fileContent = fallbackResult.content;
                console.log('📄 PDF final fallback processing completed');
              }
            }
          } else if (file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
            // Use document service for Word processing
            const { processDocument } = await import('@/services/documentService');
            const result = await processDocument(file);
            fileContent = result.content;
          } else if (file.name.endsWith('.ppt') || file.name.endsWith('.pptx')) {
            // Use document service for PowerPoint processing
            console.log('📊 Processing PowerPoint file:', file.name, file.size);
            toast.info('📊 Processing PowerPoint with AI...');

            const { processDocument } = await import('@/services/documentService');
            const result = await processDocument(file);
            fileContent = result.content;
          } else if (file.type.includes('audio') || file.name.match(/\.(mp3|wav|m4a|aac|ogg|flac)$/i)) {
            // Use audio service for audio processing
            const { processAudioFile } = await import('@/services/audioService');
            const result = await processAudioFile(file, file.name);
            fileContent = result.transcript;
          } else if (file.type.includes('video') || file.name.match(/\.(mp4|mov|avi|mkv|webm)$/i)) {
            // Use simple file processor for video files
            const { processFileSimple } = await import('@/services/simpleFileProcessor');
            const result = await processFileSimple(file);
            fileContent = result.content;
          } else if (file.type.includes('image') || file.name.match(/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i)) {
            // Use simple file processor for images
            const { processFileSimple } = await import('@/services/simpleFileProcessor');
            const result = await processFileSimple(file);
            fileContent = result.content;
          } else {
            // Fallback to simple file processor
            const { processFileSimple } = await import('@/services/simpleFileProcessor');
            const result = await processFileSimple(file);
            fileContent = result.content;
          }

          if (fileContent) {
            allFileContent += `\n\n--- Uploaded File: ${file.name} ---\n${fileContent}`;
          }
        } catch (fileError) {
          console.error(`Error processing file ${file.name}:`, fileError);
          toast.error(`Failed to process ${file.name}`);
          continue;
        }
      }

      if (topic && allFileContent) {
        const updatedContent = topic.original_content + allFileContent;

        // Update localStorage instead of Supabase
        const userEmail = localStorage.getItem('user-email') || 'anonymous';
        localStorage.setItem(`topic-${userEmail}-${topicId}`, JSON.stringify({
          id: topicId,
          title: topic.title,
          content: updatedContent,
          createdAt: topic.created_at
        }));

        await fetchTopicData();
        const fileText = files.length === 1 ? 'File' : `${files.length} files`;
        toast.success(`${fileText} uploaded and processed successfully!`);
      }

      // Clear the input
      event.target.value = '';
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Failed to upload files. Please try again.');
    } finally {
      setUploadingFile(false);
    }
  };

  const handleAdditionalFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploadingFile(true);
    try {
      let additionalContent = '';

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log('Processing file:', file.name, file.type);

        try {
          if (file.type.includes('text') || file.name.endsWith('.txt')) {
            const textContent = await file.text();
            additionalContent += `\n\n--- Additional File: ${file.name} ---\n${textContent}`;
          } else if (file.type.includes('pdf') || file.name.endsWith('.pdf')) {
            // Use Gemini AI for PDF processing (same as LibraryDashboard)
            console.log('📄 Processing additional PDF file with Gemini AI:', file.name, file.size);
            toast.info(`📄 Processing PDF with AI: ${file.name}...`);

            try {
              // Convert file to base64 for Gemini API
              const base64Data = await fileToBase64(file);

              // Process PDF directly with Gemini
              const pdfContent = await processPDFWithGemini(base64Data);

              additionalContent += `\n\n--- Additional PDF: ${file.name} ---\n${pdfContent}`;
              console.log('📄 Additional PDF processed successfully with Gemini AI');
            } catch (pdfError) {
              console.warn('📄 Additional Gemini PDF processing failed, trying fallback:', pdfError);
              toast.warning(`AI PDF processing failed for ${file.name}, trying alternative method...`);

              // Fallback to document service
              try {
                const { processDocument } = await import('@/services/documentService');
                const result = await processDocument(file);
                additionalContent += `\n\n--- Additional PDF: ${file.name} ---\n${result.content}`;
                console.log('📄 Additional PDF fallback processing completed with document service');
              } catch (fallbackError) {
                console.warn('📄 Additional document service also failed, using simple processor:', fallbackError);
                const { processFileSimple } = await import('@/services/simpleFileProcessor');
                const fallbackResult = await processFileSimple(file);
                additionalContent += `\n\n--- Additional PDF: ${file.name} ---\n${fallbackResult.content}`;
                console.log('📄 Additional PDF final fallback processing completed');
              }
            }
          } else if (file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
            // Use document service for Word processing
            const { processDocument } = await import('@/services/documentService');
            const result = await processDocument(file);
            additionalContent += `\n\n--- Additional Document: ${file.name} ---\n${result.content}`;
          } else if (file.name.endsWith('.ppt') || file.name.endsWith('.pptx')) {
            // Use document service for PowerPoint processing
            console.log('📊 Processing additional PowerPoint file:', file.name, file.size);
            toast.info(`📊 Processing ${file.name} with AI...`);

            const { processDocument } = await import('@/services/documentService');
            const result = await processDocument(file);
            additionalContent += `\n\n--- Additional Presentation: ${file.name} ---\n${result.content}`;
          } else if (file.type.includes('audio')) {
            // Use audio service for audio processing
            const { processAudioFile } = await import('@/services/audioService');
            const result = await processAudioFile(file, file.name);
            additionalContent += `\n\n--- Additional Audio: ${file.name} ---\n${result.transcript}`;
          } else {
            toast.warning(`Skipping unsupported file: ${file.name}`);
            continue;
          }
        } catch (fileError) {
          console.error(`Error processing file ${file.name}:`, fileError);
          toast.error(`Failed to process ${file.name}`);
          continue;
        }
      }

      if (topic && additionalContent) {
        const updatedContent = topic.original_content + additionalContent;

        // Update localStorage with user-specific key
        const userEmail = localStorage.getItem('user-email') || 'anonymous';
        localStorage.setItem(`topic-${userEmail}-${topicId}`, JSON.stringify({
          id: topicId,
          title: topic.title,
          content: updatedContent,
          createdAt: topic.created_at
        }));

        await fetchTopicData();
        toast.success(`Successfully added ${files.length} file(s) to topic!`);
      }

      // Clear the input
      event.target.value = '';
    } catch (error) {
      console.error('Error uploading additional files:', error);
      toast.error('Failed to upload additional files');
    } finally {
      setUploadingFile(false);
    }
  };

  const getContentByType = (type: string) => {
    return content.find(c => c.content_type === type);
  };

  // Determine file type for native preview
  const getFileTypeForPreview = (fileType?: string, fileName?: string): 'pdf' | 'word' | 'powerpoint' | 'text' | 'image' | 'audio' | 'video' | 'youtube' | 'website' => {
    if (!fileType && !fileName) return 'text';

    if (fileType?.includes('pdf') || fileName?.endsWith('.pdf')) return 'pdf';
    if (fileType?.includes('word') || fileName?.endsWith('.docx') || fileName?.endsWith('.doc')) return 'word';
    if (fileType?.includes('presentation') || fileName?.endsWith('.pptx') || fileName?.endsWith('.ppt')) return 'powerpoint';
    if (fileType?.includes('image') || fileName?.match(/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i)) return 'image';
    if (fileType?.includes('audio') || fileName?.match(/\.(mp3|wav|m4a|aac|ogg|flac)$/i)) return 'audio';
    if (fileType?.includes('video') || fileName?.match(/\.(mp4|mov|avi|mkv|webm)$/i)) return 'video';
    if (fileType === 'video/youtube' || fileName?.includes('YouTube Video')) return 'youtube';
    if (fileType === 'text/website' || fileName?.includes('Website')) return 'website';

    return 'text';
  };

  // Check if topic has file that can be previewed natively
  const canShowNativePreview = (topic: StudyTopic) => {
    const fileType = getFileTypeForPreview(topic.fileType, topic.fileName);
    return fileType !== 'text' || topic.fileType === 'text/website' || topic.fileType === 'video/youtube';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!topic) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <p>Topic not found</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-6 py-4 sticky top-0 z-50 shadow-lg shadow-black/5">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={onBack} className="text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Library
            </Button>
            <div className="text-gray-400">&gt;</div>

            {/* Editable Title */}
            <div className="flex items-center gap-2">
              {isEditingTitle ? (
                <div className="flex items-center gap-2">
                  <Input
                    value={editedTitle}
                    onChange={(e) => setEditedTitle(e.target.value)}
                    onKeyDown={handleTitleKeyDown}
                    onBlur={handleTitleBlur}
                    className="text-xl font-semibold text-gray-900 border-gray-300 focus:border-blue-500 focus:ring-blue-500 min-w-[300px]"
                    placeholder="Enter topic title..."
                    autoFocus
                    disabled={savingTitle}
                  />
                  <Button
                    onClick={saveTitle}
                    size="sm"
                    variant="ghost"
                    disabled={savingTitle || !editedTitle.trim()}
                    className="text-green-600 hover:text-green-700 hover:bg-green-50"
                  >
                    {savingTitle ? <Loader2 className="w-4 h-4 animate-spin" /> : <Check className="w-4 h-4" />}
                  </Button>
                  <Button
                    onClick={cancelEditingTitle}
                    size="sm"
                    variant="ghost"
                    disabled={savingTitle}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <div
                  className="flex items-center gap-2 group cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors"
                  onClick={startEditingTitle}
                  title="Click to edit title"
                >
                  <h1 className="text-xl font-semibold text-gray-900">{topic.title}</h1>
                  <Edit3 className="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-3">
            <input
              type="file"
              accept=".pdf,.txt,.doc,.docx,.ppt,.pptx,.mp3,.wav,.m4a,.aac,.ogg,.flac,.mp4,.mov,.avi,.mkv,.webm,.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
              multiple
            />
            <label htmlFor="file-upload">
              <Button as="span" variant="outline" disabled={uploadingFile} className="cursor-pointer">
                {uploadingFile ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Upload File
                  </>
                )}
              </Button>
            </label>
          </div>
        </div>
      </header>

      <div className="w-full">
        <div className="p-6">
          {/* AI Generation Controls */}
          <Card className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5 text-blue-600" />
                AI Study Material Generator
              </CardTitle>
              <CardDescription>
                Generate comprehensive study materials from your content using AI
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Notes Generation */}
                <div className="space-y-2">
                  <Button
                    onClick={() => generateContent('notes')}
                    disabled={generating === 'notes'}
                    className="w-full h-auto p-4 flex flex-col items-center gap-2 bg-blue-600 hover:bg-blue-700"
                  >
                    {generating === 'notes' ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <FileText className="w-5 h-5" />
                    )}
                    <span className="font-medium">Generate Notes</span>
                    <span className="text-xs opacity-90">Comprehensive study notes</span>
                  </Button>
                  {getContentByType('notes') && (
                    <Button
                      onClick={() => exportToPDF('notes', getContentByType('notes')?.content.text || '')}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export PDF
                    </Button>
                  )}
                </div>

                {/* Summary Generation */}
                <div className="space-y-2">
                  <Button
                    onClick={() => generateContent('summary')}
                    disabled={generating === 'summary'}
                    className="w-full h-auto p-4 flex flex-col items-center gap-2 bg-green-600 hover:bg-green-700"
                  >
                    {generating === 'summary' ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <BookOpen className="w-5 h-5" />
                    )}
                    <span className="font-medium">Generate Summary</span>
                    <span className="text-xs opacity-90">Key points & insights</span>
                  </Button>
                  {getContentByType('summary') && (
                    <Button
                      onClick={() => exportToPDF('summary', getContentByType('summary')?.content.text || '')}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export PDF
                    </Button>
                  )}
                </div>

                {/* Flashcards Generation */}
                <div className="space-y-2">
                  <div className="flex gap-1">
                    <Button
                      onClick={() => generateContent('flashcards')}
                      disabled={generating === 'flashcards'}
                      className="flex-1 h-auto p-4 flex flex-col items-center gap-2 bg-purple-600 hover:bg-purple-700"
                    >
                      {generating === 'flashcards' ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <Zap className="w-5 h-5" />
                      )}
                      <span className="font-medium">Flashcards</span>
                      <span className="text-xs opacity-90">{generationOptions.flashcards.count} cards</span>
                    </Button>
                    <div className="flex flex-col gap-1">
                      <Button
                        onClick={() => setGenerationOptions(prev => ({ ...prev, flashcards: { count: 10 } }))}
                        variant={generationOptions.flashcards.count === 10 ? "default" : "outline"}
                        size="sm"
                        className="text-xs px-2 py-1 h-6"
                      >
                        10
                      </Button>
                      <Button
                        onClick={() => setGenerationOptions(prev => ({ ...prev, flashcards: { count: 20 } }))}
                        variant={generationOptions.flashcards.count === 20 ? "default" : "outline"}
                        size="sm"
                        className="text-xs px-2 py-1 h-6"
                      >
                        20
                      </Button>
                      <Button
                        onClick={() => setGenerationOptions(prev => ({ ...prev, flashcards: { count: 50 } }))}
                        variant={generationOptions.flashcards.count === 50 ? "default" : "outline"}
                        size="sm"
                        className="text-xs px-2 py-1 h-6"
                      >
                        50
                      </Button>
                    </div>
                  </div>
                  {getContentByType('flashcards') && (
                    <Button
                      onClick={() => exportToPDF('flashcards', getContentByType('flashcards')?.content.text || '')}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export PDF
                    </Button>
                  )}
                </div>

                {/* Quiz Generation */}
                <div className="space-y-2">
                  <div className="flex gap-1">
                    <Button
                      onClick={() => generateContent('quiz')}
                      disabled={generating === 'quiz'}
                      className="flex-1 h-auto p-4 flex flex-col items-center gap-2 bg-orange-600 hover:bg-orange-700"
                    >
                      {generating === 'quiz' ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <Brain className="w-5 h-5" />
                      )}
                      <span className="font-medium">Quiz</span>
                      <span className="text-xs opacity-90">{generationOptions.quiz.count} questions</span>
                    </Button>
                    <div className="flex flex-col gap-1">
                      <Button
                        onClick={() => setGenerationOptions(prev => ({ ...prev, quiz: { ...prev.quiz, count: 5 } }))}
                        variant={generationOptions.quiz.count === 5 ? "default" : "outline"}
                        size="sm"
                        className="text-xs px-2 py-1 h-6"
                      >
                        5
                      </Button>
                      <Button
                        onClick={() => setGenerationOptions(prev => ({ ...prev, quiz: { ...prev.quiz, count: 10 } }))}
                        variant={generationOptions.quiz.count === 10 ? "default" : "outline"}
                        size="sm"
                        className="text-xs px-2 py-1 h-6"
                      >
                        10
                      </Button>
                      <Button
                        onClick={() => setGenerationOptions(prev => ({ ...prev, quiz: { ...prev.quiz, count: 15 } }))}
                        variant={generationOptions.quiz.count === 15 ? "default" : "outline"}
                        size="sm"
                        className="text-xs px-2 py-1 h-6"
                      >
                        15
                      </Button>
                    </div>
                  </div>
                  {getContentByType('quiz') && (
                    <Button
                      onClick={() => exportToPDF('quiz', getContentByType('quiz')?.content.text || '')}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export PDF
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid grid-cols-5 w-full bg-white border border-gray-200 p-1">
              <TabsTrigger value="original" className="data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700">
                Original Content
              </TabsTrigger>
              <TabsTrigger value="notes" className="data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700">
                AI Notes
              </TabsTrigger>
              <TabsTrigger value="summary" className="data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700">
                AI Summary
              </TabsTrigger>
              <TabsTrigger value="flashcards" className="data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700">
                AI Flashcards
              </TabsTrigger>
              <TabsTrigger value="quiz" className="data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700">
                AI Quizzes
              </TabsTrigger>
            </TabsList>

            <TabsContent value="original" className="mt-0">
              <div className="space-y-6">


                {/* Original Content Display */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Original Content
                    </CardTitle>
                      <div className="flex gap-2">
                        <input
                          type="file"
                          accept=".pdf,.doc,.docx,.ppt,.pptx,.txt,.mp3,.wav,.m4a,.mp4,.avi,.mov"
                          onChange={handleAdditionalFileUpload}
                          className="hidden"
                          id="additional-file-upload"
                          multiple
                        />
                        <label htmlFor="additional-file-upload">
                          <Button variant="outline" size="sm" disabled={uploadingFile}>
                            <Upload className="w-4 h-4 mr-2" />
                            {uploadingFile ? 'Uploading...' : 'Add More Files'}
                          </Button>
                        </label>
                      </div>
                    </CardHeader>
                  <CardContent>
                    {canShowNativePreview(topic) ? (
                      <div className="space-y-4">
                        {/* Native File Preview */}
                        <div className="border rounded-lg overflow-hidden">
                          <NativeFilePreview
                            fileName={topic.fileName || topic.title}
                            content={topic.original_content}
                            fileType={getFileTypeForPreview(topic.fileType, topic.fileName)}
                            fileUrl={topic.fileUrl}
                            metadata={topic.metadata}
                            onClose={() => {}} // No close button needed here
                            onCreateTopic={() => {}} // No create topic needed here
                            embedded={true} // Enable embedded mode
                          />
                        </div>

                        {/* Text Content as Fallback */}
                        <details className="mt-4">
                          <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800 font-medium">
                            View Extracted Text Content
                          </summary>
                          <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                            <RichContentDisplay content={topic.original_content} />
                          </div>
                        </details>
                      </div>
                    ) : (
                      <RichContentDisplay content={topic.original_content} />
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

              <TabsContent value="notes" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      AI Generated Notes
                    </CardTitle>
                    <CardDescription>
                      {getContentByType('notes') ? 'Generated study notes' : 'AI-generated notes will appear here'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {getContentByType('notes') ? (
                      <div className="bg-white border rounded-lg p-6">
                        <div className="prose max-w-none">
                          <div
                            className="text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{
                              __html: formatMarkdownContent(getContentByType('notes')?.content.text || '')
                            }}
                          />
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-500">No notes generated yet. Click the button above to generate AI notes.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="summary" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="w-5 h-5" />
                      AI Summary
                    </CardTitle>
                    <CardDescription>
                      {getContentByType('summary') ? 'Generated content summary' : 'AI-generated summary will appear here'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {getContentByType('summary') ? (
                      <div className="bg-white border rounded-lg p-6">
                        <div className="prose max-w-none">
                          <div
                            className="text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{
                              __html: formatMarkdownContent(getContentByType('summary')?.content.text || '')
                            }}
                          />
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-500">No summary generated yet. Click the button above to generate AI summary.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="flashcards" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="w-5 h-5" />
                      AI Flashcards
                    </CardTitle>
                    <CardDescription>
                      {getContentByType('flashcards') ? 'Interactive study flashcards' : 'AI-generated flashcards will appear here'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {getContentByType('flashcards') ? (
                      <InteractiveFlashcards
                        content={getContentByType('flashcards')?.content.text || ''}
                        className="mt-4"
                      />
                    ) : (
                      <p className="text-gray-500">No flashcards generated yet. Click the button above to generate AI flashcards.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="quiz" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="w-5 h-5" />
                      AI Quizzes
                    </CardTitle>
                    <CardDescription>
                      {getContentByType('quiz') ? 'Interactive practice quiz' : 'AI-generated quizzes will appear here'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {getContentByType('quiz') ? (
                      <InteractiveQuiz
                        content={getContentByType('quiz')?.content.text || ''}
                        className="mt-4"
                      />
                    ) : (
                      <p className="text-gray-500">No quiz generated yet. Click the button above to generate AI quiz.</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
          </Tabs>
        </div>
      </div>



      {showNativePreview && topic && (
        <NativeFilePreview
          fileName={topic.fileName || topic.title}
          content={topic.original_content}
          fileType={getFileTypeForPreview(topic.fileType, topic.fileName)}
          fileUrl={topic.fileUrl}
          metadata={topic.metadata}
          onClose={() => setShowNativePreview(false)}
          onCreateTopic={() => {
            setShowNativePreview(false);
            // Could trigger AI generation here if needed
          }}
        />
      )}
    </div>
  );
};

export default TopicView;
