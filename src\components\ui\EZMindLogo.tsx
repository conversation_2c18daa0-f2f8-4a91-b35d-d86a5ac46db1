import React from 'react';

interface EZMindLogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

export const EZMindLogo: React.FC<EZMindLogoProps> = ({ 
  className = '', 
  size = 'md',
  showText = true 
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-3xl'
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* EZMind Icon - Brain/Lightning Symbol */}
      <div className={`${sizeClasses[size]} bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center shadow-lg`}>
        <svg 
          viewBox="0 0 24 24" 
          fill="none" 
          className="w-2/3 h-2/3 text-white"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Brain/Lightning hybrid icon */}
          <path 
            d="M12 2L8 8h3v6l4-6h-3V2z" 
            fill="currentColor"
            opacity="0.8"
          />
          <path 
            d="M6 10c0-1.1.9-2 2-2h1l1-2H8c-2.2 0-4 1.8-4 4v4c0 2.2 1.8 4 4 4h8c2.2 0 4-1.8 4-4v-4c0-1.1-.9-2-2-2h-2l1 2h1v4H8v-4z" 
            fill="currentColor"
            opacity="0.6"
          />
        </svg>
      </div>
      
      {/* EZMind Text */}
      {showText && (
        <span className={`font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent ${textSizeClasses[size]}`}>
          EZMind
        </span>
      )}
    </div>
  );
};

export default EZMindLogo;
