import { Loader2, <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingIndicatorProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'ai' | 'processing';
  message?: string;
}

export const LoadingIndicator = ({ 
  className, 
  size = 'md', 
  variant = 'default',
  message 
}: LoadingIndicatorProps) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const getIcon = () => {
    switch (variant) {
      case 'ai':
        return <Brain className={cn(sizeClasses[size], 'animate-pulse')} />;
      case 'processing':
        return <Sparkles className={cn(sizeClasses[size], 'animate-spin')} />;
      default:
        return <Loader2 className={cn(sizeClasses[size], 'animate-spin')} />;
    }
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {getIcon()}
      {message && (
        <span className="text-sm text-gray-600 animate-pulse">
          {message}
        </span>
      )}
    </div>
  );
};

interface FullPageLoadingProps {
  message?: string;
  variant?: 'default' | 'ai' | 'processing';
}

export const FullPageLoading = ({ 
  message = 'Loading...', 
  variant = 'default' 
}: FullPageLoadingProps) => {
  return (
    <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center space-y-4">
        <LoadingIndicator 
          size="lg" 
          variant={variant}
          className="justify-center"
        />
        <p className="text-gray-600 font-medium">{message}</p>
      </div>
    </div>
  );
};

interface ProcessingOverlayProps {
  isVisible: boolean;
  message: string;
  progress?: number;
  variant?: 'default' | 'ai' | 'processing';
}

export const ProcessingOverlay = ({ 
  isVisible, 
  message, 
  progress,
  variant = 'processing' 
}: ProcessingOverlayProps) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
        <div className="text-center space-y-6">
          <LoadingIndicator 
            size="lg" 
            variant={variant}
            className="justify-center"
          />
          
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">
              Processing...
            </h3>
            <p className="text-gray-600">{message}</p>
          </div>

          {progress !== undefined && (
            <div className="space-y-2">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <p className="text-sm text-gray-500">{progress}% complete</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
