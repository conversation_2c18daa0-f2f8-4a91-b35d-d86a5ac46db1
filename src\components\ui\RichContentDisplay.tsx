import React from 'react';

interface RichContentDisplayProps {
  content: string;
  className?: string;
}

export const RichContentDisplay: React.FC<RichContentDisplayProps> = ({
  content,
  className = ''
}) => {
  // Clean and format content for display
  const formatContent = (text: string) => {
    // Remove markdown artifacts and clean the text
    let cleanText = text
      // Remove markdown formatting
      .replace(/\*\*(.*?)\*\*/g, '$1')  // Remove bold **text**
      .replace(/\*(.*?)\*/g, '$1')      // Remove italic *text*
      .replace(/_(.*?)_/g, '$1')        // Remove underline _text_
      .replace(/`(.*?)`/g, '$1')        // Remove code `text`
      .replace(/~~(.*?)~~/g, '$1')      // Remove strikethrough ~~text~~

      // Clean up headers but preserve structure
      .replace(/^#{1,6}\s*/gm, '')      // Remove # symbols but keep header text

      // Clean up metadata and timestamps
      .replace(/\*\[.*?\]\*/g, '')      // Remove *[metadata]*
      .replace(/---.*?---/gs, '')       // Remove --- sections ---
      .replace(/═══.*?═══/g, '')        // Remove ═══ separators ═══

      // Clean up extra whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Reduce multiple line breaks
      .replace(/^\s+|\s+$/g, '')        // Trim whitespace
      .trim();

    const lines = cleanText.split('\n');
    const elements: React.ReactNode[] = [];
    let currentIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines but preserve some spacing
      if (line === '') {
        if (i > 0 && i < lines.length - 1) {
          elements.push(<div key={`space-${currentIndex++}`} className="h-4" />);
        }
        continue;
      }

      // Detect headers based on content patterns
      const isHeader = (
        line.length < 100 &&
        (line.toUpperCase() === line ||
         /^[A-Z][^.!?]*$/.test(line) ||
         line.includes('Page ') ||
         line.includes('Chapter ') ||
         line.includes('Section '))
      );

      if (isHeader) {
        elements.push(
          <h3 key={`header-${currentIndex++}`} className="text-lg font-semibold text-gray-900 mt-6 mb-3">
            {line}
          </h3>
        );
      }
      // Bullet points
      else if (line.startsWith('•') || line.startsWith('-') || line.startsWith('*')) {
        const bulletText = line.replace(/^[•\-*]\s*/, '');
        elements.push(
          <div key={`bullet-${currentIndex++}`} className="flex items-start mb-2 ml-4">
            <span className="text-blue-500 mr-3 mt-1 text-sm">•</span>
            <span className="text-gray-700 leading-relaxed">{bulletText}</span>
          </div>
        );
      }
      // Regular paragraphs
      else {
        elements.push(
          <p key={`p-${currentIndex++}`} className="text-gray-700 leading-relaxed mb-4 text-justify">
            {line}
          </p>
        );
      }
    }

    return elements;
  };

  return (
    <div className={`max-w-none ${className}`}>
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="space-y-1">
          {formatContent(content)}
        </div>
      </div>
    </div>
  );
};

export default RichContentDisplay;
