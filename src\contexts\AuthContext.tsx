
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  User,
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  getRedirectResult
} from 'firebase/auth';
import { auth, googleProvider } from '@/lib/firebase';
import { trackSignUp, trackSignIn, trackSignOut, trackProfileUpdated } from '@/utils/analytics';
import { initializeUserPaywallState, shouldShowWelcomePaywall } from '@/services/paywallService';
import { syncUserData } from '@/services/simplePaymentService';

interface UserProfile {
  displayName: string;
  email: string;
  createdAt: string;
  lastUpdated: string;
}

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  showNewUserPaywall: boolean;
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signUpWithEmail: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  getUserDisplayName: () => string;
  dismissNewUserPaywall: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [showNewUserPaywall, setShowNewUserPaywall] = useState(false);

  // Load user profile from localStorage
  const loadUserProfile = (email: string): UserProfile => {
    const stored = localStorage.getItem(`profile-${email}`);
    if (stored) {
      return JSON.parse(stored);
    }

    // Create default profile
    const defaultProfile: UserProfile = {
      displayName: email.split('@')[0], // Use email prefix as default display name
      email,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    localStorage.setItem(`profile-${email}`, JSON.stringify(defaultProfile));
    return defaultProfile;
  };

  // Save user profile to localStorage
  const saveUserProfile = (profile: UserProfile) => {
    localStorage.setItem(`profile-${profile.email}`, JSON.stringify(profile));
    setUserProfile(profile);
  };

  useEffect(() => {
    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      setLoading(false);
    }, 5000); // 5 second timeout

    // Set up Firebase auth state listener
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      clearTimeout(loadingTimeout); // Clear timeout since auth state changed
      setUser(user);

      // Store user email in localStorage for data isolation
      if (user?.email && user?.uid) {
        localStorage.setItem('user-email', user.email);
        // Load user profile
        const profile = loadUserProfile(user.email);
        setUserProfile(profile);

        // 🔥 NEW: Sync user data with database
        syncUserData(user.uid, user.email)
          .then(() => {
            console.log('✅ User data synced with database');
          })
          .catch((error: any) => {
            console.error('❌ Error syncing user with database:', error);
          });

        // Initialize paywall state and check if should show welcome paywall
        initializeUserPaywallState(user.email);
        // Show paywall for all users at startup to encourage upgrades
        const shouldShow = shouldShowWelcomePaywall(user.email) || true;
        setShowNewUserPaywall(shouldShow);
      } else {
        localStorage.removeItem('user-email');
        setUserProfile(null);
        setShowNewUserPaywall(false);
      }

      setLoading(false);
    }, (error) => {
      console.error('Firebase auth error:', error);
      clearTimeout(loadingTimeout);
      setLoading(false);
    });

    // Check for redirect result (for OAuth flows)
    getRedirectResult(auth).then((result) => {
      if (result?.user) {
        console.log('🔐 OAuth redirect result:', result.user.email);
        // User is already handled by onAuthStateChanged
      }
    }).catch((error) => {
      console.error('OAuth redirect error:', error);
      clearTimeout(loadingTimeout);
      setLoading(false);
    });

    return () => {
      unsubscribe();
      clearTimeout(loadingTimeout);
    };
  }, []);

  const signInWithGoogle = async () => {
    try {
      console.log('🔐 Starting Google sign in...');
      console.log('🔐 Firebase project:', import.meta.env.VITE_FIREBASE_PROJECT_ID);

      const result = await signInWithPopup(auth, googleProvider);
      console.log('🔐 Google sign in successful:', result.user.email);
      console.log('🔐 User details:', {
        uid: result.user.uid,
        email: result.user.email,
        displayName: result.user.displayName,
        photoURL: result.user.photoURL
      });

      // Track sign in
      trackSignIn('google');
    } catch (error: any) {
      console.error('🔐 Google sign in error:', error);
      console.error('🔐 Error code:', error.code);
      console.error('🔐 Error message:', error.message);

      // Handle specific OAuth errors with user-friendly messages
      if (error.code === 'auth/popup-closed-by-user') {
        throw new Error('Sign-in was cancelled. Please try again.');
      } else if (error.code === 'auth/popup-blocked') {
        throw new Error('Pop-up was blocked by your browser. Please allow pop-ups and try again.');
      } else if (error.code === 'auth/unauthorized-domain') {
        throw new Error('This domain is not authorized for OAuth operations.');
      } else if (error.code === 'auth/operation-not-allowed') {
        throw new Error('Google sign-in is not enabled. Please contact support.');
      } else if (error.code === 'auth/cancelled-popup-request') {
        throw new Error('Another sign-in attempt is in progress.');
      }

      throw error;
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    try {
      console.log('🔐 Starting email sign in for:', email);
      const result = await signInWithEmailAndPassword(auth, email, password);
      console.log('🔐 Email sign in successful:', result.user.email);

      // Track sign in
      trackSignIn('email');
    } catch (error: any) {
      console.error('🔐 Email sign in error:', error);
      throw error;
    }
  };

  const signUpWithEmail = async (email: string, password: string) => {
    try {
      console.log('🔐 Starting email sign up for:', email);
      const result = await createUserWithEmailAndPassword(auth, email, password);
      console.log('🔐 Email sign up successful:', result.user.email);

      // Track sign up
      trackSignUp('email');
    } catch (error: any) {
      console.error('🔐 Email sign up error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      console.log('🔐 Starting sign out...');
      await firebaseSignOut(auth);
      console.log('🔐 Sign out successful');

      // Track sign out
      trackSignOut();
    } catch (error: any) {
      console.error('🔐 Sign out error:', error);
      throw error;
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!userProfile) throw new Error('No user profile loaded');

    const updatedProfile: UserProfile = {
      ...userProfile,
      ...updates,
      lastUpdated: new Date().toISOString()
    };

    saveUserProfile(updatedProfile);

    // Track profile update
    trackProfileUpdated();
  };

  const getUserDisplayName = (): string => {
    if (userProfile?.displayName) {
      return userProfile.displayName;
    }
    if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'User';
  };

  const dismissNewUserPaywall = (): void => {
    setShowNewUserPaywall(false);
    if (user?.email) {
      // Mark as seen in paywall service
      const { markWelcomePaywallSeen } = require('@/services/paywallService');
      markWelcomePaywallSeen(user.email);
    }
  };

  const value = {
    user,
    userProfile,
    loading,
    showNewUserPaywall,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    updateProfile,
    getUserDisplayName,
    dismissNewUserPaywall,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
