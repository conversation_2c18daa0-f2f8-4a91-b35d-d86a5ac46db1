import { useState, useEffect, useCallback } from 'react';
import { 
  getUserSubscription, 
  getUserUsage, 
  canPerformAction, 
  hasFeatureAccess, 
  incrementUsage,
  UserSubscription,
  UserUsage
} from '@/services/subscriptionService';

interface UseSubscriptionReturn {
  subscription: UserSubscription;
  usage: UserUsage;
  canPerformAction: (action: keyof UserUsage, userEmail?: string) => boolean;
  hasFeatureAccess: (feature: string, userEmail?: string) => boolean;
  incrementUsage: (action: keyof UserUsage, amount?: number) => void;
  showPaywall: (feature: string, description: string, requiredPlan?: 'basic' | 'premium') => void;
  isPaywallOpen: boolean;
  paywallData: {
    feature: string;
    description: string;
    requiredPlan: 'basic' | 'premium';
  } | null;
  closePaywall: () => void;
  refreshSubscription: () => void;
}

export const useSubscription = (userEmail?: string): UseSubscriptionReturn => {
  const [subscription, setSubscription] = useState<UserSubscription>(getUserSubscription());
  const [usage, setUsage] = useState<UserUsage>(getUserUsage());
  const [isPaywallOpen, setIsPaywallOpen] = useState(false);
  const [paywallData, setPaywallData] = useState<{
    feature: string;
    description: string;
    requiredPlan: 'monthly' | 'yearly' | 'premium';
  } | null>(null);

  // Refresh subscription and usage data
  const refreshSubscription = useCallback(() => {
    setSubscription(getUserSubscription());
    setUsage(getUserUsage());
  }, []);

  // Check if user can perform an action
  const checkCanPerformAction = useCallback((action: keyof UserUsage, email?: string) => {
    return canPerformAction(action, email || userEmail);
  }, [userEmail]);

  // Check if user has feature access
  const checkHasFeatureAccess = useCallback((feature: string, email?: string) => {
    // Map feature names to subscription service keys
    const featureMap: Record<string, keyof any> = {
      'mathExpert': 'mathExpert',
      'liveRecording': 'liveRecording',
      'multipleFiles': 'multipleFiles',
      'imageAnalysis': 'imageAnalysis'
    };
    
    const mappedFeature = featureMap[feature] || feature;
    return hasFeatureAccess(mappedFeature as any, email || userEmail);
  }, [userEmail]);

  // Increment usage and refresh state
  const handleIncrementUsage = useCallback((action: keyof UserUsage, amount: number = 1) => {
    incrementUsage(action, amount);
    setUsage(getUserUsage());
  }, []);

  // Show paywall modal
  const showPaywall = useCallback((
    feature: string, 
    description: string, 
    requiredPlan: 'monthly' | 'yearly' | 'premium' = 'monthly'
  ) => {
    setPaywallData({ feature, description, requiredPlan });
    setIsPaywallOpen(true);
  }, []);

  // Close paywall modal
  const closePaywall = useCallback(() => {
    setIsPaywallOpen(false);
    setPaywallData(null);
  }, []);

  // Listen for subscription changes
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user-subscription' || e.key === 'user-usage') {
        refreshSubscription();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [refreshSubscription]);

  return {
    subscription,
    usage,
    canPerformAction: checkCanPerformAction,
    hasFeatureAccess: checkHasFeatureAccess,
    incrementUsage: handleIncrementUsage,
    showPaywall,
    isPaywallOpen,
    paywallData,
    closePaywall,
    refreshSubscription
  };
};

// Helper hook for specific features
export const useFeatureGate = (
  feature: string,
  description: string,
  requiredPlan: 'monthly' | 'yearly' | 'premium' = 'monthly',
  userEmail?: string
) => {
  const { hasFeatureAccess, showPaywall } = useSubscription(userEmail);

  const checkAccess = useCallback(() => {
    const hasAccess = hasFeatureAccess(feature, userEmail);
    if (!hasAccess) {
      showPaywall(feature, description, requiredPlan);
      return false;
    }
    return true;
  }, [feature, description, requiredPlan, userEmail, hasFeatureAccess, showPaywall]);

  return {
    hasAccess: hasFeatureAccess(feature, userEmail),
    checkAccess
  };
};

// Helper hook for usage-based features
export const useUsageGate = (
  action: keyof UserUsage,
  feature: string,
  description: string,
  requiredPlan: 'monthly' | 'yearly' | 'premium' = 'monthly',
  userEmail?: string
) => {
  const { canPerformAction, incrementUsage, showPaywall } = useSubscription(userEmail);

  const checkAndIncrement = useCallback((amount: number = 1) => {
    const canPerform = canPerformAction(action, userEmail);
    if (!canPerform) {
      showPaywall(feature, description, requiredPlan);
      return false;
    }
    incrementUsage(action, amount);
    return true;
  }, [action, feature, description, requiredPlan, userEmail, canPerformAction, incrementUsage, showPaywall]);

  return {
    canPerform: canPerformAction(action, userEmail),
    checkAndIncrement
  };
};

export default useSubscription;
