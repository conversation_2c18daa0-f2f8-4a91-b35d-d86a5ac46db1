
import { useState, useCallback } from 'react';
import { canPerformAction, incrementUsage } from '@/services/subscriptionService';
import { UserUsage } from '@/services/subscriptionService';

interface UseUsageGateReturn {
  checkAndIncrement: () => boolean;
  canUseFeature: boolean;
  showUpgrade: boolean;
}

export const useUsageGate = (
  action: keyof UserUsage,
  featureName: string,
  description: string,
  requiredPlan?: 'monthly' | 'yearly' | 'premium',
  userEmail?: string
): UseUsageGateReturn => {
  const [showUpgrade, setShowUpgrade] = useState(false);
  
  const canUseFeature = canPerformAction(action, userEmail);

  const checkAndIncrement = useCallback(() => {
    if (canUseFeature) {
      incrementUsage(action, 1);
      return true;
    } else {
      setShowUpgrade(true);
      return false;
    }
  }, [action, canUseFeature]);

  return {
    checkAndIncrement,
    canUseFeature,
    showUpgrade
  };
};

export default useUsageGate;
