// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
import { getStorage } from "firebase/storage";
import { getFirestore } from "firebase/firestore";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyAFxCeWDmHMJH5NZVVY141AwwK4LIUR7fE",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "studywise-hsli2.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "studywise-hsli2",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "studywise-hsli2.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "391336079060",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:391336079060:web:dd10e2378fda776ff81541"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const storage = getStorage(app);
export const db = getFirestore(app);

// Configure Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');

// Configure OAuth settings for better user experience
googleProvider.setCustomParameters({
  prompt: 'select_account', // Always show account selection
  hd: undefined // Allow any domain (remove if you want to restrict to specific domains)
});

export default app;
