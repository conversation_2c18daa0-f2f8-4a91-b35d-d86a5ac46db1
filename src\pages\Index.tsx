
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { LandingPage } from '@/components/landing/LandingPage';
import { LibraryDashboard } from '@/components/dashboard/LibraryDashboard';
import TopicView from '@/components/topic/TopicView';

const Index = () => {
  const { user, loading } = useAuth();
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(null);
  const [dashboardKey, setDashboardKey] = useState(0);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          <p className="text-gray-600">Loading EZMind AI...</p>
        </div>
      </div>
    );
  }

  const handleSelectTopic = (topicId: string) => {
    setSelectedTopicId(topicId);
  };

  const handleBackToDashboard = () => {
    console.log('🔄 Returning to dashboard, forcing refresh to sync title changes...');
    setSelectedTopicId(null);
    // Force LibraryDashboard to remount and reload topics
    setDashboardKey(prev => prev + 1);
  };

  // If user is not authenticated, show landing page
  if (!user) {
    return <LandingPage onGetStarted={() => {}} />;
  }

  // If user selected a topic, show topic view
  if (selectedTopicId) {
    return (
      <TopicView
        topicId={selectedTopicId}
        onBack={handleBackToDashboard}
      />
    );
  }

  // Show dashboard for authenticated users
  return (
    <LibraryDashboard
      key={dashboardKey}
      onSelectTopic={handleSelectTopic}
      userEmail={user.email}
      onSignOut={() => {}}
    />
  );
};

export default Index;
