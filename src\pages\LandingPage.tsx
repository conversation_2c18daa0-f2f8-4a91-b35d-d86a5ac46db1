
import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Brain, Upload, FileText, PenTool, HelpCircle, CreditCard, Sparkles, Play, Users, Star, ArrowRight, Mic, Video, Lightbulb, Zap, BookOpen, CheckCircle, Globe, TrendingUp } from 'lucide-react';
import AuthPage from '@/components/auth/AuthPage';

const LandingPage = () => {
  const [showAuth, setShowAuth] = useState(false);

  if (showAuth) {
    return <AuthPage />;
  }

  const features = [
    {
      icon: <Brain className="w-6 h-6" />,
      title: "AI-Powered Notes",
      description: "Transform lectures and readings into comprehensive, organized study notes instantly.",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Upload className="w-6 h-6" />,
      title: "Multi-Format Upload",
      description: "Support for documents, videos, audio recordings, and live lecture capture.",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Smart Summaries",
      description: "Get straight to the point with AI-generated summaries and key takeaways.",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: <PenTool className="w-6 h-6" />,
      title: "Detailed Analysis",
      description: "Deep dive into any topic with structured, comprehensive study materials.",
      gradient: "from-orange-500 to-red-500"
    },
    {
      icon: <HelpCircle className="w-6 h-6" />,
      title: "Interactive Quizzes",
      description: "Test your knowledge with auto-generated quizzes and instant feedback.",
      gradient: "from-indigo-500 to-purple-500"
    },
    {
      icon: <CreditCard className="w-6 h-6" />,
      title: "Smart Flashcards",
      description: "Memorize faster with AI-optimized flashcard generation and spaced repetition.",
      gradient: "from-teal-500 to-blue-500"
    }
  ];

  const benefits = [
    { icon: Zap, label: "Instant Processing", value: "AI-Powered" },
    { icon: BookOpen, label: "Multi-Format Support", value: "All Types" },
    { icon: Brain, label: "Smart Analysis", value: "Advanced AI" },
    { icon: CheckCircle, label: "Always Available", value: "24/7 Access" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 overflow-hidden">
      {/* Modern Navigation */}
      <nav className="relative bg-white/80 backdrop-blur-lg border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl blur opacity-75"></div>
                <div className="relative p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                  <Brain className="w-8 h-8 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  EZMind AI
                </h1>
                <p className="text-xs text-gray-500 font-medium">Smart Learning Platform</p>
              </div>
            </div>
            <Button 
              onClick={() => setShowAuth(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Get Started Free
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative pt-20 pb-32">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-6xl mx-auto px-6 text-center">
          <Badge className="mb-8 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-blue-200 px-4 py-2 rounded-full font-medium">
            <Sparkles className="w-4 h-4 mr-2" />
            AI-Powered Learning Revolution
          </Badge>

          <h1 className="text-7xl font-bold mb-8 leading-tight">
            <span className="bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
              The Best AI
            </span>
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Note-Taking Solution
            </span>
          </h1>

          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed font-medium">
            Transform any study material into organized notes, summaries, quizzes, and flashcards with cutting-edge AI.
            Designed for Founders, Developers, Management, and Students who demand excellence.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Button 
              onClick={() => setShowAuth(true)}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-4 text-lg rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
            >
              <Play className="w-5 h-5 mr-2" />
              Start Learning Now
            </Button>
            <Button 
              variant="outline" 
              size="lg" 
              className="border-2 border-gray-300 hover:border-blue-300 text-gray-700 hover:text-blue-700 font-semibold px-8 py-4 text-lg rounded-xl hover:bg-blue-50 transition-all duration-300"
            >
              <Video className="w-5 h-5 mr-2" />
              Watch Demo
            </Button>
          </div>

          {/* Benefits Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center group">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  <benefit.icon className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-lg font-bold text-gray-900 mb-1">{benefit.value}</div>
                <div className="text-sm text-gray-600 font-medium">{benefit.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="relative py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-20">
            <Badge className="mb-6 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-blue-200 px-4 py-2 rounded-full font-medium">
              <Zap className="w-4 h-4 mr-2" />
              Powerful Features
            </Badge>
            <h2 className="text-5xl font-bold text-gray-900 mb-6">
              Professional-Grade AI Tools
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From processing complex documents to generating comprehensive study materials, EZMind AI delivers enterprise-quality results for serious learners
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="group border-0 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-gradient-to-br from-white to-gray-50 overflow-hidden">
                <CardHeader className="pb-4">
                  <div className={`inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r ${feature.gradient} rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <div className="text-white">
                      {feature.icon}
                    </div>
                  </div>
                  <CardTitle className="text-xl text-gray-900 group-hover:text-blue-700 transition-colors duration-300">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* AI Tutor Highlight */}
      <div className="relative py-24 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-6xl mx-auto px-6">
          <Card className="border-0 shadow-2xl bg-gradient-to-r from-white to-blue-50 overflow-hidden">
            <div className="flex flex-col lg:flex-row items-center p-12 gap-12">
              <div className="flex-1">
                <Badge className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full font-medium">
                  <Brain className="w-4 h-4 mr-2" />
                  AI Tutor
                </Badge>
                <h3 className="text-4xl font-bold text-gray-900 mb-6">
                  Your Personal AI Learning Assistant
                </h3>
                <p className="text-lg text-gray-600 leading-relaxed mb-8">
                  Get instant help with any topic, search the web for relevant resources, and receive personalized guidance 
                  tailored to your learning style. Available 24/7 to support your academic journey.
                </p>
                <div className="flex flex-wrap gap-3">
                  {['Instant Answers', 'Web Search', 'Personalized Help', '24/7 Available'].map((item, index) => (
                    <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-700 px-3 py-1 font-medium">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      {item}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-600 rounded-2xl blur opacity-25"></div>
                <div className="relative p-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl">
                  <Brain className="w-24 h-24 text-white" />
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* CTA Section */}
      <div className="relative py-24 bg-gradient-to-br from-gray-900 to-blue-900">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-5xl font-bold text-white mb-6">
            Ready to revolutionize your learning?
          </h2>
          <p className="text-xl text-blue-100 mb-12 max-w-2xl mx-auto">
            Join professionals and students who trust EZMind AI for their most important learning and knowledge management needs
          </p>
          <Button 
            onClick={() => setShowAuth(true)}
            size="lg"
            className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold px-12 py-4 text-lg rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
          >
            Start Your Free Trial
            <TrendingUp className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-12">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center gap-3 mb-6 md:mb-0">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">EZMind AI</h3>
                <p className="text-sm text-gray-500">Smart Learning Platform</p>
              </div>
            </div>
            <p className="text-gray-500 text-center">© 2024 EZMind AI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
