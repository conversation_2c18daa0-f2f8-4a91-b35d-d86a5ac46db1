import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Crown, ArrowRight, Sparkles, Zap, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { SubscriptionStatus } from '@/components/debug/SubscriptionStatus';
import { toast } from 'sonner';
import { getSubscriptionBySessionId, updateUserSubscription } from '@/services/subscriptionService';
import { UserSubscription } from '@/services/subscriptionService';

const PaymentSuccess: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(true);
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  const sessionId = searchParams.get('session_id');
  const MAX_RETRIES = 3;
  const TIMEOUT_MS = 8000; // 8 seconds timeout

  const fetchSubscriptionWithTimeout = async (sessionId: string, userId: string): Promise<UserSubscription | null> => {
    return Promise.race([
      getSubscriptionBySessionId(sessionId, userId),
      new Promise<null>((_, reject) => 
        setTimeout(() => reject(new Error('Request timeout')), TIMEOUT_MS)
      )
    ]);
  };

  const fetchSubscription = async (retry = false) => {
    if (!user?.uid) {
      return;
    }
    
    if (!sessionId) {
      console.log('❌ No session ID found in URL parameters.');
      setError('Invalid payment session.');
      toast.error('Invalid payment session.');
      setTimeout(() => navigate('/pricing'), 2000);
      setIsProcessing(false);
      return;
    }

    if (retry) {
      setIsRetrying(true);
    }

    console.log(`🔍 Payment success page loaded with session ID: ${sessionId} (attempt ${retryCount + 1})`);
    
    try {
      const sub = await fetchSubscriptionWithTimeout(sessionId, user.uid);
      
      if (sub) {
        setSubscription(sub);
        // Update local storage immediately for faster access
        updateUserSubscription(sub);
        toast.success('🎉 Welcome to EZMind AI Pro! Your subscription is now active.');
        setError(null);
      } else {
        // If no subscription found and we haven't reached max retries, try again
        if (retryCount < MAX_RETRIES) {
          console.log(`Subscription not found, retrying in 2 seconds... (${retryCount + 1}/${MAX_RETRIES})`);
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchSubscription(true), 2000);
          return;
        } else {
          setError('Subscription verification is taking longer than expected. Your payment was successful, but it may take a few minutes to activate. Please try refreshing the page or contact support.');
          toast.error('Subscription verification delayed.');
        }
      }
    } catch (err: any) {
      console.error('Error fetching subscription by session ID:', err);
      
      if (err.message === 'Request timeout' && retryCount < MAX_RETRIES) {
        console.log(`Request timed out, retrying... (${retryCount + 1}/${MAX_RETRIES})`);
        setRetryCount(prev => prev + 1);
        setTimeout(() => fetchSubscription(true), 1000);
        return;
      }
      
      setError(err.message || 'Failed to verify subscription. Please try refreshing the page.');
      toast.error('Failed to verify subscription.');
    } finally {
      setIsProcessing(false);
      setIsRetrying(false);
    }
  };

  useEffect(() => {
    if (user?.uid) {
      fetchSubscription();
    }
  }, [user?.uid]);

  const handleRetry = () => {
    setIsProcessing(true);
    setRetryCount(0);
    setError(null);
    fetchSubscription();
  };

  const handleContinue = () => {
    navigate('/');
  };

  if (isProcessing) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {isRetrying ? 'Retrying...' : 'Verifying Payment...'}
            </h2>
            <p className="text-gray-600">
              {isRetrying 
                ? `Attempt ${retryCount}/${MAX_RETRIES} - This may take a moment` 
                : 'Activating your subscription'
              }
            </p>
            {retryCount > 0 && (
              <div className="mt-4 text-sm text-gray-500">
                <p>Payment processing may take up to 30 seconds</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center pb-6">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <CheckCircle className="w-16 h-16 text-green-600" />
              <div className="absolute -top-1 -right-1">
                <Crown className="w-6 h-6 text-yellow-500" />
              </div>
            </div>

          </div>
          
          <CardTitle className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
            {subscription ? 'Payment Successful! 🎉' : error ? 'Payment Issue' : 'Verifying Payment...'}
          </CardTitle>
          
          {subscription && (
            <p className="text-gray-600 mt-2 text-lg">
              Welcome to EZMind AI Pro! Your subscription is now active.
            </p>
          )}
          {error && (
            <div className="mt-4">
              <p className="text-red-600 text-lg mb-3">
                {error}
              </p>
              <Button
                onClick={handleRetry}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Try Again
              </Button>
            </div>
          )}
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Subscription Details */}
          <div className="bg-gradient-to-r from-green-100 to-blue-100 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Crown className="w-6 h-6 text-yellow-600" />
                <div>
                  <h3 className="font-semibold text-gray-900">EZMind AI Pro</h3>
                  <p className="text-sm text-gray-600">
                    {subscription?.planType === 'monthly' ? 'Monthly' : subscription?.planType === 'yearly' ? 'Yearly' : ''} Subscription
                  </p>
                </div>
              </div>
              <Badge className="bg-green-100 text-green-700">
                Active
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="font-semibold text-gray-900">Plan</div>
                <div className="text-gray-600">
                  {subscription?.planType === 'monthly' ? '$10/month' : subscription?.planType === 'yearly' ? '$100/year' : '-'}
                </div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">Status</div>
                <div className="text-green-600">Active</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">Session</div>
                <div className="text-gray-600 font-mono text-xs">
                  {sessionId?.substring(0, 12)}...
                </div>
              </div>
            </div>
          </div>

          {/* Pro Features Unlocked */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-600" />
              Pro Features Unlocked
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {[
                { icon: <Zap className="w-4 h-4" />, text: "Unlimited Study Topics" },
                { icon: <Zap className="w-4 h-4" />, text: "Unlimited Audio Recording" },
                { icon: <Zap className="w-4 h-4" />, text: "50MB+ File Uploads" },
                { icon: <Zap className="w-4 h-4" />, text: "Advanced AI Features" },
                { icon: <Zap className="w-4 h-4" />, text: "Priority Support" },
                { icon: <Zap className="w-4 h-4" />, text: "Export & Download" }
              ].map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                  <div className="text-green-600">{feature.icon}</div>
                  {feature.text}
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="pt-4 space-y-3">
            <Button
              onClick={handleContinue}
              className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 py-3"
              size="lg"
            >
              Continue to Dashboard
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>

          {/* Support Info */}
          <div className="text-center text-sm text-gray-500 pt-4 border-t">
            <p>
              Need help? Contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Debug Subscription Status */}
      <div className="mt-8">
        <SubscriptionStatus />
      </div>
    </div>
  );
};

export default PaymentSuccess;
