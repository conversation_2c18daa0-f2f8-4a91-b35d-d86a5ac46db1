import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft, Brain, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import StripePricingTable from '@/components/pricing/StripePricingTable';
import { useAuth } from '@/contexts/AuthContext';
import { createStripeCheckout } from '@/services/simplePaymentService';
import { toast } from 'sonner';

const PricingPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleSelectPlan = async (plan: 'monthly' | 'yearly') => {
    if (!user?.uid || !user?.email) {
      toast.error('Please sign in to select a plan');
      return;
    }

    try {
      toast.loading('Creating checkout session...');
      
      // Create Stripe checkout session
      const result = await createStripeCheckout(plan, user.email);
      
      if (result.success) {
        toast.dismiss();
        toast.success('Redirecting to checkout...');
        // The createStripeCheckout function handles the redirect
      } else {
        toast.dismiss();
        toast.error('Failed to activate subscription. Please try again.');
      }
    } catch (error) {
      toast.dismiss();
      toast.error('An error occurred. Please try again.');
      console.error('Subscription activation error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="flex items-center gap-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Dashboard
              </Button>
            </div>

            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2.5 h-2.5 text-white" />
                </div>
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-blue-900 bg-clip-text text-transparent">
                EZMind AI
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-blue-900 bg-clip-text text-transparent mb-8">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Unlock the full potential of AI-powered learning with EZMind AI.
            Transform your study experience with unlimited access to all premium features.
          </p>
          <div className="flex items-center justify-center gap-6 mt-8 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>7-Day Free Trial</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Cancel Anytime</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>Secure Payments</span>
            </div>
          </div>
        </div>

        {/* Stripe Pricing Table */}
        <div className="max-w-4xl mx-auto">
          <StripePricingTable className="w-full" />
        </div>

        {/* FAQ Section */}
        <div className="mt-20 max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Frequently Asked Questions
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Can I cancel anytime?
              </h3>
              <p className="text-gray-600">
                Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Is there a free trial?
              </h3>
              <p className="text-gray-600">
                All paid plans come with a 7-day free trial. No credit card required to start.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                How does subscription activation work?
              </h3>
              <p className="text-gray-600">
                Your subscription is activated instantly when you select a plan. No external payment processing required.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Can I upgrade or downgrade my plan?
              </h3>
              <p className="text-gray-600">
                Yes, you can change your plan at any time. Changes will be prorated and reflected in your next billing cycle.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPage;
