import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  User, 
  CreditCard, 
  Bell, 
  Shield, 
  Trash2, 
  Download,
  Settings,
  Crown,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserSubscription, getUserUsage } from '@/services/subscriptionService';
import { toast } from 'sonner';
import { trackSettingsChanged, trackSubscriptionCancel, trackDataExported } from '@/utils/analytics';

// Import test utilities for development
if (import.meta.env.DEV) {
  import('@/utils/testSettings');
}

const SettingsPage = () => {
  const navigate = useNavigate();
  const { user, userProfile, updateProfile, getUserDisplayName } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const subscription = getUserSubscription();
  const usage = getUserUsage();

  // Profile settings
  const [displayName, setDisplayName] = useState(userProfile?.displayName || getUserDisplayName());
  const [email, setEmail] = useState(userProfile?.email || user?.email || '');
  const [hasChanges, setHasChanges] = useState(false);
  
  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [weeklyDigest, setWeeklyDigest] = useState(true);

  // Sync profile data when userProfile changes
  React.useEffect(() => {
    if (userProfile) {
      setDisplayName(userProfile.displayName);
      setEmail(userProfile.email);
      setHasChanges(false);
    }
  }, [userProfile]);

  // Load notification settings on mount
  React.useEffect(() => {
    const userEmail = user?.email || localStorage.getItem('user-email') || '';
    const stored = localStorage.getItem(`notifications-${userEmail}`);
    if (stored) {
      try {
        const settings = JSON.parse(stored);
        setEmailNotifications(settings.emailNotifications ?? true);
        setPushNotifications(settings.pushNotifications ?? true);
        setWeeklyDigest(settings.weeklyDigest ?? true);
      } catch (error) {
        console.error('Failed to load notification settings:', error);
      }
    }
  }, [user?.email]);

  const saveNotificationSettings = async () => {
    setIsLoading(true);
    try {
      const userEmail = user?.email || localStorage.getItem('user-email') || '';
      const settings = {
        emailNotifications,
        pushNotifications,
        weeklyDigest,
        lastUpdated: new Date().toISOString()
      };

      localStorage.setItem(`notifications-${userEmail}`, JSON.stringify(settings));

      // Track settings change
      trackSettingsChanged('notifications');

      toast.success('Notification settings saved!');
    } catch (error) {
      console.error('Failed to save notification settings:', error);
      toast.error('Failed to save notification settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {};

    if (!displayName.trim()) {
      newErrors.displayName = 'Display name is required';
    } else if (displayName.trim().length < 2) {
      newErrors.displayName = 'Display name must be at least 2 characters';
    } else if (displayName.trim().length > 50) {
      newErrors.displayName = 'Display name must be less than 50 characters';
    }

    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveProfile = async () => {
    if (!validateForm()) {
      toast.error('Please fix the errors before saving');
      return;
    }

    setIsLoading(true);
    try {
      await updateProfile({
        displayName: displayName.trim(),
        email: email.trim()
      });

      setHasChanges(false);
      toast.success('Profile updated successfully!');
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  // Track changes
  const handleDisplayNameChange = (value: string) => {
    setDisplayName(value);
    setHasChanges(true);
    if (errors.displayName) {
      setErrors(prev => ({ ...prev, displayName: '' }));
    }
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    setHasChanges(true);
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: '' }));
    }
  };

  const handleCancelSubscription = async () => {
    const confirmMessage = `Are you sure you want to cancel your subscription?

This will:
• Cancel your subscription at the end of the current billing period
• Remove access to premium features after ${formatDate(subscription.currentPeriodEnd)}
• Keep your data safe until you decide to resubscribe

You can reactivate your subscription anytime before the end date.`;

    if (!confirm(confirmMessage)) {
      return;
    }

    setIsLoading(true);
    try {
      const userEmail = user?.email || localStorage.getItem('user-email') || '';

      // Simulate subscription cancellation
      const cancelledSubscription = {
        ...subscription,
        status: 'active', // Still active until period end
        cancelAtPeriodEnd: true,
        cancelledAt: new Date().toISOString()
      };

      localStorage.setItem(`subscription-${userEmail}`, JSON.stringify(cancelledSubscription));

      // Track subscription cancellation
      trackSubscriptionCancel(subscription.plan, 'user_initiated');

      toast.success('Subscription cancelled. You will retain access until the end of your billing period.');

      // Refresh the page to update subscription status
      setTimeout(() => window.location.reload(), 1500);
    } catch (error) {
      console.error('Cancellation error:', error);
      toast.error('Failed to cancel subscription. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!confirm('Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.')) {
      return;
    }

    if (!confirm('This will permanently delete all your study materials, topics, and account data. Type "DELETE" to confirm.')) {
      return;
    }

    setIsLoading(true);
    try {
      // In a real app, this would call your backend to delete the account
      const userEmail = user?.email || localStorage.getItem('user-email') || '';
      
      // Clear all user data from localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.includes(userEmail) || key.includes('user-') || key.includes('topic-')) {
          localStorage.removeItem(key);
        }
      });
      
      toast.success('Account deleted successfully');
      logout();
      navigate('/');
    } catch (error) {
      toast.error('Failed to delete account');
    } finally {
      setIsLoading(false);
    }
  };

  const exportData = () => {
    try {
      const userEmail = user?.email || localStorage.getItem('user-email') || '';

      // Collect all user data
      const topics = Object.keys(localStorage)
        .filter(key => key.startsWith(`topic-${userEmail}`))
        .map(key => {
          try {
            return JSON.parse(localStorage.getItem(key) || '{}');
          } catch {
            return null;
          }
        })
        .filter(Boolean);

      const userData = {
        exportInfo: {
          exportDate: new Date().toISOString(),
          version: '1.0',
          userEmail: userEmail
        },
        profile: userProfile || { email, displayName },
        subscription: {
          ...subscription,
          // Remove sensitive data
          stripeCustomerId: subscription.stripeCustomerId ? '[REDACTED]' : undefined,
          stripeSubscriptionId: subscription.stripeSubscriptionId ? '[REDACTED]' : undefined
        },
        usage,
        topics: topics,
        statistics: {
          totalTopics: topics.length,
          totalFileUploads: usage.fileUploads,
          totalAIGenerations: usage.aiQuestions,
          totalAudioSessions: usage.audioTranscriptions
        }
      };

      const dataStr = JSON.stringify(userData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const timestamp = new Date().toISOString().split('T')[0];
      const link = document.createElement('a');
      link.href = url;
      link.download = `ezmind-data-export-${timestamp}.json`;
      link.click();

      URL.revokeObjectURL(url);

      // Track data export
      trackDataExported(topics.length);

      toast.success(`Data exported successfully! ${topics.length} topics included.`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export data. Please try again.');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              onClick={() => navigate('/')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </Button>
            <div className="flex items-center gap-2">
              <Settings className="w-6 h-6 text-gray-600" />
              <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="w-4 h-4" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="subscription" className="flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              Subscription
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="w-4 h-4" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Privacy
            </TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="displayName">Display Name</Label>
                    <Input
                      id="displayName"
                      value={displayName}
                      onChange={(e) => handleDisplayNameChange(e.target.value)}
                      placeholder="Enter your display name"
                      className={errors.displayName ? 'border-red-500' : ''}
                    />
                    {errors.displayName && (
                      <p className="text-sm text-red-600">{errors.displayName}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => handleEmailChange(e.target.value)}
                      placeholder="Enter your email"
                      className={errors.email ? 'border-red-500' : ''}
                    />
                    {errors.email && (
                      <p className="text-sm text-red-600">{errors.email}</p>
                    )}
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium">Account Status</h3>
                    <p className="text-sm text-gray-600">
                      Member since {formatDate(new Date().toISOString())}
                    </p>
                  </div>
                  <Badge variant={subscription.plan === 'free' ? 'secondary' : 'default'}>
                    {subscription.plan === 'free' ? 'Free Plan' : 'Pro Plan'}
                  </Badge>
                </div>

                <div className="flex items-center gap-3">
                  <Button
                    onClick={handleSaveProfile}
                    disabled={isLoading || !hasChanges}
                    className={hasChanges ? 'bg-blue-600 hover:bg-blue-700' : ''}
                  >
                    {isLoading ? 'Saving...' : 'Save Changes'}
                  </Button>
                  {hasChanges && (
                    <span className="text-sm text-orange-600">You have unsaved changes</span>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Subscription Tab */}
          <TabsContent value="subscription">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Crown className="w-5 h-5" />
                    Current Plan
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-lg">
                        {subscription.plan === 'free' ? 'Free Plan' : 'EZMind AI Pro'}
                      </h3>
                      <p className="text-gray-600">
                        {subscription.plan === 'free' 
                          ? 'Limited features with usage restrictions'
                          : 'Unlimited access to all features'
                        }
                      </p>
                    </div>
                    <Badge variant={subscription.plan === 'free' ? 'secondary' : 'default'}>
                      {subscription.status}
                    </Badge>
                  </div>

                  {subscription.plan !== 'free' && (
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Calendar className="w-4 h-4 text-blue-600" />
                        <span className="font-medium text-blue-900">Billing Information</span>
                      </div>
                      <p className="text-sm text-blue-700">
                        Next billing date: {formatDate(subscription.currentPeriodEnd)}
                      </p>
                      {subscription.cancelAtPeriodEnd && (
                        <p className="text-sm text-orange-700 mt-1">
                          ⚠️ Subscription will be cancelled at the end of this period
                        </p>
                      )}
                    </div>
                  )}

                  <div className="flex gap-3">
                    {subscription.plan === 'free' ? (
                      <Button 
                        onClick={() => navigate('/pricing')}
                        className="bg-gradient-to-r from-blue-500 to-purple-600"
                      >
                        Upgrade to Pro
                      </Button>
                    ) : (
                      <>
                        <Button variant="outline" onClick={() => navigate('/pricing')}>
                          Change Plan
                        </Button>
                        {!subscription.cancelAtPeriodEnd && (
                          <Button 
                            variant="destructive" 
                            onClick={handleCancelSubscription}
                            disabled={isLoading}
                          >
                            Cancel Subscription
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Usage Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle>Usage Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-700">{usage.fileUploads}</div>
                      <div className="text-sm text-blue-600">Files Uploaded</div>
                      {subscription.plan === 'free' && <div className="text-xs text-gray-500">/ 3 limit</div>}
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-700">{usage.aiQuestions}</div>
                      <div className="text-sm text-purple-600">AI Generations</div>
                      {subscription.plan === 'free' && <div className="text-xs text-gray-500">/ 5 limit</div>}
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-700">{usage.audioTranscriptions}</div>
                      <div className="text-sm text-green-600">Audio Sessions</div>
                      {subscription.plan === 'free' && <div className="text-xs text-gray-500">/ 1 limit</div>}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notification Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Email Notifications</h3>
                      <p className="text-sm text-gray-600">Receive updates about your account and new features</p>
                    </div>
                    <Switch 
                      checked={emailNotifications} 
                      onCheckedChange={setEmailNotifications}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Push Notifications</h3>
                      <p className="text-sm text-gray-600">Get notified when AI processing is complete</p>
                    </div>
                    <Switch 
                      checked={pushNotifications} 
                      onCheckedChange={setPushNotifications}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Weekly Digest</h3>
                      <p className="text-sm text-gray-600">Summary of your learning progress and tips</p>
                    </div>
                    <Switch 
                      checked={weeklyDigest} 
                      onCheckedChange={setWeeklyDigest}
                    />
                  </div>
                </div>

                <Button onClick={saveNotificationSettings} disabled={isLoading}>
                  {isLoading ? 'Saving...' : 'Save Notification Settings'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Privacy Tab */}
          <TabsContent value="privacy">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Data & Privacy</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium mb-2">Export Your Data</h3>
                      <p className="text-sm text-gray-600 mb-3">
                        Download a copy of all your data including study materials, topics, and account information.
                      </p>
                      <Button variant="outline" onClick={exportData}>
                        <Download className="w-4 h-4 mr-2" />
                        Export Data
                      </Button>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="font-medium mb-2 text-red-700 flex items-center gap-2">
                        <AlertTriangle className="w-4 h-4" />
                        Danger Zone
                      </h3>
                      <p className="text-sm text-gray-600 mb-3">
                        Permanently delete your account and all associated data. This action cannot be undone.
                      </p>
                      <Button 
                        variant="destructive" 
                        onClick={handleDeleteAccount}
                        disabled={isLoading}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Account
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SettingsPage;
