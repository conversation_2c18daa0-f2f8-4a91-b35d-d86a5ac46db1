// Document processing service for handling various file types
import mammoth from 'mammoth';
import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js worker with exact version matching
const configurePDFWorker = () => {
  if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
    try {
      // Use the exact same version as the installed package (5.2.133)
      pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/5.2.133/pdf.worker.min.js`;
      console.log('📄 PDF.js worker configured with exact version 5.2.133');
    } catch (error) {
      try {
        // Fallback to unpkg with exact version
        pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@5.2.133/build/pdf.worker.min.js`;
        console.log('📄 PDF.js worker configured with unpkg exact version');
      } catch (fallbackError) {
        // Final fallback to latest unpkg
        pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist/build/pdf.worker.min.js`;
        console.log('📄 PDF.js worker configured with unpkg latest');
      }
    }
  }
};

// Initialize worker configuration
configurePDFWorker();

export interface DocumentProcessingResult {
  content: string;
  fileName: string;
  fileSize: number;
  pageCount?: number;
  wordCount: number;
  metadata?: any;
}

/**
 * Process Word documents (.doc, .docx) using mammoth.js with enhanced error handling
 */
export const processWordDocument = async (file: File): Promise<DocumentProcessingResult> => {
  try {
    console.log('📝 Starting Word document processing for:', file.name);

    const arrayBuffer = await file.arrayBuffer();
    console.log('📝 Word file loaded, size:', arrayBuffer.byteLength, 'bytes');

    // Validate that we have a valid Word document
    if (arrayBuffer.byteLength === 0) {
      throw new Error('Word document appears to be empty');
    }

    // Extract with enhanced debugging and error handling
    console.log('📝 Starting mammoth.js conversion...');

    let htmlResult;
    try {
      htmlResult = await mammoth.convertToHtml({
        arrayBuffer,
        options: {
          includeDefaultStyleMap: true,
          includeEmbeddedStyleMap: false,
          convertImage: mammoth.images.imgElement(function(image) {
            return image.read("base64").then(function(imageBuffer) {
              console.log('🖼️ Image extracted successfully');
              return {
                src: "data:" + image.contentType + ";base64," + imageBuffer
              };
            }).catch(function(error) {
              console.warn('🖼️ Image extraction failed:', error);
              return { src: "" };
            });
          }),
          styleMap: [
            "p[style-name='Heading 1'] => h1:fresh",
            "p[style-name='Heading 2'] => h2:fresh",
            "p[style-name='Heading 3'] => h3:fresh",
            "p[style-name='Title'] => h1:fresh",
            "p[style-name='Subtitle'] => h2:fresh"
          ]
        }
      });
      console.log('📝 Mammoth.js conversion completed successfully');
    } catch (mammothError) {
      console.error('❌ Mammoth.js conversion failed:', mammothError);
      throw new Error(`DOCX processing failed: ${mammothError.message}`);
    }

    const htmlContent = htmlResult.value;
    console.log('📝 Word document converted to HTML, length:', htmlContent.length);

    // Convert HTML to clean text format
    let cleanContent = htmlContent
      // Convert headings to clean text with line breaks
      .replace(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi, '\n\n$1\n\n')

      // Convert paragraphs
      .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')

      // Convert lists to simple bullet points
      .replace(/<ul[^>]*>/gi, '\n')
      .replace(/<\/ul>/gi, '\n')
      .replace(/<ol[^>]*>/gi, '\n')
      .replace(/<\/ol>/gi, '\n')
      .replace(/<li[^>]*>(.*?)<\/li>/gi, '• $1\n')

      // Remove all formatting tags but keep the text
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '$1')
      .replace(/<b[^>]*>(.*?)<\/b>/gi, '$1')
      .replace(/<em[^>]*>(.*?)<\/em>/gi, '$1')
      .replace(/<i[^>]*>(.*?)<\/i>/gi, '$1')
      .replace(/<u[^>]*>(.*?)<\/u>/gi, '$1')

      // Convert line breaks
      .replace(/<br[^>]*>/gi, '\n')

      // Remove all remaining HTML tags
      .replace(/<[^>]*>/g, '')

      // Clean up whitespace and formatting
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")

      // Clean up extra whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      .replace(/^\s+|\s+$/g, '')
      .trim();

    if (!cleanContent.trim()) {
      throw new Error('No text content could be extracted from the Word document');
    }

    const wordCount = cleanContent.split(/\s+/).filter(word => word.length > 0).length;
    console.log('📝 Word document processing completed, extracted words:', wordCount);

    return {
      content: cleanContent,
      fileName: file.name,
      fileSize: file.size,
      wordCount,
      metadata: {
        type: 'word',
        formatted: true,
        messages: htmlResult.messages,
        originalHtml: htmlContent,
        extractedSuccessfully: true
      }
    };
  } catch (error) {
    console.error('❌ Error processing Word document:', error);
    throw new Error(`Failed to process Word document: ${error.message}`);
  }
};

/**
 * Process PDF documents using PDF.js with enhanced error handling
 */
export const processPdfDocument = async (file: File): Promise<DocumentProcessingResult> => {
  try {
    console.log('📄 Starting PDF processing for:', file.name);

    const arrayBuffer = await file.arrayBuffer();
    console.log('📄 PDF file loaded, size:', arrayBuffer.byteLength, 'bytes');

    // Validate that we have a valid PDF
    if (arrayBuffer.byteLength === 0) {
      throw new Error('PDF file appears to be empty');
    }

    // Ensure worker is properly configured for this processing session
    configurePDFWorker();

    console.log('📄 Loading PDF with worker:', pdfjsLib.GlobalWorkerOptions.workerSrc);

    let loadingTask;
    try {
      // First try with worker
      loadingTask = pdfjsLib.getDocument({
        data: arrayBuffer,
        verbosity: 1, // Enable some logging for debugging
        disableAutoFetch: false,
        disableStream: false,
        useSystemFonts: true
      });
    } catch (workerError) {
      console.warn('📄 Worker failed, trying without worker:', workerError);
      // Disable worker and try again
      pdfjsLib.GlobalWorkerOptions.workerSrc = '';
      loadingTask = pdfjsLib.getDocument({
        data: arrayBuffer,
        verbosity: 1,
        disableAutoFetch: false,
        disableStream: false,
        useSystemFonts: true,
        useWorkerFetch: false
      });
    }

    let pdf;
    try {
      pdf = await loadingTask.promise;
      console.log('📄 PDF parsed successfully, pages:', pdf.numPages);
    } catch (loadError) {
      console.error('📄 PDF loading failed:', loadError);
      // Try one more time without worker
      console.log('📄 Retrying PDF load without worker...');
      pdfjsLib.GlobalWorkerOptions.workerSrc = '';
      const retryTask = pdfjsLib.getDocument({
        data: arrayBuffer,
        verbosity: 0,
        disableAutoFetch: true,
        disableStream: true,
        useSystemFonts: false,
        useWorkerFetch: false
      });
      pdf = await retryTask.promise;
      console.log('📄 PDF parsed successfully on retry, pages:', pdf.numPages);
    }

    let formattedContent = '';
    const pageCount = pdf.numPages;

    // Extract text from all pages with clean formatting
    for (let pageNum = 1; pageNum <= pageCount; pageNum++) {
      try {
        console.log(`📄 Processing page ${pageNum}/${pageCount}`);
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        const textItems = textContent.items as any[];

        let pageContent = '';
        let currentY = -1;
        let lineText = '';

        // Group text items by line for clean text extraction
        for (const item of textItems) {
          const itemY = Math.round(item.transform[5]);
          const text = item.str;

          // If this is a new line (different Y position)
          if (currentY !== -1 && Math.abs(itemY - currentY) > 3) {
            if (lineText.trim()) {
              // Add line with proper spacing
              pageContent += `${lineText.trim()}\n\n`;
            }
            lineText = '';
          }

          currentY = itemY;

          // Add spacing between words if needed
          if (lineText && !lineText.endsWith(' ') && !text.startsWith(' ')) {
            lineText += ' ';
          }
          lineText += text;
        }

        // Add the last line
        if (lineText.trim()) {
          pageContent += `${lineText.trim()}\n\n`;
        }

        if (pageContent.trim()) {
          // Add page content without decorative separators
          if (pageNum > 1) {
            formattedContent += `\n\nPage ${pageNum}\n\n`;
          }
          formattedContent += pageContent;
        }
      } catch (pageError) {
        console.warn(`📄 Error processing page ${pageNum}:`, pageError);
        formattedContent += `\n\n[Page ${pageNum} - Content extraction failed]\n\n`;
      }
    }

    if (!formattedContent.trim()) {
      throw new Error('No text content could be extracted from the PDF');
    }

    const wordCount = formattedContent.split(/\s+/).filter(word => word.length > 0).length;
    console.log('📄 PDF processing completed, extracted words:', wordCount);

    return {
      content: formattedContent.trim(),
      fileName: file.name,
      fileSize: file.size,
      pageCount,
      wordCount,
      metadata: {
        type: 'pdf',
        pages: pageCount,
        formatted: true,
        extractedSuccessfully: true
      }
    };
  } catch (error) {
    console.error('❌ Error processing PDF document:', error);
    throw new Error(`Failed to process PDF document: ${error.message}`);
  }
};

/**
 * Process PowerPoint documents (.ppt, .pptx) using Gemini AI
 */
export const processPowerPointDocument = async (file: File): Promise<DocumentProcessingResult> => {
  try {
    console.log('📊 Processing PowerPoint file with Gemini AI:', file.name);

    // Convert file to base64 for Gemini API
    const arrayBuffer = await file.arrayBuffer();
    const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

    // Use the hardcoded API key (same as other components)
    const GEMINI_API_KEY = 'AIzaSyBd5ImRFOeTFhAQUgBVjhsTkFHsHmelbmI';

    // Use Gemini to extract content from PowerPoint
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-001:generateContent?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: "Extract all text content from this PowerPoint presentation. Include slide titles, bullet points, and any text content. Format it as structured markdown with clear headings for each slide."
          }, {
            inline_data: {
              mime_type: file.type,
              data: base64Data
            }
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: 8192,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status}`);
    }

    const data = await response.json();
    let extractedContent = data.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!extractedContent) {
      throw new Error('No content extracted from PowerPoint');
    }

    // Clean and format the content
    let formattedContent = `# PowerPoint Presentation: ${file.name}\n\n`;
    formattedContent += `## File Information\n`;
    formattedContent += `- **File Size:** ${(file.size / 1024 / 1024).toFixed(2)} MB\n`;
    formattedContent += `- **Format:** ${file.name.endsWith('.pptx') ? 'PowerPoint 2007+' : 'PowerPoint Legacy'}\n`;
    formattedContent += `- **Processed:** ${new Date().toLocaleString()}\n\n`;
    formattedContent += `## Extracted Content\n\n`;
    formattedContent += extractedContent;

    // Simulate slide content structure
    for (let i = 1; i <= Math.min(slideCount, 10); i++) {
      formattedContent += `## Slide ${i}\n\n`;
      formattedContent += `### Slide Title\n`;
      formattedContent += `[Title content would appear here]\n\n`;
      formattedContent += `### Slide Content\n`;
      formattedContent += `• Bullet point 1\n`;
      formattedContent += `• Bullet point 2\n`;
      formattedContent += `• Bullet point 3\n\n`;
      formattedContent += `*[Additional slide content, images, and formatting would be preserved here]*\n\n`;
      formattedContent += `---\n\n`;
    }

    if (slideCount > 10) {
      formattedContent += `*... and ${slideCount - 10} more slides*\n\n`;
    }

    const wordCount = formattedContent.split(/\s+/).filter(word => word.length > 0).length;

    console.log('✅ PowerPoint processed successfully with Gemini AI');

    return {
      content: formattedContent,
      fileName: file.name,
      fileSize: file.size,
      wordCount,
      metadata: {
        type: 'powerpoint',
        extractedWithAI: true,
        formatted: true
      }
    };
  } catch (error) {
    console.error('❌ Error processing PowerPoint with Gemini:', error);

    // Fallback to basic processing
    console.log('🔄 Using fallback PowerPoint processing...');

    let formattedContent = `# PowerPoint Presentation: ${file.name}\n\n`;
    formattedContent += `## File Information\n`;
    formattedContent += `- **File Size:** ${(file.size / 1024 / 1024).toFixed(2)} MB\n`;
    formattedContent += `- **Format:** ${file.name.endsWith('.pptx') ? 'PowerPoint 2007+' : 'PowerPoint Legacy'}\n`;
    formattedContent += `- **Processed:** ${new Date().toLocaleString()}\n\n`;
    formattedContent += `## Processing Status\n\n`;
    formattedContent += `⚠️ **AI extraction temporarily unavailable**\n\n`;
    formattedContent += `The PowerPoint file has been uploaded successfully. While automatic content extraction is being configured, you can:\n\n`;
    formattedContent += `1. **Manual Input:** Copy and paste key content from your slides\n`;
    formattedContent += `2. **AI Analysis:** The system can still generate study materials based on the file context\n`;
    formattedContent += `3. **Slide Summary:** Provide a brief description of the main topics covered\n\n`;
    formattedContent += `**Error Details:** ${error.message}\n\n`;
    formattedContent += `*Note: PowerPoint content extraction will be enhanced in the next update.*`;

    const wordCount = formattedContent.split(/\s+/).filter(word => word.length > 0).length;

    return {
      content: formattedContent,
      fileName: file.name,
      fileSize: file.size,
      wordCount,
      metadata: {
        type: 'powerpoint',
        extractedWithAI: false,
        formatted: true,
        error: error.message
      }
    };
  }
};

/**
 * Process video files (.mp4, .mov, .avi, etc.) with AI transcription
 */
export const processVideoFile = async (file: File): Promise<DocumentProcessingResult> => {
  try {
    console.log('🎬 Processing video file with AI:', file.name, file.type, file.size);

    // Get video duration and metadata
    const videoMetadata = await getVideoMetadata(file);

    // Try to process video with Gemini AI for transcription
    try {
      console.log('🚀 Attempting AI video transcription...');

      // Convert file to base64 for Gemini API
      const arrayBuffer = await file.arrayBuffer();
      const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

      const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
      if (!GEMINI_API_KEY) {
        throw new Error('Gemini API key not configured');
      }

      // Use Gemini to transcribe video
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-001:generateContent?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: "Transcribe this video file and extract all spoken content. Format the transcription with timestamps if possible and include any important visual information or context."
            }, {
              inline_data: {
                mime_type: file.type,
                data: base64Data
              }
            }]
          }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 8192,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const data = await response.json();
      const transcript = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (transcript && transcript.trim().length > 0) {
        // Successfully transcribed
        const content = `# Video Transcription: ${file.name}

## File Information
- **Size:** ${(file.size / 1024 / 1024).toFixed(2)} MB
- **Format:** ${file.type || 'Unknown'}
- **Duration:** ${videoMetadata.duration ? Math.round(videoMetadata.duration) + ' seconds' : 'Unknown'}
- **Processed:** ${new Date().toLocaleString()}

## Video Transcript

${transcript}

## Processing Notes
✅ **Successfully transcribed using AI**
- Audio content extracted and analyzed
- Ready for study material generation
- Transcript can be used for notes, summaries, and flashcards`;

        const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

        return {
          content,
          fileName: file.name,
          fileSize: file.size,
          wordCount,
          metadata: {
            type: 'video',
            duration: videoMetadata.duration,
            transcribed: true,
            formatted: true
          }
        };
      } else {
        throw new Error('No transcript received from AI');
      }
    } catch (aiError) {
      console.warn('⚠️ AI transcription failed, using fallback:', aiError);

      // Fallback to basic video processing
      const content = `# Video File: ${file.name}

## File Information
- **Size:** ${(file.size / 1024 / 1024).toFixed(2)} MB
- **Format:** ${file.type || 'Unknown'}
- **Duration:** ${videoMetadata.duration ? Math.round(videoMetadata.duration) + ' seconds' : 'Unknown'}
- **Processed:** ${new Date().toLocaleString()}

## Video Content Processing

⚠️ **Automatic transcription temporarily unavailable**

This video file has been uploaded successfully. While automatic transcription is being configured, you can:

1. **Manual Summary:** Provide a brief description of the video content
2. **Key Topics:** List the main topics covered in the video
3. **AI Analysis:** The system can still generate study materials based on video context
4. **Alternative Upload:** Try uploading the audio track separately if available

## Processing Options

- **Audio Extraction:** The audio track can be extracted and transcribed
- **Content Analysis:** AI can analyze the video content and context
- **Study Materials:** Generate notes, summaries, and key points
- **Interactive Content:** Create flashcards and quizzes based on video

## Technical Details
- **File Type:** ${file.type}
- **File Size:** ${file.size} bytes
- **Processing Method:** Video Content Analysis
- **Status:** Ready for AI processing

- **Upload Audio Separately:** Extract audio using video editing software and upload as MP3/WAV
- **Provide Context:** Add manual notes about the video content for AI processing

**Error Details:** ${aiError.message}

*Note: Video transcription capabilities will be enhanced in the next update.*`;

      const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

      return {
        content,
        fileName: file.name,
        fileSize: file.size,
        wordCount,
        metadata: {
          type: 'video',
          duration: videoMetadata.duration,
          transcribed: false,
          formatted: true,
          error: aiError.message
        }
      };
    }
  } catch (error) {
    console.error('❌ Error processing video file:', error);
    throw new Error(`Failed to process video file: ${error.message}`);
  }
};

/**
 * Get video metadata (duration, etc.)
 */
const getVideoMetadata = async (file: File): Promise<{ duration?: number }> => {
  return new Promise((resolve) => {
    try {
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        resolve({ duration: video.duration });
        URL.revokeObjectURL(video.src);
      };

      video.onerror = () => {
        resolve({ duration: undefined });
        URL.revokeObjectURL(video.src);
      };

      video.src = URL.createObjectURL(file);
    } catch (error) {
      resolve({ duration: undefined });
    }
  });
};

/**
 * Process plain text files
 */
export const processTextFile = async (file: File): Promise<DocumentProcessingResult> => {
  try {
    const content = await file.text();
    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

    return {
      content,
      fileName: file.name,
      fileSize: file.size,
      wordCount,
      metadata: {
        type: 'text'
      }
    };
  } catch (error) {
    console.error('Error processing text file:', error);
    throw new Error('Failed to process text file');
  }
};

/**
 * Main document processing function that handles different file types
 */
export const processDocument = async (file: File): Promise<DocumentProcessingResult> => {
  const fileExtension = file.name.toLowerCase().split('.').pop();
  const mimeType = file.type.toLowerCase();

  try {
    // Handle Word documents
    if (fileExtension === 'docx' || mimeType.includes('wordprocessingml')) {
      return await processWordDocument(file);
    }

    // Handle older Word documents
    if (fileExtension === 'doc' || mimeType.includes('msword')) {
      return await processWordDocument(file);
    }

    // Handle PDF documents
    if (fileExtension === 'pdf' || mimeType === 'application/pdf') {
      return await processPdfDocument(file);
    }

    // Handle PowerPoint documents
    if (fileExtension === 'ppt' || fileExtension === 'pptx' ||
        mimeType.includes('presentationml') || mimeType.includes('ms-powerpoint')) {
      return await processPowerPointDocument(file);
    }

    // Handle video files
    if (fileExtension === 'mp4' || fileExtension === 'mov' || fileExtension === 'avi' ||
        fileExtension === 'mkv' || fileExtension === 'webm' ||
        mimeType.startsWith('video/')) {
      return await processVideoFile(file);
    }

    // Handle text files
    if (fileExtension === 'txt' || mimeType === 'text/plain') {
      return await processTextFile(file);
    }

    // Fallback for unknown types
    throw new Error(`Unsupported file type: ${fileExtension || mimeType}`);

  } catch (error) {
    console.error('Error processing document:', error);
    throw error;
  }
};

/**
 * Check if a file is a supported document type
 */
export const isSupportedDocument = (file: File): boolean => {
  const fileExtension = file.name.toLowerCase().split('.').pop();
  const mimeType = file.type.toLowerCase();

  const supportedExtensions = ['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx'];
  const supportedMimeTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-powerpoint',
    'text/plain'
  ];

  return supportedExtensions.includes(fileExtension || '') ||
         supportedMimeTypes.some(type => mimeType.includes(type));
};

/**
 * Extract key information from document content
 */
export const extractKeyInformation = (content: string): {
  topics: string[];
  keyTerms: string[];
  summary: string;
} => {
  // Simple keyword extraction (in production, use NLP libraries)
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = content.toLowerCase().match(/\b\w+\b/g) || [];

  // Extract potential topics (capitalized words/phrases)
  const topics = Array.from(new Set(
    content.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || []
  )).slice(0, 10);

  // Extract key terms (frequent words, excluding common words)
  const commonWords = new Set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those']);

  const wordFreq = words.reduce((freq: Record<string, number>, word) => {
    if (!commonWords.has(word) && word.length > 3) {
      freq[word] = (freq[word] || 0) + 1;
    }
    return freq;
  }, {});

  const keyTerms = Object.entries(wordFreq)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 15)
    .map(([word]) => word);

  // Create a simple summary (first few sentences)
  const summary = sentences.slice(0, 3).join('. ') + '.';

  return {
    topics,
    keyTerms,
    summary
  };
};

/**
 * Format document content for better readability
 */
export const formatDocumentContent = (content: string, fileName: string): string => {
  const { topics, keyTerms, summary } = extractKeyInformation(content);

  return `# Document: ${fileName}

## Summary
${summary}

## Key Topics
${topics.map(topic => `- ${topic}`).join('\n')}

## Key Terms
${keyTerms.map(term => `- ${term}`).join('\n')}

## Full Content
${content}

---
*Processed by Study Spark AI*`;
};

/**
 * Get file type icon for display
 */
export const getFileTypeIcon = (fileName: string): string => {
  const extension = fileName.toLowerCase().split('.').pop();

  switch (extension) {
    case 'pdf': return '📄';
    case 'doc':
    case 'docx': return '📝';
    case 'ppt':
    case 'pptx': return '📊';
    case 'txt': return '📋';
    default: return '📄';
  }
};
