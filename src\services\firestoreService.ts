/**
 * Firestore Service for database operations
 */

import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  query, 
  where, 
  getDocs,
  serverTimestamp,
  onSnapshot
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { UserSubscription } from './subscriptionService';

export interface FirestoreUser {
  uid: string;
  email: string;
  displayName?: string;
  subscription?: UserSubscription;
  stripeCustomerId?: string;
  createdAt: any;
  lastUpdated: any;
}

export interface FirestoreSubscription {
  userId: string;
  userEmail: string;
  plan: string;
  status: string;
  planType: string;
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  currentPeriodEnd: string;
  createdAt: any;
  updatedAt: any;
}

/**
 * Create or update user document in Firestore
 */
export const createOrUpdateUser = async (
  uid: string, 
  email: string, 
  displayName?: string,
  subscription?: UserSubscription
): Promise<void> => {
  try {
    const userRef = doc(db, 'users', uid);
    
    const userData: Partial<FirestoreUser> = {
      uid,
      email,
      lastUpdated: serverTimestamp(),
    };

    if (displayName) {
      userData.displayName = displayName;
    }

    if (subscription) {
      userData.subscription = subscription;
    }

    // Check if user exists
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      // Create new user
      userData.createdAt = serverTimestamp();
      await setDoc(userRef, userData);
      console.log('✅ New user created in Firestore:', uid);
    } else {
      // Update existing user
      await updateDoc(userRef, userData);
      console.log('✅ User updated in Firestore:', uid);
    }
  } catch (error) {
    console.error('Error creating/updating user:', error);
    throw error;
  }
};

/**
 * Get user from Firestore
 */
export const getUser = async (uid: string): Promise<FirestoreUser | null> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      return userDoc.data() as FirestoreUser;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
};

/**
 * Update user subscription in Firestore
 */
export const updateUserSubscription = async (
  uid: string,
  subscription: UserSubscription,
  sessionId: string,
  planType: string
): Promise<void> => {
  try {
    const userRef = doc(db, 'users', uid);
    
    // Update user document
    await updateDoc(userRef, {
      subscription: subscription,
      lastUpdated: serverTimestamp(),
      stripeCustomerId: subscription.stripeCustomerId,
    });

    // Create/update subscription document
    const subscriptionRef = doc(db, 'subscriptions', sessionId);
    const userDoc = await getDoc(userRef);
    const userData = userDoc.data() as FirestoreUser;

    const subscriptionData: FirestoreSubscription = {
      userId: uid,
      userEmail: userData.email,
      plan: subscription.plan,
      status: subscription.status,
      planType: planType,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      stripeCustomerId: subscription.stripeCustomerId,
      currentPeriodEnd: subscription.currentPeriodEnd,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    await setDoc(subscriptionRef, subscriptionData);
    
    console.log('✅ User subscription updated in Firestore:', uid);
  } catch (error) {
    console.error('Error updating user subscription:', error);
    throw error;
  }
};

/**
 * Get user subscription from Firestore
 */
export const getUserSubscriptionFromFirestore = async (uid: string): Promise<UserSubscription | null> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data() as FirestoreUser;
      return userData.subscription || null;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting user subscription from Firestore:', error);
    return null;
  }
};

/**
 * Listen to user subscription changes
 */
export const subscribeToUserChanges = (
  uid: string, 
  callback: (user: FirestoreUser | null) => void
): (() => void) => {
  const userRef = doc(db, 'users', uid);
  
  return onSnapshot(userRef, (doc) => {
    if (doc.exists()) {
      callback(doc.data() as FirestoreUser);
    } else {
      callback(null);
    }
  }, (error) => {
    console.error('Error listening to user changes:', error);
    callback(null);
  });
};

/**
 * Get active subscriptions for a user
 */
export const getActiveSubscriptions = async (uid: string): Promise<FirestoreSubscription[]> => {
  try {
    const subscriptionsRef = collection(db, 'subscriptions');
    const q = query(
      subscriptionsRef,
      where('userId', '==', uid),
      where('status', '==', 'active')
    );
    
    const querySnapshot = await getDocs(q);
    const subscriptions: FirestoreSubscription[] = [];
    
    querySnapshot.forEach((doc) => {
      subscriptions.push(doc.data() as FirestoreSubscription);
    });
    
    return subscriptions;
  } catch (error) {
    console.error('Error getting active subscriptions:', error);
    return [];
  }
};

/**
 * Sync localStorage with Firestore
 */
export const syncUserDataWithFirestore = async (uid: string, email: string): Promise<void> => {
  try {
    // Get user from Firestore
    const firestoreUser = await getUser(uid);
    
    if (firestoreUser?.subscription) {
      // Update localStorage with Firestore data
      localStorage.setItem('user-subscription', JSON.stringify(firestoreUser.subscription));
      localStorage.setItem(`subscription-${email}`, JSON.stringify(firestoreUser.subscription));
      
      console.log('✅ User data synced from Firestore to localStorage');
    } else {
      // Get subscription from localStorage and save to Firestore
      const localSubscription = localStorage.getItem('user-subscription');
      if (localSubscription) {
        const subscription = JSON.parse(localSubscription) as UserSubscription;
        await createOrUpdateUser(uid, email, undefined, subscription);
        console.log('✅ User data synced from localStorage to Firestore');
      }
    }
  } catch (error) {
    console.error('Error syncing user data:', error);
  }
};
