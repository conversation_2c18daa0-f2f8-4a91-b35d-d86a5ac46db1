// Gamification service for study streaks, progress tracking, and achievements

export interface StudyStreak {
  currentStreak: number;
  longestStreak: number;
  lastStudyDate: string;
  totalStudyDays: number;
}

export interface TopicProgress {
  topicId: string;
  topicTitle: string;
  completionPercentage: number;
  notesGenerated: boolean;
  flashcardsGenerated: boolean;
  quizGenerated: boolean;
  summaryGenerated: boolean;
  lastUpdated: string;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt?: string;
  progress?: number;
  target?: number;
}

export interface UserStats {
  totalTopics: number;
  totalStudyTime: number; // in minutes
  totalFlashcardsCreated: number;
  totalQuizzesTaken: number;
  averageQuizScore: number;
  level: number;
  experiencePoints: number;
}

// Achievement definitions
const ACHIEVEMENTS: Achievement[] = [
  {
    id: 'first_topic',
    title: 'Getting Started',
    description: 'Create your first study topic',
    icon: '🎯',
    target: 1
  },
  {
    id: 'streak_3',
    title: 'Consistent Learner',
    description: 'Study for 3 days in a row',
    icon: '🔥',
    target: 3
  },
  {
    id: 'streak_7',
    title: 'Week Warrior',
    description: 'Study for 7 days in a row',
    icon: '⚡',
    target: 7
  },
  {
    id: 'streak_30',
    title: 'Study Master',
    description: 'Study for 30 days in a row',
    icon: '👑',
    target: 30
  },
  {
    id: 'flashcards_50',
    title: 'Card Collector',
    description: 'Generate 50 flashcards',
    icon: '🃏',
    target: 50
  },
  {
    id: 'quizzes_10',
    title: 'Quiz Champion',
    description: 'Complete 10 quizzes',
    icon: '🏆',
    target: 10
  },
  {
    id: 'perfect_score',
    title: 'Perfectionist',
    description: 'Get 100% on a quiz',
    icon: '💯',
    target: 1
  },
  {
    id: 'topics_10',
    title: 'Knowledge Seeker',
    description: 'Create 10 study topics',
    icon: '📚',
    target: 10
  }
];

/**
 * Get user's current study streak
 */
export const getStudyStreak = (userEmail?: string): StudyStreak => {
  const key = `study-streak-${userEmail || 'anonymous'}`;
  const stored = localStorage.getItem(key);
  
  if (stored) {
    return JSON.parse(stored);
  }
  
  return {
    currentStreak: 0,
    longestStreak: 0,
    lastStudyDate: '',
    totalStudyDays: 0
  };
};

/**
 * Update study streak when user studies
 */
export const updateStudyStreak = (userEmail?: string): StudyStreak => {
  const streak = getStudyStreak(userEmail);
  const today = new Date().toDateString();
  const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();
  
  if (streak.lastStudyDate === today) {
    // Already studied today, no change
    return streak;
  }
  
  if (streak.lastStudyDate === yesterday) {
    // Continuing streak
    streak.currentStreak += 1;
  } else if (streak.lastStudyDate === '') {
    // First time studying
    streak.currentStreak = 1;
  } else {
    // Streak broken, start over
    streak.currentStreak = 1;
  }
  
  streak.lastStudyDate = today;
  streak.totalStudyDays += 1;
  streak.longestStreak = Math.max(streak.longestStreak, streak.currentStreak);
  
  const key = `study-streak-${userEmail || 'anonymous'}`;
  localStorage.setItem(key, JSON.stringify(streak));
  
  // Check for streak achievements
  checkStreakAchievements(streak.currentStreak, userEmail);
  
  return streak;
};

/**
 * Get topic progress
 */
export const getTopicProgress = (topicId: string, userEmail?: string): TopicProgress => {
  const key = `topic-progress-${userEmail || 'anonymous'}-${topicId}`;
  const stored = localStorage.getItem(key);
  
  if (stored) {
    return JSON.parse(stored);
  }
  
  return {
    topicId,
    topicTitle: '',
    completionPercentage: 0,
    notesGenerated: false,
    flashcardsGenerated: false,
    quizGenerated: false,
    summaryGenerated: false,
    lastUpdated: new Date().toISOString()
  };
};

/**
 * Update topic progress
 */
export const updateTopicProgress = (
  topicId: string, 
  updates: Partial<TopicProgress>, 
  userEmail?: string
): TopicProgress => {
  const progress = getTopicProgress(topicId, userEmail);
  const updatedProgress = { ...progress, ...updates, lastUpdated: new Date().toISOString() };
  
  // Calculate completion percentage
  const completedItems = [
    updatedProgress.notesGenerated,
    updatedProgress.flashcardsGenerated,
    updatedProgress.quizGenerated,
    updatedProgress.summaryGenerated
  ].filter(Boolean).length;
  
  updatedProgress.completionPercentage = (completedItems / 4) * 100;
  
  const key = `topic-progress-${userEmail || 'anonymous'}-${topicId}`;
  localStorage.setItem(key, JSON.stringify(updatedProgress));
  
  // Update study streak when progress is made
  updateStudyStreak(userEmail);
  
  return updatedProgress;
};

/**
 * Get user achievements
 */
export const getUserAchievements = (userEmail?: string): Achievement[] => {
  const key = `achievements-${userEmail || 'anonymous'}`;
  const stored = localStorage.getItem(key);
  
  if (stored) {
    return JSON.parse(stored);
  }
  
  return ACHIEVEMENTS.map(achievement => ({ ...achievement }));
};

/**
 * Unlock achievement
 */
export const unlockAchievement = (achievementId: string, userEmail?: string): boolean => {
  const achievements = getUserAchievements(userEmail);
  const achievement = achievements.find(a => a.id === achievementId);
  
  if (achievement && !achievement.unlockedAt) {
    achievement.unlockedAt = new Date().toISOString();
    
    const key = `achievements-${userEmail || 'anonymous'}`;
    localStorage.setItem(key, JSON.stringify(achievements));
    
    return true; // New achievement unlocked
  }
  
  return false; // Already unlocked or not found
};

/**
 * Check and unlock streak achievements
 */
const checkStreakAchievements = (currentStreak: number, userEmail?: string): void => {
  const streakAchievements = ['streak_3', 'streak_7', 'streak_30'];
  const targets = [3, 7, 30];
  
  streakAchievements.forEach((achievementId, index) => {
    if (currentStreak >= targets[index]) {
      unlockAchievement(achievementId, userEmail);
    }
  });
};

/**
 * Get user stats
 */
export const getUserStats = (userEmail?: string): UserStats => {
  const key = `user-stats-${userEmail || 'anonymous'}`;
  const stored = localStorage.getItem(key);
  
  if (stored) {
    return JSON.parse(stored);
  }
  
  return {
    totalTopics: 0,
    totalStudyTime: 0,
    totalFlashcardsCreated: 0,
    totalQuizzesTaken: 0,
    averageQuizScore: 0,
    level: 1,
    experiencePoints: 0
  };
};

/**
 * Update user stats
 */
export const updateUserStats = (updates: Partial<UserStats>, userEmail?: string): UserStats => {
  const stats = getUserStats(userEmail);
  const updatedStats = { ...stats, ...updates };
  
  // Calculate level based on experience points
  updatedStats.level = Math.floor(updatedStats.experiencePoints / 100) + 1;
  
  const key = `user-stats-${userEmail || 'anonymous'}`;
  localStorage.setItem(key, JSON.stringify(updatedStats));
  
  return updatedStats;
};

/**
 * Add experience points
 */
export const addExperiencePoints = (points: number, userEmail?: string): UserStats => {
  const stats = getUserStats(userEmail);
  return updateUserStats({ experiencePoints: stats.experiencePoints + points }, userEmail);
};

/**
 * Get recently unlocked achievements (last 7 days)
 */
export const getRecentAchievements = (userEmail?: string): Achievement[] => {
  const achievements = getUserAchievements(userEmail);
  const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  
  return achievements.filter(achievement => 
    achievement.unlockedAt && 
    new Date(achievement.unlockedAt) > sevenDaysAgo
  );
};
