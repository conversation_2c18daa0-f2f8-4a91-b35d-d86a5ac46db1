
// Re-export Vertex AI service functions for backward compatibility
import vertexAI from './vertexAIService';

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

// Re-export all Vertex AI functions for backward compatibility
export const generateContent = vertexAI.generateContent;
export const generateNotes = vertexAI.generateNotes;
export const generateQuiz = vertexAI.generateQuiz;
export const generateFlashcards = vertexAI.generateFlashcards;
export const processAudioTranscript = vertexAI.processAudioTranscript;
export const generateFromAudio = vertexAI.generateFromAudio;

// Additional function for summary generation
export const generateSummary = async (content: string): Promise<string> => {
  // Check if content is a simple topic or full content
  const isFullContent = content.length > 200 || content.includes('\n') || content.includes('#');

  let prompt = '';

  if (isFullContent) {
    prompt = `Summarize the following content into clear, concise points that capture the main ideas and key information:

${content}

Provide a well-structured summary with:
- Main points highlighted
- Key takeaways
- Important details preserved
- Easy to understand format`;
  } else {
    prompt = `Create a comprehensive summary about "${content}" with:
- Main concepts and ideas
- Key points to understand
- Important facts and details
- Clear, easy to understand format`;
  }

  return generateContent(prompt);
};


