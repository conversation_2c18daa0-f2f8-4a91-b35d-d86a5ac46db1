// Paywall enforcement service for free tier limitations

import {
  getUserSubscription,
  getUserUsage,
  canPerformAction,
  hasFeatureAccess,
  incrementUsage,
  type UserUsage
} from './subscriptionService';

export interface UserPaywallState {
  hasSeenWelcomePaywall: boolean;
  hasSubscription: boolean;
  signupDate: string;
  lastPaywallShown: string | null;
  paywallDismissCount: number;
}

export interface PaywallCheck {
  allowed: boolean;
  reason?: string;
  upgradeMessage?: string;
  currentUsage?: number;
  limit?: number;
}

/**
 * Check if user can create a new topic
 */
export const checkTopicCreation = (userEmail?: string): PaywallCheck => {
  const subscription = getUserSubscription();
  
  if (subscription.plan !== 'free') {
    return { allowed: true };
  }

  const usage = getUserUsage();
  const limit = 3; // Free tier limit
  
  if (usage.fileUploads >= limit) {
    return {
      allowed: false,
      reason: 'Topic limit reached',
      upgradeMessage: `You've reached the free tier limit of ${limit} study topics. Upgrade to create unlimited topics.`,
      currentUsage: usage.fileUploads,
      limit
    };
  }

  return { allowed: true };
};

/**
 * Check if user can upload files
 */
export const checkFileUpload = (fileSize: number, userEmail?: string): PaywallCheck => {
  const subscription = getUserSubscription();
  
  if (subscription.plan !== 'free') {
    return { allowed: true };
  }

  const usage = getUserUsage();
  const storageLimitMB = 5; // 5MB for free tier
  const fileSizeMB = fileSize / (1024 * 1024);
  
  if (usage.libraryStorage + fileSizeMB > storageLimitMB) {
    return {
      allowed: false,
      reason: 'Storage limit exceeded',
      upgradeMessage: `This file would exceed your ${storageLimitMB}MB storage limit. Upgrade for unlimited storage.`,
      currentUsage: Math.round(usage.libraryStorage * 100) / 100,
      limit: storageLimitMB
    };
  }

  return { allowed: true };
};

/**
 * Check if user can use AI features
 */
export const checkAIUsage = (userEmail?: string): PaywallCheck => {
  const subscription = getUserSubscription();
  
  if (subscription.plan !== 'free') {
    return { allowed: true };
  }

  const usage = getUserUsage();
  const limit = 5; // Free tier limit
  
  if (usage.aiQuestions >= limit) {
    return {
      allowed: false,
      reason: 'AI usage limit reached',
      upgradeMessage: `You've used all ${limit} AI generations for this month. Upgrade for unlimited AI features.`,
      currentUsage: usage.aiQuestions,
      limit
    };
  }

  return { allowed: true };
};

/**
 * Check if user can record audio
 */
export const checkAudioRecording = (userEmail?: string): PaywallCheck => {
  const subscription = getUserSubscription();
  
  if (subscription.plan !== 'free') {
    return { allowed: true };
  }

  const usage = getUserUsage();
  const limit = 1; // 1 recording for free tier (representing 5 minutes)
  
  if (usage.audioTranscriptions >= limit) {
    return {
      allowed: false,
      reason: 'Recording limit reached',
      upgradeMessage: `You've reached the free tier limit of 5 minutes total recording time. Upgrade for unlimited recording.`,
      currentUsage: usage.audioTranscriptions,
      limit
    };
  }

  return { allowed: true };
};

/**
 * Check if user can access premium features
 */
export const checkPremiumFeature = (feature: string, userEmail?: string): PaywallCheck => {
  const subscription = getUserSubscription();
  
  if (subscription.plan !== 'free') {
    return { allowed: true };
  }

  const premiumFeatures = [
    'multipleFiles',
    'mathExpert', 
    'liveRecording',
    'imageAnalysis',
    'advancedAI'
  ];

  if (premiumFeatures.includes(feature)) {
    return {
      allowed: false,
      reason: 'Premium feature',
      upgradeMessage: `This is a premium feature. Upgrade to access ${feature} and all advanced capabilities.`
    };
  }

  return { allowed: true };
};

/**
 * Enforce paywall and increment usage if allowed
 */
export const enforcePaywall = async (
  action: keyof UserUsage, 
  amount: number = 1,
  userEmail?: string
): Promise<PaywallCheck> => {
  let check: PaywallCheck;

  switch (action) {
    case 'fileUploads':
      check = checkTopicCreation(userEmail);
      break;
    case 'aiQuestions':
      check = checkAIUsage(userEmail);
      break;
    case 'audioTranscriptions':
      check = checkAudioRecording(userEmail);
      break;
    default:
      check = { allowed: true };
  }

  if (check.allowed) {
    incrementUsage(action, amount);
  }

  return check;
};

/**
 * Get usage warnings for approaching limits
 */
export const getUsageWarnings = (userEmail?: string): string[] => {
  const subscription = getUserSubscription();
  
  if (subscription.plan !== 'free') {
    return [];
  }

  const usage = getUserUsage();
  const warnings: string[] = [];

  // Check AI usage
  if (usage.aiQuestions >= 4) {
    warnings.push(`You have ${5 - usage.aiQuestions} AI generations remaining this month.`);
  }

  // Check topic creation
  if (usage.fileUploads >= 2) {
    warnings.push(`You can create ${3 - usage.fileUploads} more study topics.`);
  }

  // Check storage
  if (usage.libraryStorage >= 4) {
    warnings.push(`You're using ${Math.round(usage.libraryStorage * 100) / 100}MB of your 5MB storage limit.`);
  }

  // Check audio recording
  if (usage.audioTranscriptions >= 1) {
    warnings.push(`You've used your free recording time. Upgrade for unlimited recording.`);
  }

  return warnings;
};

/**
 * Show upgrade prompt with specific messaging
 */
export const showUpgradePrompt = (check: PaywallCheck): void => {
  if (!check.allowed && check.upgradeMessage) {
    // This would trigger the paywall modal in the UI
    const event = new CustomEvent('showPaywall', {
      detail: {
        feature: check.reason,
        description: check.upgradeMessage,
        requiredPlan: 'basic',
        paymentUrls: {
          monthly: '/pricing',
          yearly: '/pricing'
        }
      }
    });
    window.dispatchEvent(event);
  }
};

/**
 * Direct subscription activation (replaces Stripe checkout)
 */
export const redirectToUpgrade = (planType: 'monthly' | 'yearly' = 'monthly'): void => {
  // Redirect to pricing page for direct activation
  window.location.href = '/pricing';
};

/**
 * Check if user should see upgrade prompts
 */
export const shouldShowUpgradePrompt = (userEmail?: string): boolean => {
  const subscription = getUserSubscription();

  if (subscription.plan !== 'free') {
    return false;
  }

  const usage = getUserUsage();

  // Show upgrade prompt if user is close to any limit
  return (
    usage.aiQuestions >= 3 ||
    usage.fileUploads >= 2 ||
    usage.libraryStorage >= 3 ||
    usage.audioTranscriptions >= 1
  );
};

/**
 * Check if user is a new user (signed up recently)
 */
export const isNewUser = (userEmail: string): boolean => {
  const userState = getUserPaywallState(userEmail);
  const signupDate = new Date(userState.signupDate);
  const now = new Date();
  const hoursSinceSignup = (now.getTime() - signupDate.getTime()) / (1000 * 60 * 60);

  // Consider user "new" if they signed up within the last 24 hours
  return hoursSinceSignup < 24;
};

/**
 * Check if user should see the welcome paywall
 */
export const shouldShowWelcomePaywall = (userEmail: string): boolean => {
  const userState = getUserPaywallState(userEmail);

  // Don't show if user has already seen it or has a subscription
  if (userState.hasSeenWelcomePaywall || userState.hasSubscription) {
    return false;
  }

  // Show to new users who haven't seen it yet
  return isNewUser(userEmail);
};

/**
 * Get user paywall state from localStorage
 */
export const getUserPaywallState = (userEmail: string): UserPaywallState => {
  const stored = localStorage.getItem(`paywall-state-${userEmail}`);
  if (stored) {
    return JSON.parse(stored);
  }

  // Default state for new users
  return {
    hasSeenWelcomePaywall: false,
    hasSubscription: false,
    signupDate: new Date().toISOString(),
    lastPaywallShown: null,
    paywallDismissCount: 0
  };
};

/**
 * Update user paywall state
 */
export const updateUserPaywallState = (userEmail: string, updates: Partial<UserPaywallState>): void => {
  const currentState = getUserPaywallState(userEmail);
  const newState = { ...currentState, ...updates };
  localStorage.setItem(`paywall-state-${userEmail}`, JSON.stringify(newState));
};

/**
 * Mark welcome paywall as seen
 */
export const markWelcomePaywallSeen = (userEmail: string): void => {
  updateUserPaywallState(userEmail, {
    hasSeenWelcomePaywall: true,
    lastPaywallShown: new Date().toISOString()
  });
};

// Direct subscription activation (no external payment links)

/**
 * Get free tier limits
 */
export const getFreeTierLimits = () => {
  return {
    topics: 3,
    recordingMinutes: 5,
    fileSizeMB: 5,
    aiGenerations: 10 // per day
  };
};

/**
 * Initialize user paywall state on first visit
 */
export const initializeUserPaywallState = (userEmail: string): void => {
  const existing = localStorage.getItem(`paywall-state-${userEmail}`);
  if (!existing) {
    const initialState: UserPaywallState = {
      hasSeenWelcomePaywall: false,
      hasSubscription: false,
      signupDate: new Date().toISOString(),
      lastPaywallShown: null,
      paywallDismissCount: 0
    };
    localStorage.setItem(`paywall-state-${userEmail}`, JSON.stringify(initialState));
  }
};
