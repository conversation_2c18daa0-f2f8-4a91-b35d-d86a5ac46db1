/**
 * Simple Payment Service - Direct Database Integration
 * No complex webhooks needed - handles payment success directly
 */

import { doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { UserSubscription } from './subscriptionService';

export interface PaymentRecord {
  userId: string;
  userEmail: string;
  sessionId: string;
  planType: 'monthly' | 'yearly';
  amount: number;
  status: 'completed' | 'pending' | 'failed';
  createdAt: any;
  subscription: UserSubscription;
}



/**
 * Get user subscription from database
 */
export const getUserSubscriptionFromDB = async (userId: string): Promise<UserSubscription | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (userDoc.exists()) {
      const userData = userDoc.data();
      return userData.subscription || null;
    }
    return null;
  } catch (error) {
    console.error('Error getting user subscription from DB:', error);
    return null;
  }
};

/**
 * Check if user has active subscription in database
 */
export const checkActiveSubscription = async (userId: string): Promise<boolean> => {
  try {
    const subscription = await getUserSubscriptionFromDB(userId);
    if (!subscription) return false;

    // Check if subscription is active and not expired
    const isActive = subscription.status === 'active';
    const notExpired = new Date(subscription.currentPeriodEnd) > new Date();
    
    return isActive && notExpired;
  } catch (error) {
    console.error('Error checking active subscription:', error);
    return false;
  }
};

/**
 * Sync user data between localStorage and database
 */
export const syncUserData = async (userId: string, userEmail: string): Promise<void> => {
  try {
    // Get subscription from database
    const dbSubscription = await getUserSubscriptionFromDB(userId);
    
    if (dbSubscription) {
      // Update localStorage with database data
      localStorage.setItem('user-subscription', JSON.stringify(dbSubscription));
      localStorage.setItem(`subscription-${userEmail}`, JSON.stringify(dbSubscription));
      console.log('✅ User data synced from database to localStorage');
    } else {
      // Check if user has subscription in localStorage but not in database
      const localSubscription = localStorage.getItem('user-subscription');
      if (localSubscription) {
        const subscription = JSON.parse(localSubscription) as UserSubscription;
        if (subscription.plan !== 'free') {
          // Save to database
          await setDoc(doc(db, 'users', userId), {
            email: userEmail,
            subscription: subscription,
            lastUpdated: serverTimestamp(),
          }, { merge: true });
          console.log('✅ User data synced from localStorage to database');
        }
      }
    }
  } catch (error) {
    console.error('Error syncing user data:', error);
  }
};

/**
 * Create Stripe checkout session with proper URLs
 */
export const createStripeCheckout = async (
  planType: 'monthly' | 'yearly',
  userEmail: string,
  successUrl?: string,
  cancelUrl?: string
): Promise<{ success: boolean; url?: string; error?: string }> => {
  try {
    console.log('🔄 Creating Stripe checkout for:', planType, userEmail);

    // Get current domain for URLs
    const currentDomain = 'https://ezmindai.com';
    const defaultSuccessUrl = `${currentDomain}/payment-success?session_id={CHECKOUT_SESSION_ID}&plan=${planType}`;
    const defaultCancelUrl = `${currentDomain}/pricing?cancelled=true`;

    // Use the correct price IDs
    const priceId = planType === 'yearly' 
      ? 'price_1RZEwUBV6Oovfe41AdJYCJ0K'
      : 'price_1RVw22BV6Oovfe41vGG9VqI3';

    // Create checkout session via Firebase function
    const { getFunctions, httpsCallable } = await import('firebase/functions');
    const { app } = await import('@/lib/firebase');
    const functions = getFunctions(app);
    const createCheckoutSession = httpsCallable(functions, 'createCheckoutSession');

    const result = await createCheckoutSession({
      priceId,
      planType,
      userEmail,
      successUrl: successUrl || defaultSuccessUrl,
      cancelUrl: cancelUrl || defaultCancelUrl
    });

    const data = result.data as any;
    
    if (data.success && data.url) {
      console.log('🔗 Redirecting to checkout URL:', data.url);
      window.location.href = data.url;
      return { success: true, url: data.url };
    } else {
      throw new Error(data.error || 'Failed to create checkout session');
    }
  } catch (error) {
    console.error('Error creating checkout:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};
