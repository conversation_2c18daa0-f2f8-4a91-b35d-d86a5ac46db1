/**
 * Stripe Service for handling payments and subscriptions
 */

export interface StripeConfig {
  publishableKey: string;
  products: {
    monthly: string;
    yearly: string;
  };
  paymentLinks: {
    monthly: string;
    yearly: string;
  };
}

// Stripe configuration - Production live keys
const STRIPE_CONFIG: StripeConfig = {
  publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_live_51R1kMLBV6Oovfe415qZa7OJam5CoiIQCgCZGfRtJ9h8yRf4siDUeqcF0WNH08zEey0NDVV2bE55qwr2qlHXh8EnN00o30VJrYc',
  products: {
    monthly: import.meta.env.VITE_STRIPE_MONTHLY_PRICE_ID || 'price_1RVw22BV6Oovfe41vGG9VqI3',
    yearly: import.meta.env.VITE_STRIPE_YEARLY_PRICE_ID || 'price_1RZEwUBV6Oovfe41AdJYCJ0K'
  },
  paymentLinks: {
    monthly: `https://checkout.stripe.com/pay/cs_live_${import.meta.env.VITE_STRIPE_MONTHLY_PRICE_ID}`,
    yearly: `https://checkout.stripe.com/pay/cs_live_${import.meta.env.VITE_STRIPE_YEARLY_PRICE_ID}`
  }
};

/**
 * Initialize Stripe
 */
export const initializeStripe = async () => {
  if (!window.Stripe) {
    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/';
    script.async = true;
    document.head.appendChild(script);

    return new Promise((resolve) => {
      script.onload = () => {
        resolve(window.Stripe(STRIPE_CONFIG.publishableKey));
      };
    });
  }

  return window.Stripe(STRIPE_CONFIG.publishableKey);
};

/**
 * Create Stripe checkout session - Direct redirect to payment links with proper URLs
 */
export const createCheckoutSession = async (
  planType: 'monthly' | 'yearly',
  userEmail?: string,
  successUrl?: string,
  cancelUrl?: string
) => {
  try {
    console.log('🔄 Redirecting to Stripe checkout for:', planType, userEmail);

    if (!userEmail) {
      throw new Error('User email is required for checkout');
    }

    // Import and use the simple payment service
    const { createStripeCheckout } = await import('./simplePaymentService');
    return createStripeCheckout(planType, userEmail);

  } catch (error) {
    console.error('Error creating checkout session:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Redirect to upgrade with proper checkout
 */
export const redirectToUpgrade = (planType: 'monthly' | 'yearly' = 'monthly') => {
  return createCheckoutSession(planType);
};

/**
 * Get pricing information
 */
export const getPricingInfo = () => {
  return {
    monthly: {
      price: 9.99,
      productId: STRIPE_CONFIG.products.monthly,
      paymentUrl: STRIPE_CONFIG.paymentLinks.monthly
    },
    yearly: {
      price: 7.99, // per month, billed annually
      annualPrice: 95.88,
      productId: STRIPE_CONFIG.products.yearly,
      paymentUrl: STRIPE_CONFIG.paymentLinks.yearly,
      savings: '20%'
    }
  };
};



/**
 * Check if user has active subscription
 */
export const hasActiveSubscription = (userEmail: string): boolean => {
  try {
    const subscription = localStorage.getItem(`subscription-${userEmail}`);
    if (!subscription) return false;
    
    const parsed = JSON.parse(subscription);
    return parsed.status === 'active' && new Date(parsed.currentPeriodEnd) > new Date();
  } catch {
    return false;
  }
};

export default {
  initializeStripe,
  createCheckoutSession,
  redirectToUpgrade,
  getPricingInfo,
  handlePaymentSuccess,
  hasActiveSubscription,
  STRIPE_CONFIG
};
