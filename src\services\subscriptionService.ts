// Subscription and usage management service
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, limit as firestoreLimit } from 'firebase/firestore';

export interface UserSubscription {
  planType?: 'monthly' | 'yearly'; // Added to align with PaymentSuccess.tsx and webhook data
  plan: 'free' | 'basic' | 'premium';
  status: 'active' | 'canceled' | 'past_due' | 'trialing';
  currentPeriodEnd: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  trialEnd?: string;
}

export interface UsageLimits {
  aiQuestions: number;
  fileUploads: number;
  audioTranscriptions: number;
  libraryStorage: number; // in MB
  mathExpert: boolean;
  liveRecording: boolean;
  multipleFiles: boolean;
  imageAnalysis: boolean;
}

export interface UserUsage {
  aiQuestions: number;
  fileUploads: number;
  audioTranscriptions: number;
  libraryStorage: number; // in MB
  resetDate: string; // Monthly reset
}

// Plan limits configuration
const PLAN_LIMITS: Record<string, UsageLimits> = {
  free: {
    aiQuestions: 5, // Very limited for free tier
    fileUploads: 3, // Maximum 3 study topics
    audioTranscriptions: 1, // 5 minutes total recording time
    libraryStorage: 5, // 5MB storage limit
    mathExpert: false,
    liveRecording: false,
    multipleFiles: false,
    imageAnalysis: false
  },
  basic: {
    aiQuestions: -1, // Unlimited
    fileUploads: -1, // Unlimited
    audioTranscriptions: -1, // Unlimited
    libraryStorage: -1, // Unlimited
    mathExpert: false,
    liveRecording: false,
    multipleFiles: false,
    imageAnalysis: false
  },

  premium: {
    aiQuestions: -1, // Unlimited
    fileUploads: -1, // Unlimited
    audioTranscriptions: -1, // Unlimited
    libraryStorage: -1, // Unlimited
    mathExpert: true,
    liveRecording: true,
    multipleFiles: true,
    imageAnalysis: true
  }
};

/**
 * Get user subscription from localStorage (with Firestore fallback)
 */
export const getUserSubscription = async (uid?: string): Promise<UserSubscription> => {
  // First try localStorage
  const stored = localStorage.getItem('user-subscription');
  if (stored) {
    return JSON.parse(stored);
  }

  // If no localStorage data and we have a user ID, try Firestore
  if (uid) {
    try {
      const { getUserSubscriptionFromFirestore } = await import('@/services/firestoreService');
      const firestoreSubscription = await getUserSubscriptionFromFirestore(uid);
      if (firestoreSubscription) {
        // Update localStorage with Firestore data
        localStorage.setItem('user-subscription', JSON.stringify(firestoreSubscription));
        return firestoreSubscription;
      }
    } catch (error) {
      console.error('Error getting subscription from Firestore:', error);
    }
  }

  // Default free plan
  return {
    plan: 'free',
    status: 'active',
    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
  };
};

/**
 * Get user subscription synchronously (localStorage only)
 */
export const getUserSubscriptionSync = (): UserSubscription => {
  const stored = localStorage.getItem('user-subscription');
  if (stored) {
    return JSON.parse(stored);
  }

  // Default free plan
  return {
    plan: 'free',
    status: 'active',
    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
  };
};

/**
 * Update user subscription
 */
export const updateUserSubscription = (subscription: UserSubscription): void => {
  localStorage.setItem('user-subscription', JSON.stringify(subscription));
};

/**
 * Get user usage from localStorage
 */
export const getUserUsage = (): UserUsage => {
  const stored = localStorage.getItem('user-usage');
  if (stored) {
    const usage = JSON.parse(stored);

    // Check if we need to reset monthly usage
    const resetDate = new Date(usage.resetDate);
    const now = new Date();
    const nextReset = new Date(resetDate.getFullYear(), resetDate.getMonth() + 1, resetDate.getDate());

    if (now >= nextReset) {
      // Reset usage for new month
      const newUsage: UserUsage = {
        aiQuestions: 0,
        fileUploads: 0,
        audioTranscriptions: 0,
        libraryStorage: 0,
        resetDate: nextReset.toISOString()
      };
      updateUserUsage(newUsage);
      return newUsage;
    }

    return usage;
  }

  // Default usage
  const defaultUsage: UserUsage = {
    aiQuestions: 0,
    fileUploads: 0,
    audioTranscriptions: 0,
    libraryStorage: 0,
    resetDate: new Date().toISOString()
  };
  updateUserUsage(defaultUsage);
  return defaultUsage;
};

/**
 * Update user usage
 */
export const updateUserUsage = (usage: UserUsage): void => {
  localStorage.setItem('user-usage', JSON.stringify(usage));
};

/**
 * Get plan limits for a specific plan
 */
export const getPlanLimits = (plan: string): UsageLimits => {
  return PLAN_LIMITS[plan] || PLAN_LIMITS.free;
};

/**
 * Get user subscription by session ID from Firestore (optimized)
 */
export const getSubscriptionBySessionId = async (sessionId: string, userId: string): Promise<UserSubscription | null> => {
  if (!sessionId || !userId) {
    console.error('Session ID and User ID are required to fetch subscription by session ID');
    return null;
  }
  
  try {
    console.log(`🔍 Fetching subscription for session: ${sessionId}, user: ${userId}`);
    
    // First, try to get subscription directly from user document (fastest approach)
    const { getUserSubscriptionFromFirestore } = await import('@/services/firestoreService');
    const userSubscription = await getUserSubscriptionFromFirestore(userId);
    
    if (userSubscription && userSubscription.status === 'active') {
      console.log(`✅ Active subscription found for user ${userId}`);
      // Update localStorage immediately for faster future access
      localStorage.setItem('user-subscription', JSON.stringify(userSubscription));
      localStorage.setItem(`subscription-${userId}`, JSON.stringify(userSubscription));
      return userSubscription as UserSubscription;
    }
    
    // If no active subscription found, verify payment was processed
    console.log(`🔍 No active subscription found, checking payment record...`);
    const paymentsQuery = query(
      collection(db, 'payments'),
      where('sessionId', '==', sessionId),
      where('userId', '==', userId),
      firestoreLimit(1)
    );
    
    const paymentSnap = await getDocs(paymentsQuery);
    
    if (paymentSnap.empty) {
      console.warn(`❌ No payment record found for session ${sessionId}`);
      return null;
    }
    
    // Payment exists but subscription might not be updated yet
    console.log(`💳 Payment record found, but subscription not yet active. This may indicate webhook processing delay.`);
    
    // Return the user subscription even if not active, as it might be processing
    if (userSubscription) {
      console.log(`📋 Returning subscription in processing state`);
      localStorage.setItem('user-subscription', JSON.stringify(userSubscription));
      localStorage.setItem(`subscription-${userId}`, JSON.stringify(userSubscription));
      return userSubscription as UserSubscription;
    }
    
    return null;
    
  } catch (error) {
    console.error('❌ Error getting subscription by session ID:', error);
    throw error; // Re-throw to allow retry logic in PaymentSuccess
  }
};

/**
 * Check if user can perform an action based on their plan and usage
 */
export const canPerformAction = (action: keyof UserUsage, userEmail?: string): boolean => {
  const subscription = getUserSubscriptionSync(); // Use sync version for immediate checks
  const usage = getUserUsage();
  const limits = getPlanLimits(subscription.plan);

  // Admin always has access
  if (userEmail === '<EMAIL>') {
    return true;
  }

  // Check if plan is active
  if (subscription.status !== 'active' && subscription.status !== 'trialing') {
    return false;
  }

  // Check specific limits
  const limit = limits[action as keyof UsageLimits];
  if (typeof limit === 'number') {
    if (limit === -1) return true; // Unlimited
    return usage[action] < limit;
  }

  return false;
};

/**
 * Check if user has access to a feature
 */
export const hasFeatureAccess = (feature: keyof UsageLimits, userEmail?: string): boolean => {
  const subscription = getUserSubscriptionSync(); // Use sync version for immediate checks
  const limits = getPlanLimits(subscription.plan);

  // Admin always has access
  if (userEmail === '<EMAIL>') {
    return true;
  }

  // Check if plan is active
  if (subscription.status !== 'active' && subscription.status !== 'trialing') {
    return false;
  }

  return limits[feature] === true || limits[feature] === -1;
};

/**
 * Increment usage for an action
 */
export const incrementUsage = (action: keyof UserUsage, amount: number = 1): void => {
  const usage = getUserUsage();
  usage[action] += amount;
  updateUserUsage(usage);
};

/**
 * Get usage percentage for display
 */
export const getUsagePercentage = (action: keyof UserUsage): number => {
  const subscription = getUserSubscription();
  const usage = getUserUsage();
  const limits = getPlanLimits(subscription.plan);

  const limit = limits[action as keyof UsageLimits];
  if (typeof limit === 'number') {
    if (limit === -1) return 0; // Unlimited
    return Math.min((usage[action] / limit) * 100, 100);
  }

  return 0;
};

/**
 * Get remaining usage for an action
 */
export const getRemainingUsage = (action: keyof UserUsage): number | string => {
  const subscription = getUserSubscription();
  const usage = getUserUsage();
  const limits = getPlanLimits(subscription.plan);

  const limit = limits[action as keyof UsageLimits];
  if (typeof limit === 'number') {
    if (limit === -1) return 'Unlimited';
    return Math.max(limit - usage[action], 0);
  }

  return 0;
};

/**
 * Initialize Stripe for payments
 */
export const initializeStripe = async () => {
  const stripeKey = 'pk_live_51R1kMLBV6Oovfe41rGvQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQJxQ'; // Live key

  // Load Stripe script if not already loaded
  if (!window.Stripe) {
    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/';
    script.async = true;
    document.head.appendChild(script);

    return new Promise((resolve) => {
      script.onload = () => {
        resolve(window.Stripe(stripeKey));
      };
    });
  }

  return window.Stripe(stripeKey);
};

/**
 * Redirect to pricing page for direct subscription activation
 */
export const redirectToUpgrade = (planType: 'monthly' | 'yearly' = 'monthly', userEmail: string): void => {
  console.log('🔗 Redirecting to pricing page for direct activation:', planType);
  
  // Redirect to pricing page for direct activation
  window.location.href = '/pricing';
};
