// SIMPLE WORKING VERSION - DIRECT API CALLS
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY || "AIzaSyBd5ImRFOeTFhAQUgBVjhsTkFHsHmelbmI";
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent";

/**
 * Generate content using direct Gemini API - WORKING VERSION
 */
export const generateContent = async (prompt: string): Promise<string> => {
  try {
    console.log('🚀 Generating content with Gemini API...');

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_NONE"
          }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates.length > 0) {
      const text = data.candidates[0].content.parts[0].text;
      console.log('✅ Content generated successfully');
      return text;
    } else {
      throw new Error('No content generated');
    }
  } catch (error) {
    console.error('❌ Error calling Gemini API:', error);
    throw new Error('Failed to generate content. Please try again.');
  }
};

/**
 * Generate content with streaming response - SIMPLIFIED
 */
export const generateContentStream = async (
  prompt: string,
  onChunk?: (chunk: string) => void
): Promise<string> => {
  // For now, just use regular generation and call onChunk with full response
  const result = await generateContent(prompt);
  if (onChunk) {
    onChunk(result);
  }
  return result;
};

/**
 * Generate study notes
 */
export const generateNotes = async (content: string, level: string): Promise<string> => {
  // Check if content is a simple topic or full content
  const isFullContent = content.length > 200 || content.includes('\n') || content.includes('#');

  let prompt = '';

  if (isFullContent) {
    prompt = `Based on the following content, create comprehensive study notes at ${level} level:

${content}

Create well-structured notes with:
1. Introduction and overview
2. Key concepts and definitions
3. Important facts and details
4. Examples and applications
5. Summary and key takeaways

Make the notes clear, well-organized, and appropriate for the ${level} level. Use bullet points, headings, and clear formatting.`;
  } else {
    prompt = `Create comprehensive study notes for the topic "${content}" at ${level} level.

Structure the notes with:
1. Introduction and overview
2. Key concepts and definitions
3. Important facts and details
4. Examples and applications
5. Summary and key takeaways

Make the notes clear, well-organized, and appropriate for the ${level} level. Use bullet points, headings, and clear formatting.`;
  }

  return generateContent(prompt);
};

/**
 * Generate quiz questions
 */
export const generateQuiz = async (content: string, difficulty: string, questionCount: number): Promise<string> => {
  // Check if content is a simple topic or full content
  const isFullContent = content.length > 200 || content.includes('\n') || content.includes('#');

  let prompt = '';

  if (isFullContent) {
    prompt = `Based on the following content, create a ${difficulty} level quiz with exactly ${questionCount} multiple choice questions:

${content}

Format each question as follows:
Q1: [Question text]
A) [Option A]
B) [Option B]
C) [Option C]
D) [Option D]
Correct Answer: [Letter]
Explanation: [Brief explanation of why this answer is correct]

Make sure the questions test understanding of key concepts from the content and are appropriate for the ${difficulty} difficulty level.`;
  } else {
    prompt = `Create a ${difficulty} level quiz about "${content}" with exactly ${questionCount} multiple choice questions.

Format each question as follows:
Q1: [Question text]
A) [Option A]
B) [Option B]
C) [Option C]
D) [Option D]
Correct Answer: [Letter]
Explanation: [Brief explanation of why this answer is correct]

Make sure the questions test understanding of key concepts and are appropriate for the ${difficulty} difficulty level.`;
  }

  return generateContent(prompt);
};

/**
 * Generate flashcards
 */
export const generateFlashcards = async (content: string, count: number): Promise<string> => {
  // Check if content is a simple topic or full content
  const isFullContent = content.length > 200 || content.includes('\n') || content.includes('#');

  let prompt = '';

  if (isFullContent) {
    prompt = `Based on the following content, create ${count} flashcards for studying:

${content}

Format each flashcard as:
Card 1:
Front: [Question or term]
Back: [Answer or definition]

Make sure the flashcards cover the most important concepts, terms, and facts from the content.
Keep the front side concise and the back side informative but not too lengthy.`;
  } else {
    prompt = `Create ${count} flashcards for studying "${content}".

Format each flashcard as:
Card 1:
Front: [Question or term]
Back: [Answer or definition]

Make sure the flashcards cover the most important concepts, terms, and facts about ${content}.
Keep the front side concise and the back side informative but not too lengthy.`;
  }

  return generateContent(prompt);
};

/**
 * Process audio transcript
 */
export const processAudioTranscript = async (transcript: string, fileName: string): Promise<string> => {
  const prompt = `Process the following audio transcript and create comprehensive study material:

Audio File: ${fileName}
Transcript:
${transcript}

Please provide:
1. A clean, formatted version of the transcript
2. Key topics and concepts covered
3. Important definitions and terms
4. Main takeaways and learning objectives
5. Any action items or assignments mentioned

Format the response in a clear, study-friendly manner with proper headings and bullet points.`;

  return generateContent(prompt);
};

/**
 * Generate content from audio transcript
 */
export const generateFromAudio = async (
  transcript: string,
  contentType: 'notes' | 'summary' | 'quiz' | 'flashcards',
  options?: { difficulty?: string; count?: number }
): Promise<string> => {
  let prompt = '';

  switch (contentType) {
    case 'notes':
      prompt = `Based on the following audio transcript, generate comprehensive study notes:

${transcript}

Create well-structured notes with:
- Key concepts and main ideas
- Important definitions and terms
- Examples and explanations
- Summary of main points
- Action items or assignments mentioned

Format with clear headings and bullet points.`;
      break;

    case 'summary':
      prompt = `Create a concise summary of the following audio transcript:

${transcript}

Provide:
- Main topics covered
- Key takeaways
- Important points to remember
- Any conclusions or next steps mentioned

Keep it concise but comprehensive.`;
      break;

    case 'quiz':
      const difficulty = options?.difficulty || 'medium';
      prompt = `Create a ${difficulty} level quiz based on this audio transcript:

${transcript}

Format each question as:
Q1: [Question text]
A) [Option A]
B) [Option B]
C) [Option C]
D) [Option D]
Correct Answer: [Letter]
Explanation: [Brief explanation]`;
      break;

    case 'flashcards':
      const cardCount = options?.count || 10;
      prompt = `Create ${cardCount} flashcards based on this audio transcript:

${transcript}

Format each flashcard as:
Card 1:
Front: [Question or term]
Back: [Answer or definition]

Focus on key concepts, terms, and important facts from the audio.`;
      break;
  }

  return generateContent(prompt);
};

/**
 * Process file content and generate study material
 */
export const processFileContent = async (
  content: string,
  fileName: string,
  contentType: 'notes' | 'summary' | 'quiz' | 'flashcards' = 'notes'
): Promise<string> => {
  const prompt = `Process the following file content and generate ${contentType}:

File: ${fileName}
Content:
${content}

Please create comprehensive ${contentType} based on this content. Make it well-structured and educational.`;

  return generateContent(prompt);
};

/**
 * Transcribe audio using Gemini's native audio processing capabilities
 * This uses the same API as the official Gemini website
 */
export const transcribeAudio = async (audioBase64: string, mimeType: string): Promise<string> => {
  try {
    console.log('🎵 Transcribing audio with Gemini native audio processing...');

    // Use Gemini's multimodal capabilities for audio processing
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: "Please transcribe this audio file accurately. Provide ONLY the clean transcription text without any commentary, timestamps, or additional formatting. Just the spoken words in plain text format."
          }, {
            inline_data: {
              mime_type: mimeType,
              data: audioBase64
            }
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 1,
          topP: 0.8,
          maxOutputTokens: 4096,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_NONE"
          }
        ]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Gemini audio API error:', errorText);
      throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('🎵 Gemini audio response received');

    if (data.candidates && data.candidates.length > 0) {
      const transcript = data.candidates[0].content.parts[0].text.trim();

      if (transcript.length > 0) {
        console.log('✅ Audio transcribed successfully with Gemini, length:', transcript.length);
        return transcript;
      }
    }

    throw new Error('No transcription results from Gemini API');

  } catch (error) {
    console.error('❌ Error transcribing audio with Gemini:', error);
    throw new Error(`Audio transcription failed: ${error.message}`);
  }
};

export default {
  generateContent,
  generateContentStream,
  generateNotes,
  generateQuiz,
  generateFlashcards,
  processAudioTranscript,
  generateFromAudio,
  processFileContent,
  transcribeAudio
};
