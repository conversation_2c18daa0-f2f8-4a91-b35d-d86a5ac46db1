// Website processing service for handling URL content extraction
export interface WebsiteProcessingResult {
  content: string;
  title: string;
  url: string;
  description?: string;
  metadata?: {
    type: string;
    domain: string;
    extractedSuccessfully: boolean;
    wordCount: number;
  };
}

/**
 * Validate if a string is a valid URL
 */
export const isValidUrl = (urlString: string): boolean => {
  try {
    const url = new URL(urlString);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch {
    return false;
  }
};

/**
 * Clean and format URL for processing
 */
export const cleanUrl = (urlString: string): string => {
  let cleanedUrl = urlString.trim();

  // Add https:// if no protocol is specified
  if (!cleanedUrl.startsWith('http://') && !cleanedUrl.startsWith('https://')) {
    cleanedUrl = 'https://' + cleanedUrl;
  }

  return cleanedUrl;
};

/**
 * Extract text content from HTML with advanced content detection
 */
const extractTextFromHtml = (html: string): string => {
  console.log('🔍 Starting HTML text extraction...');

  // Create a temporary DOM element to parse HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  console.log('📄 HTML content length:', html.length);

  // Remove unwanted elements more comprehensively
  const unwantedSelectors = [
    'script', 'style', 'nav', 'header', 'footer', 'aside', 'noscript',
    '.navigation', '.nav', '.menu', '.sidebar', '.ads', '.advertisement',
    '.social', '.share', '.comments', '.related', '.recommended',
    '[class*="nav"]', '[class*="menu"]', '[class*="sidebar"]',
    '[class*="ad"]', '[class*="social"]', '[class*="share"]',
    '.cookie', '.popup', '.modal', '.overlay', '.banner',
    '.breadcrumb', '.breadcrumbs', '.tags', '.tag-list',
    '.author-bio', '.author-info', '.meta', '.metadata',
    '.newsletter', '.subscription', '.signup', '.login',
    '.search', '.search-form', '.search-box',
    '.widget', '.widgets', '.plugin', '.embed'
  ];

  let removedCount = 0;
  unwantedSelectors.forEach(selector => {
    try {
      const elements = tempDiv.querySelectorAll(selector);
      removedCount += elements.length;
      elements.forEach(el => el.remove());
    } catch (e) {
      // Ignore selector errors
    }
  });

  console.log('🗑️ Removed', removedCount, 'unwanted elements');

  // Enhanced content selectors for better content detection
  const contentSelectors = [
    // Primary content areas
    'main', 'article', '[role="main"]',

    // Common content classes
    '.content', '.main-content', '.post-content', '.entry-content',
    '.article-content', '.page-content', '.text-content', '.body-content',
    '.post-body', '.article-body', '.content-body', '.story-body',

    // CMS-specific selectors
    '.mw-parser-output', // Wikipedia
    '.post', '.entry', '.story', '.article-text',
    '.content-area', '.main-area', '.primary-content',

    // Blog and news specific
    '.post-text', '.article-text', '.story-text',
    '.content-wrapper', '.text-wrapper', '.article-wrapper',

    // Generic containers that might contain content
    '.container .content', '.wrapper .content',
    '#content', '#main-content', '#article-content'
  ];

  let mainContent = '';
  let bestSelector = '';

  for (const selector of contentSelectors) {
    try {
      const contentEl = tempDiv.querySelector(selector);
      if (contentEl && contentEl.textContent) {
        const textLength = contentEl.textContent.trim().length;
        console.log(`📝 Found content with selector "${selector}": ${textLength} characters`);

        // Use content if it's substantial (lowered threshold for better detection)
        if (textLength > 300 && textLength > mainContent.length) {
          mainContent = contentEl.textContent;
          bestSelector = selector;
        }
      }
    } catch (e) {
      // Ignore selector errors
    }
  }

  console.log('🎯 Best content selector:', bestSelector || 'none found');

  // Enhanced fallback strategy
  if (!mainContent || mainContent.length < 300) {
    console.log('⚠️ Main content too short, trying fallback methods...');

    // Try to find paragraphs with substantial content
    const paragraphs = tempDiv.querySelectorAll('p');
    let paragraphContent = '';

    paragraphs.forEach(p => {
      const text = p.textContent?.trim();
      if (text && text.length > 50) {
        paragraphContent += text + '\n\n';
      }
    });

    if (paragraphContent.length > mainContent.length) {
      mainContent = paragraphContent;
      console.log('📝 Used paragraph extraction:', paragraphContent.length, 'characters');
    }

    // Final fallback to body content
    if (!mainContent || mainContent.length < 200) {
      const bodyContent = tempDiv.textContent || tempDiv.innerText || '';
      if (bodyContent.length > mainContent.length) {
        mainContent = bodyContent;
        console.log('📝 Used body content as final fallback:', bodyContent.length, 'characters');
      }
    }
  }

  // Enhanced text cleaning
  let textContent = mainContent
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove excessive line breaks
    .replace(/\t/g, ' ') // Replace tabs with spaces
    .replace(/[\r\n]+/g, '\n') // Normalize line endings
    .trim();

  // Remove common website noise patterns
  const noisePatterns = [
    /Cookie Policy|Privacy Policy|Terms of Service|Terms & Conditions/gi,
    /Subscribe to our newsletter|Sign up for updates|Join our mailing list/gi,
    /Follow us on|Connect with us|Find us on/gi,
    /Share this article|Share on|Tweet this|Like this/gi,
    /Read more|Continue reading|View full article/gi,
    /Advertisement|Sponsored content|Promoted content/gi,
    /Skip to main content|Skip navigation|Skip to content/gi,
    /Loading\.\.\.|Please wait|Loading content/gi,
    /\b(Home|About|Contact|FAQ|Help|Support|Login|Register|Sign up|Sign in)\b/gi
  ];

  noisePatterns.forEach(pattern => {
    textContent = textContent.replace(pattern, '');
  });

  // Remove excessive punctuation and clean up
  textContent = textContent
    .replace(/\.{3,}/g, '...') // Normalize ellipsis
    .replace(/!{2,}/g, '!') // Normalize exclamation marks
    .replace(/\?{2,}/g, '?') // Normalize question marks
    .replace(/\s+/g, ' ') // Final whitespace cleanup
    .trim();

  console.log('✅ Final extracted text length:', textContent.length);

  return textContent;
};

/**
 * Extract metadata from HTML with enhanced detection
 */
const extractMetadata = (html: string, url: string) => {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  console.log('📋 Extracting metadata from HTML...');

  // Extract title with multiple fallbacks
  let title = '';

  // Try multiple title sources
  const titleSources = [
    () => tempDiv.querySelector('title')?.textContent?.trim(),
    () => tempDiv.querySelector('meta[property="og:title"]')?.getAttribute('content')?.trim(),
    () => tempDiv.querySelector('meta[name="twitter:title"]')?.getAttribute('content')?.trim(),
    () => tempDiv.querySelector('h1')?.textContent?.trim(),
    () => tempDiv.querySelector('.title')?.textContent?.trim(),
    () => tempDiv.querySelector('#title')?.textContent?.trim()
  ];

  for (const source of titleSources) {
    try {
      const extractedTitle = source();
      if (extractedTitle && extractedTitle.length > 0 && extractedTitle.length < 200) {
        title = extractedTitle;
        break;
      }
    } catch (e) {
      // Continue to next source
    }
  }

  // Fallback to domain if no title found
  if (!title) {
    title = new URL(url).hostname;
  }

  // Clean up title
  title = title
    .replace(/\s+/g, ' ')
    .replace(/\|.*$/, '') // Remove site name after pipe
    .replace(/\-.*$/, '') // Remove site name after dash (be careful with this)
    .trim();

  console.log('📋 Extracted title:', title);

  // Extract description with multiple fallbacks
  let description = '';

  const descriptionSources = [
    () => tempDiv.querySelector('meta[name="description"]')?.getAttribute('content')?.trim(),
    () => tempDiv.querySelector('meta[property="og:description"]')?.getAttribute('content')?.trim(),
    () => tempDiv.querySelector('meta[name="twitter:description"]')?.getAttribute('content')?.trim(),
    () => tempDiv.querySelector('.description')?.textContent?.trim(),
    () => tempDiv.querySelector('.excerpt')?.textContent?.trim(),
    () => tempDiv.querySelector('.summary')?.textContent?.trim()
  ];

  for (const source of descriptionSources) {
    try {
      const extractedDesc = source();
      if (extractedDesc && extractedDesc.length > 10 && extractedDesc.length < 500) {
        description = extractedDesc;
        break;
      }
    } catch (e) {
      // Continue to next source
    }
  }

  console.log('📋 Extracted description length:', description.length);

  return { title, description };
};

/**
 * Format extracted content for better readability
 */
const formatExtractedContent = (text: string, title: string, url: string): string => {
  // Clean up the text
  let cleanedText = text
    .replace(/\s+/g, ' ')
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim();

  // Limit content length to avoid overwhelming the AI (increased limit for better content)
  if (cleanedText.length > 8000) {
    cleanedText = cleanedText.substring(0, 8000) + '...';
  }

  // Format as structured content optimized for AI processing
  return `# ${title}

**Source:** ${url}
**Extracted:** ${new Date().toLocaleString()}

## Main Content

${cleanedText}

## Study Notes
This content was extracted from a website and is ready for AI-powered study material generation. The content above contains the key information from the webpage that can be used to create notes, summaries, quizzes, and flashcards.

---
*Content extracted from website for study purposes*`;
};

/**
 * Generate content using AI when direct extraction fails
 */
const generateContentFromUrl = async (url: string, domain: string): Promise<string> => {
  try {
    console.log('🤖 Generating content with AI for:', url);

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-001:generateContent?key=AIzaSyBd5ImRFOeTFhAQUgBVjhsTkFHsHmelbmI`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `Analyze this URL: ${url} and create comprehensive educational content about the topic this website covers.

Please provide:
1. **Main Topic Overview** - What is this website about?
2. **Key Concepts** - Important terms and ideas
3. **Core Information** - Essential facts and details
4. **Learning Objectives** - What should students understand?
5. **Study Points** - Specific items to focus on
6. **Context & Background** - Why is this topic important?

Make the content educational, well-structured, and suitable for study purposes. If this is a well-known website (like Wikipedia, educational institutions, news sites), provide accurate information about the specific topic. Focus on creating valuable learning material rather than generic descriptions.`
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          topK: 32,
          topP: 0.8,
          maxOutputTokens: 2048,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_NONE"
          }
        ]
      })
    });

    if (response.ok) {
      const data = await response.json();
      const aiContent = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (aiContent) {
        const generatedTitle = `Educational Content: ${domain}`;
        return `# ${generatedTitle}

**Source URL:** ${url}
**Generated:** ${new Date().toLocaleString()}

## Educational Content

${aiContent}

## Study Guide
This educational content was generated by AI based on the website URL and is designed for study purposes. The information above covers the key concepts and learning objectives related to this topic.

---
*This content was generated by AI based on the website URL. For the most accurate information, please visit the original website.*`;
      }
    }

    throw new Error('AI content generation failed');

  } catch (error) {
    console.error('❌ AI content generation failed:', error);

    // Fallback to basic structured content
    return `# Website Reference: ${domain}

**URL:** ${url}
**Processed:** ${new Date().toLocaleString()}

## About This Website

This is a reference to content from ${domain}. While automatic content extraction was not possible due to technical limitations, you can still use this as a starting point for your studies.

## Suggested Study Approach

1. **Visit the Website:** Go to ${url} to read the original content
2. **Take Notes:** Copy important information you want to study
3. **Create Materials:** Use the AI tools to generate study materials based on the content
4. **Organize Learning:** Structure your notes into flashcards, summaries, and quizzes

## Next Steps

- Visit the website to gather the specific content you want to study
- Copy and paste relevant text into a new topic for AI processing
- Use the AI tools to create comprehensive study materials

*This reference helps you organize your learning from web-based sources.*`;
  }
};

/**
 * Process website URL and extract content
 */
export const processWebsiteUrl = async (urlString: string): Promise<WebsiteProcessingResult> => {
  try {
    console.log('🌐 Processing website URL:', urlString);

    const cleanedUrl = cleanUrl(urlString);

    if (!isValidUrl(cleanedUrl)) {
      throw new Error('Invalid URL format');
    }

    console.log('🌐 Cleaned URL:', cleanedUrl);

    // Try multiple methods to extract content
    let content = '';
    let title = '';
    let description = '';
    const domain = new URL(cleanedUrl).hostname;

    // Method 1: Try multiple proxy services for better reliability
    const proxyServices = [
      {
        name: 'AllOrigins',
        url: `https://api.allorigins.win/get?url=${encodeURIComponent(cleanedUrl)}`,
        extractContent: (data: any) => data.contents
      },
      {
        name: 'CORS Anywhere (Heroku)',
        url: `https://cors-anywhere.herokuapp.com/${cleanedUrl}`,
        extractContent: (data: any) => data
      },
      {
        name: 'ThingProxy',
        url: `https://thingproxy.freeboard.io/fetch/${cleanedUrl}`,
        extractContent: (data: any) => data
      }
    ];

    for (const proxy of proxyServices) {
      try {
        console.log(`🔍 Attempting content extraction via ${proxy.name}...`);
        const response = await fetch(proxy.url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        });

        console.log(`📡 ${proxy.name} response status:`, response.status);

        if (response.ok) {
          const data = proxy.name === 'AllOrigins' ? await response.json() : await response.text();
          const htmlContent = proxy.extractContent(data);

          console.log(`📄 ${proxy.name} HTML content length:`, htmlContent?.length || 0);

          if (htmlContent && htmlContent.length > 100) {
            // Extract metadata
            const metadata = extractMetadata(htmlContent, cleanedUrl);
            title = metadata.title;
            description = metadata.description;

            console.log(`📋 Extracted metadata - Title: "${title}", Description length: ${description.length}`);

            // Extract text content
            const extractedText = extractTextFromHtml(htmlContent);

            console.log(`📝 Extracted text length: ${extractedText?.length || 0}`);

            if (extractedText && extractedText.length > 200) {
              // Clean and format the content
              content = formatExtractedContent(extractedText, title, cleanedUrl);
              console.log(`✅ Successfully extracted content via ${proxy.name}`);
              break; // Success, exit the loop
            } else {
              console.log(`⚠️ ${proxy.name} extracted text too short or empty`);
            }
          } else {
            console.log(`⚠️ ${proxy.name} returned insufficient HTML content`);
          }
        } else {
          console.log(`❌ ${proxy.name} request failed with status:`, response.status);
        }
      } catch (proxyError) {
        console.log(`❌ ${proxy.name} extraction failed:`, proxyError.message);
      }
    }

    // Method 2: If proxy fails, try direct fetch (may fail due to CORS)
    if (!content) {
      try {
        console.log('🔍 Attempting direct content extraction...');
        const directResponse = await fetch(cleanedUrl, {
          mode: 'cors',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Cache-Control': 'no-cache'
          }
        });

        console.log('📡 Direct fetch response status:', directResponse.status);

        if (directResponse.ok) {
          const htmlContent = await directResponse.text();
          console.log('📄 Direct fetch HTML content length:', htmlContent.length);

          const metadata = extractMetadata(htmlContent, cleanedUrl);
          title = metadata.title;
          description = metadata.description;

          console.log(`📋 Direct fetch metadata - Title: "${title}", Description length: ${description.length}`);

          const extractedText = extractTextFromHtml(htmlContent);
          console.log('📝 Direct fetch extracted text length:', extractedText?.length || 0);

          if (extractedText && extractedText.length > 200) {
            content = formatExtractedContent(extractedText, title, cleanedUrl);
            console.log('✅ Successfully extracted content directly');
          } else {
            console.log('⚠️ Direct fetch extracted text too short or empty');
          }
        } else {
          console.log('❌ Direct fetch failed with status:', directResponse.status);
        }
      } catch (corsError) {
        console.log('❌ Direct extraction failed due to CORS restrictions:', corsError.message);
      }
    }

    // Method 3: Use AI to generate content based on URL if extraction fails
    if (!content) {
      console.log('🤖 All extraction methods failed, using AI to generate content based on URL...');
      content = await generateContentFromUrl(cleanedUrl, domain);
      title = title || `Website: ${domain}`;
    }

    // Validate content quality
    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
    const hasSubstantialContent = wordCount > 50 && content.length > 300;

    console.log('📊 Content validation results:', {
      title,
      domain,
      wordCount,
      contentLength: content.length,
      hasSubstantialContent,
      extractionMethod: content.includes('AI-powered study material generation') ? 'direct extraction' :
                       content.includes('generated by AI') ? 'AI generation' : 'unknown'
    });

    if (!hasSubstantialContent) {
      console.warn('⚠️ Warning: Extracted content may be insufficient for quality study materials');
    }

    console.log('✅ Website URL processed successfully');

    return {
      content: content,
      title,
      url: cleanedUrl,
      description: `Website reference for ${domain}`,
      metadata: {
        type: 'website',
        domain,
        extractedSuccessfully: true,
        wordCount
      }
    };

  } catch (error) {
    console.error('❌ Error processing website:', error);
    throw new Error(`Failed to process website: ${error.message}`);
  }
};

/**
 * Check if a string looks like a URL
 */
export const looksLikeUrl = (input: string): boolean => {
  const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;
  return urlPattern.test(input.trim());
};

/**
 * Get domain from URL for display
 */
export const getDomainFromUrl = (url: string): string => {
  try {
    return new URL(cleanUrl(url)).hostname;
  } catch {
    return url;
  }
};

/**
 * Generate website preview content for display
 */
export const generateWebsitePreview = (result: WebsiteProcessingResult): string => {
  return `🌐 **${result.title}**

📍 **URL:** ${result.url}
🏷️ **Domain:** ${result.metadata?.domain}
${result.description ? `📝 **Description:** ${result.description}` : ''}
📊 **Word Count:** ${result.metadata?.wordCount || 0}

**Content Preview:**
${result.content.substring(0, 500)}${result.content.length > 500 ? '...' : ''}

*Ready for AI processing*`;
};
