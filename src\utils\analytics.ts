import { track } from '@vercel/analytics';

/**
 * Custom analytics tracking for EZMind AI
 * Track user interactions and business metrics
 */

// Analytics configuration
const ANALYTICS_ENABLED = import.meta.env.VITE_ENABLE_ANALYTICS !== 'false';
const ANALYTICS_DEBUG = import.meta.env.VITE_ANALYTICS_DEBUG === 'true';

// Helper function to track events with environment checks
const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  if (!ANALYTICS_ENABLED) {
    if (ANALYTICS_DEBUG) {
      console.log(`[Analytics Disabled] ${eventName}:`, properties);
    }
    return;
  }

  if (ANALYTICS_DEBUG) {
    console.log(`[Analytics] ${eventName}:`, properties);
  }

  try {
    track(eventName, properties);
  } catch (error) {
    console.error('Analytics tracking error:', error);
  }
};

// User Authentication Events
export const trackSignUp = (method: 'google' | 'email') => {
  trackEvent('user_signup', { method });
};

export const trackSignIn = (method: 'google' | 'email') => {
  trackEvent('user_signin', { method });
};

export const trackSignOut = () => {
  trackEvent('user_signout');
};

// File Upload Events
export const trackFileUpload = (fileType: string, fileSize: number) => {
  trackEvent('file_upload', {
    file_type: fileType,
    file_size_mb: Math.round(fileSize / (1024 * 1024) * 100) / 100
  });
};

export const trackFileProcessingComplete = (fileType: string, processingTime: number) => {
  trackEvent('file_processing_complete', {
    file_type: fileType,
    processing_time_seconds: Math.round(processingTime / 1000)
  });
};

// AI Generation Events
export const trackAIGeneration = (type: 'notes' | 'flashcards' | 'quiz', topicId: string) => {
  trackEvent('ai_generation', {
    generation_type: type,
    topic_id: topicId
  });
};

export const trackAIGenerationComplete = (type: 'notes' | 'flashcards' | 'quiz', itemCount: number) => {
  trackEvent('ai_generation_complete', {
    generation_type: type,
    item_count: itemCount
  });
};

// Audio Recording Events
export const trackAudioRecordingStart = () => {
  trackEvent('audio_recording_start');
};

export const trackAudioRecordingComplete = (duration: number) => {
  trackEvent('audio_recording_complete', {
    duration_seconds: Math.round(duration)
  });
};

export const trackAudioTranscriptionComplete = (wordCount: number) => {
  trackEvent('audio_transcription_complete', {
    word_count: wordCount
  });
};

// Subscription Events
export const trackSubscriptionUpgrade = (plan: string, billingCycle: 'monthly' | 'yearly') => {
  trackEvent('subscription_upgrade', {
    plan,
    billing_cycle: billingCycle
  });
};

export const trackSubscriptionCancel = (plan: string, reason?: string) => {
  trackEvent('subscription_cancel', {
    plan,
    reason: reason || 'not_specified'
  });
};

export const trackPaywallShown = (feature: string, userPlan: string) => {
  trackEvent('paywall_shown', {
    feature,
    user_plan: userPlan
  });
};

// Study Material Events
export const trackTopicCreated = (type: 'file' | 'audio' | 'manual') => {
  trackEvent('topic_created', { creation_type: type });
};

export const trackTopicViewed = (topicId: string, contentType: string) => {
  trackEvent('topic_viewed', {
    topic_id: topicId,
    content_type: contentType
  });
};

export const trackFlashcardStudy = (topicId: string, cardCount: number, correctAnswers: number) => {
  trackEvent('flashcard_study', {
    topic_id: topicId,
    card_count: cardCount,
    correct_answers: correctAnswers,
    accuracy: Math.round((correctAnswers / cardCount) * 100)
  });
};

export const trackQuizCompleted = (topicId: string, score: number, totalQuestions: number) => {
  trackEvent('quiz_completed', {
    topic_id: topicId,
    score,
    total_questions: totalQuestions,
    percentage: Math.round((score / totalQuestions) * 100)
  });
};

// Settings & Profile Events
export const trackProfileUpdated = () => {
  trackEvent('profile_updated');
};

export const trackSettingsChanged = (settingType: 'notifications' | 'privacy' | 'account') => {
  trackEvent('settings_changed', { setting_type: settingType });
};

export const trackDataExported = (itemCount: number) => {
  trackEvent('data_exported', { item_count: itemCount });
};

// Error Tracking
export const trackError = (errorType: string, errorMessage: string, context?: string) => {
  trackEvent('error_occurred', {
    error_type: errorType,
    error_message: errorMessage.substring(0, 100), // Limit message length
    context: context || 'unknown'
  });
};

// Performance Tracking
export const trackPageLoad = (pageName: string, loadTime: number) => {
  trackEvent('page_load', {
    page_name: pageName,
    load_time_ms: Math.round(loadTime)
  });
};

export const trackFeatureUsage = (featureName: string, userPlan: string) => {
  trackEvent('feature_usage', {
    feature_name: featureName,
    user_plan: userPlan
  });
};

// Business Metrics
export const trackUserRetention = (daysSinceSignup: number, sessionsCount: number) => {
  trackEvent('user_retention', {
    days_since_signup: daysSinceSignup,
    sessions_count: sessionsCount
  });
};

export const trackConversionFunnel = (step: 'landing' | 'signup' | 'first_upload' | 'first_generation' | 'upgrade') => {
  trackEvent('conversion_funnel', { step });
};

// Website Events
export const trackLandingPageCTA = (ctaType: 'get_started' | 'pricing' | 'demo') => {
  trackEvent('landing_page_cta', { cta_type: ctaType });
};

export const trackPricingPageView = (source: 'header' | 'paywall' | 'direct') => {
  trackEvent('pricing_page_view', { source });
};

// Utility function to track custom events
export const trackCustomEvent = (eventName: string, properties?: Record<string, any>) => {
  trackEvent(eventName, properties);
};

// Initialize analytics tracking
export const initializeAnalytics = () => {
  if (!ANALYTICS_ENABLED) {
    console.log('📊 Analytics disabled');
    return;
  }

  // Track initial page load
  const startTime = performance.now();

  window.addEventListener('load', () => {
    const loadTime = performance.now() - startTime;
    trackPageLoad(window.location.pathname, loadTime);
  });

  // Track conversion funnel step
  if (window.location.pathname === '/') {
    trackConversionFunnel('landing');
  }

  console.log('📊 Analytics initialized for EZMind AI');
};
