/**
 * Test utilities for Settings page functionality
 * Use these functions in browser console to test features
 */

// Test profile updates
export const testProfileUpdate = () => {
  console.log('🧪 Testing Profile Update...');
  
  // Simulate user profile update
  const userEmail = localStorage.getItem('user-email') || '<EMAIL>';
  const testProfile = {
    displayName: 'Test User Updated',
    email: userEmail,
    createdAt: new Date().toISOString(),
    lastUpdated: new Date().toISOString()
  };
  
  localStorage.setItem(`profile-${userEmail}`, JSON.stringify(testProfile));
  console.log('✅ Profile updated:', testProfile);
  
  // Verify it persists
  const stored = localStorage.getItem(`profile-${userEmail}`);
  console.log('✅ Profile persisted:', JSON.parse(stored || '{}'));
};

// Test subscription cancellation
export const testSubscriptionCancellation = () => {
  console.log('🧪 Testing Subscription Cancellation...');
  
  const userEmail = localStorage.getItem('user-email') || '<EMAIL>';
  const cancelledSubscription = {
    plan: 'basic',
    status: 'active',
    cancelAtPeriodEnd: true,
    cancelledAt: new Date().toISOString(),
    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    stripeCustomerId: 'cus_test_123',
    stripeSubscriptionId: 'sub_test_123'
  };
  
  localStorage.setItem(`subscription-${userEmail}`, JSON.stringify(cancelledSubscription));
  console.log('✅ Subscription cancelled:', cancelledSubscription);
};

// Test notification settings
export const testNotificationSettings = () => {
  console.log('🧪 Testing Notification Settings...');
  
  const userEmail = localStorage.getItem('user-email') || '<EMAIL>';
  const notificationSettings = {
    emailNotifications: false,
    pushNotifications: true,
    weeklyDigest: false,
    lastUpdated: new Date().toISOString()
  };
  
  localStorage.setItem(`notifications-${userEmail}`, JSON.stringify(notificationSettings));
  console.log('✅ Notification settings saved:', notificationSettings);
};

// Test data export
export const testDataExport = () => {
  console.log('🧪 Testing Data Export...');
  
  const userEmail = localStorage.getItem('user-email') || '<EMAIL>';
  
  // Create some test data
  const testTopic = {
    id: 'test-topic-1',
    title: 'Test Topic',
    content: 'This is test content',
    createdAt: new Date().toISOString(),
    type: 'text'
  };
  
  localStorage.setItem(`topic-${userEmail}-test1`, JSON.stringify(testTopic));
  
  // Simulate export
  const topics = Object.keys(localStorage)
    .filter(key => key.startsWith(`topic-${userEmail}`))
    .map(key => JSON.parse(localStorage.getItem(key) || '{}'));
  
  console.log('✅ Topics found for export:', topics.length);
  console.log('✅ Sample topic:', topics[0]);
};

// Test account deletion
export const testAccountDeletion = () => {
  console.log('🧪 Testing Account Deletion...');
  
  const userEmail = localStorage.getItem('user-email') || '<EMAIL>';
  const keysToDelete = Object.keys(localStorage).filter(key => 
    key.includes(userEmail) || key.includes('user-') || key.includes('topic-')
  );
  
  console.log('✅ Keys that would be deleted:', keysToDelete);
  console.log('⚠️ Not actually deleting for safety');
};

// Test form validation
export const testFormValidation = () => {
  console.log('🧪 Testing Form Validation...');
  
  const testCases = [
    { displayName: '', email: '<EMAIL>', shouldPass: false, reason: 'Empty display name' },
    { displayName: 'A', email: '<EMAIL>', shouldPass: false, reason: 'Display name too short' },
    { displayName: 'Valid Name', email: 'invalid-email', shouldPass: false, reason: 'Invalid email' },
    { displayName: 'Valid Name', email: '<EMAIL>', shouldPass: true, reason: 'Valid input' },
  ];
  
  testCases.forEach((testCase, index) => {
    const displayNameValid = testCase.displayName.trim().length >= 2 && testCase.displayName.trim().length <= 50;
    const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(testCase.email);
    const isValid = displayNameValid && emailValid;
    
    console.log(`Test ${index + 1}: ${testCase.reason}`);
    console.log(`Expected: ${testCase.shouldPass}, Actual: ${isValid}, ${isValid === testCase.shouldPass ? '✅ PASS' : '❌ FAIL'}`);
  });
};

// Test usage statistics
export const testUsageStatistics = () => {
  console.log('🧪 Testing Usage Statistics...');
  
  const userEmail = localStorage.getItem('user-email') || '<EMAIL>';
  const testUsage = {
    fileUploads: 2,
    aiQuestions: 3,
    audioTranscriptions: 1,
    libraryStorage: 1024 * 1024 // 1MB
  };
  
  localStorage.setItem(`usage-${userEmail}`, JSON.stringify(testUsage));
  console.log('✅ Usage statistics set:', testUsage);
  
  // Test limits
  const freeLimits = { fileUploads: 3, aiQuestions: 5, audioTranscriptions: 1 };
  Object.entries(testUsage).forEach(([key, value]) => {
    if (freeLimits[key as keyof typeof freeLimits]) {
      const limit = freeLimits[key as keyof typeof freeLimits];
      const percentage = Math.min((value / limit) * 100, 100);
      console.log(`${key}: ${value}/${limit} (${percentage.toFixed(1)}%)`);
    }
  });
};

// Run all tests
export const runAllTests = () => {
  console.log('🚀 Running all Settings page tests...\n');
  
  testProfileUpdate();
  console.log('');
  
  testSubscriptionCancellation();
  console.log('');
  
  testNotificationSettings();
  console.log('');
  
  testDataExport();
  console.log('');
  
  testAccountDeletion();
  console.log('');
  
  testFormValidation();
  console.log('');
  
  testUsageStatistics();
  console.log('');
  
  console.log('✅ All tests completed! Check the Settings page to see the results.');
  console.log('💡 Refresh the page to see updated data in the UI.');
};

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testSettings = {
    testProfileUpdate,
    testSubscriptionCancellation,
    testNotificationSettings,
    testDataExport,
    testAccountDeletion,
    testFormValidation,
    testUsageStatistics,
    runAllTests
  };
  
  console.log('🧪 Settings test utilities loaded! Use window.testSettings.runAllTests() to test all features.');
}
