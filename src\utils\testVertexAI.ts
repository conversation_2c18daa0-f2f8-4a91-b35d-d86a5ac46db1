// Test utility for Vertex AI integration
import { generateContent } from '@/services/vertexAIService';

export const testVertexAI = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing Vertex AI connection...');
    
    const testPrompt = "Say 'Hello from Vertex AI!' and explain what you are in one sentence.";
    const response = await generateContent(testPrompt);
    
    console.log('✅ Vertex AI test successful!');
    console.log('📝 Response:', response);
    
    return true;
  } catch (error) {
    console.error('❌ Vertex AI test failed:', error);
    return false;
  }
};

export const testVertexAIFeatures = async () => {
  console.log('🧪 Testing Vertex AI features...');
  
  try {
    // Test basic content generation
    const basicTest = await generateContent("What is 2+2?");
    console.log('✅ Basic generation test passed:', basicTest.substring(0, 50));
    
    // Test note generation
    const { generateNotes } = await import('@/services/vertexAIService');
    const notesTest = await generateNotes("Photosynthesis", "high school");
    console.log('✅ Notes generation test passed:', notesTest.substring(0, 50));
    
    // Test quiz generation
    const { generateQuiz } = await import('@/services/vertexAIService');
    const quizTest = await generateQuiz("Basic Math", "easy", 2);
    console.log('✅ Quiz generation test passed:', quizTest.substring(0, 50));
    
    console.log('🎉 All Vertex AI features working correctly!');
    return true;
  } catch (error) {
    console.error('❌ Vertex AI feature test failed:', error);
    return false;
  }
};

// Auto-run test in development
if (process.env.NODE_ENV === 'development') {
  // Uncomment to test on app load
  // testVertexAI();
}
